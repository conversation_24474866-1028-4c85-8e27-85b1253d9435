#!/usr/bin/env node

/**
 * Migration Health Check
 * 
 * Monitors the health of the journey architecture migration.
 * Provides dashboard-style insights into migration completeness and issues.
 */

import * as fs from 'fs';
import * as path from 'path';
import { execSync } from 'child_process';

interface MigrationHealth {
  journeyCompleteness: Record<string, number>;
  importErrors: string[];
  missingFiles: string[];
  testCoverage: Record<string, number>;
  performanceMetrics: {
    buildTime: number;
    bundleSize: number;
    typeCheckTime: number;
  };
  codeQuality: {
    lintErrors: number;
    lintWarnings: number;
    duplicateCode: number;
  };
}

interface JourneyHealth {
  name: string;
  filesExpected: number;
  filesFound: number;
  completeness: number;
  criticalFiles: string[];
  missingCriticalFiles: string[];
  indexFileExists: boolean;
  hasTests: boolean;
}

class MigrationHealthChecker {
  private readonly journeyPaths = {
    onboarding: 'src/journeys/onboarding',
    daily: 'src/journeys/daily',
    activities: 'src/journeys/activities',
    memories: 'src/journeys/memories',
    planning: 'src/journeys/planning',
    progress: 'src/journeys/progress'
  };

  private readonly criticalFiles = {
    onboarding: ['useAuth.ts', 'useUserProfile.ts', 'useCouplePairing.ts', 'index.ts'],
    daily: ['useDailyQuestions.ts', 'useStreakData.ts', 'index.ts'],
    activities: ['useDateNightIdeasSupabase.ts', 'useMatchGame.ts', 'index.ts'],
    memories: ['useTimeline.ts', 'useOriginStoryData.ts', 'index.ts'],
    planning: ['useUserPreferences.ts', 'index.ts'],
    progress: ['usePointsSystemSupabase.ts', 'useUserEvents.ts', 'index.ts']
  };

  /**
   * Run complete health check
   */
  async runHealthCheck(): Promise<MigrationHealth> {
    console.log('🏥 Running migration health check...\n');

    const journeyCompleteness = await this.checkJourneyCompleteness();
    const importErrors = await this.checkImportErrors();
    const missingFiles = await this.checkMissingFiles();
    const testCoverage = await this.checkTestCoverage();
    const performanceMetrics = await this.checkPerformanceMetrics();
    const codeQuality = await this.checkCodeQuality();

    const health: MigrationHealth = {
      journeyCompleteness,
      importErrors,
      missingFiles,
      testCoverage,
      performanceMetrics,
      codeQuality
    };

    this.displayHealthReport(health);
    return health;
  }

  /**
   * Check completeness of each journey
   */
  private async checkJourneyCompleteness(): Promise<Record<string, number>> {
    const completeness: Record<string, number> = {};

    for (const [journeyName, journeyPath] of Object.entries(this.journeyPaths)) {
      const health = await this.checkJourneyHealth(journeyName, journeyPath);
      completeness[journeyName] = health.completeness;
    }

    return completeness;
  }

  /**
   * Check health of individual journey
   */
  private async checkJourneyHealth(journeyName: string, journeyPath: string): Promise<JourneyHealth> {
    const criticalFiles = this.criticalFiles[journeyName as keyof typeof this.criticalFiles] || [];
    
    // Check if journey directory exists
    if (!fs.existsSync(journeyPath)) {
      return {
        name: journeyName,
        filesExpected: criticalFiles.length,
        filesFound: 0,
        completeness: 0,
        criticalFiles,
        missingCriticalFiles: criticalFiles,
        indexFileExists: false,
        hasTests: false
      };
    }

    // Count files in journey
    const files = fs.readdirSync(journeyPath);
    const tsFiles = files.filter(f => f.endsWith('.ts') || f.endsWith('.tsx'));
    
    // Check critical files
    const missingCriticalFiles = criticalFiles.filter(file => 
      !fs.existsSync(path.join(journeyPath, file))
    );

    // Check index file
    const indexFileExists = fs.existsSync(path.join(journeyPath, 'index.ts'));

    // Check tests
    const testPath = `__tests__/journeys/${journeyName}`;
    const hasTests = fs.existsSync(testPath);

    const completeness = ((criticalFiles.length - missingCriticalFiles.length) / criticalFiles.length) * 100;

    return {
      name: journeyName,
      filesExpected: criticalFiles.length,
      filesFound: tsFiles.length,
      completeness,
      criticalFiles,
      missingCriticalFiles,
      indexFileExists,
      hasTests
    };
  }

  /**
   * Check for import errors (simplified - will work once TS compilation is fixed)
   */
  private async checkImportErrors(): Promise<string[]> {
    const errors: string[] = [];
    
    try {
      // This will fail until imports are fixed, but structure is ready
      execSync('npx tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
    } catch (error: any) {
      const output = error.stdout?.toString() || error.stderr?.toString() || '';
      const importErrors = output
        .split('\n')
        .filter(line => line.includes('Cannot find module') || line.includes('Module not found'))
        .slice(0, 10); // Limit to first 10 errors
      
      errors.push(...importErrors);
    }

    return errors;
  }

  /**
   * Check for missing critical files
   */
  private async checkMissingFiles(): Promise<string[]> {
    const missing: string[] = [];

    for (const [journeyName, journeyPath] of Object.entries(this.journeyPaths)) {
      const criticalFiles = this.criticalFiles[journeyName as keyof typeof this.criticalFiles] || [];
      
      for (const file of criticalFiles) {
        const filePath = path.join(journeyPath, file);
        if (!fs.existsSync(filePath)) {
          missing.push(`${journeyName}/${file}`);
        }
      }
    }

    return missing;
  }

  /**
   * Check test coverage (placeholder - will work once tests run)
   */
  private async checkTestCoverage(): Promise<Record<string, number>> {
    const coverage: Record<string, number> = {};

    for (const journeyName of Object.keys(this.journeyPaths)) {
      const testPath = `__tests__/journeys/${journeyName}`;
      const hasTests = fs.existsSync(testPath);
      
      // Placeholder coverage calculation
      coverage[journeyName] = hasTests ? 50 : 0; // Will be real coverage once tests run
    }

    return coverage;
  }

  /**
   * Check performance metrics (placeholder)
   */
  private async checkPerformanceMetrics(): Promise<MigrationHealth['performanceMetrics']> {
    return {
      buildTime: 0, // Will measure once build works
      bundleSize: 0, // Will measure once build works
      typeCheckTime: 0 // Will measure once TS compilation works
    };
  }

  /**
   * Check code quality metrics
   */
  private async checkCodeQuality(): Promise<MigrationHealth['codeQuality']> {
    let lintErrors = 0;
    let lintWarnings = 0;
    let duplicateCode = 0;

    try {
      // Run ESLint (if available)
      const lintOutput = execSync('npx eslint src/ --format json', { stdio: 'pipe' }).toString();
      const lintResults = JSON.parse(lintOutput);
      
      lintErrors = lintResults.reduce((sum: number, result: any) => sum + result.errorCount, 0);
      lintWarnings = lintResults.reduce((sum: number, result: any) => sum + result.warningCount, 0);
    } catch (error) {
      // ESLint might not be configured or might fail due to import errors
    }

    // Check for duplicate files (simple check)
    try {
      const duplicateCheck = execSync('find src/ -name "*.ts" -o -name "*.tsx" | xargs basename -a | sort | uniq -d', { stdio: 'pipe' }).toString();
      duplicateCode = duplicateCheck.trim().split('\n').filter(Boolean).length;
    } catch (error) {
      // Command might fail on some systems
    }

    return {
      lintErrors,
      lintWarnings,
      duplicateCode
    };
  }

  /**
   * Display health report
   */
  private displayHealthReport(health: MigrationHealth): void {
    console.log('📊 MIGRATION HEALTH REPORT');
    console.log('=' .repeat(50));

    // Journey Completeness
    console.log('\n🎯 Journey Completeness:');
    for (const [journey, completeness] of Object.entries(health.journeyCompleteness)) {
      const status = completeness >= 100 ? '✅' : completeness >= 75 ? '⚠️' : '❌';
      console.log(`   ${status} ${journey}: ${completeness.toFixed(1)}%`);
    }

    // Missing Files
    if (health.missingFiles.length > 0) {
      console.log('\n❌ Missing Critical Files:');
      health.missingFiles.forEach(file => console.log(`   - ${file}`));
    } else {
      console.log('\n✅ All critical files present');
    }

    // Import Errors
    if (health.importErrors.length > 0) {
      console.log('\n🔧 Import Errors (Top 5):');
      health.importErrors.slice(0, 5).forEach(error => console.log(`   - ${error.trim()}`));
      if (health.importErrors.length > 5) {
        console.log(`   ... and ${health.importErrors.length - 5} more`);
      }
    } else {
      console.log('\n✅ No import errors detected');
    }

    // Test Coverage
    console.log('\n🧪 Test Coverage:');
    for (const [journey, coverage] of Object.entries(health.testCoverage)) {
      const status = coverage >= 80 ? '✅' : coverage >= 50 ? '⚠️' : '❌';
      console.log(`   ${status} ${journey}: ${coverage}%`);
    }

    // Code Quality
    console.log('\n🔍 Code Quality:');
    console.log(`   Lint Errors: ${health.codeQuality.lintErrors}`);
    console.log(`   Lint Warnings: ${health.codeQuality.lintWarnings}`);
    console.log(`   Duplicate Files: ${health.codeQuality.duplicateCode}`);

    // Overall Health Score
    const avgCompleteness = Object.values(health.journeyCompleteness).reduce((sum: any, val: any) => sum + val, 0) / Object.keys(health.journeyCompleteness).length;
    const avgCoverage = Object.values(health.testCoverage).reduce((sum: any, val: any) => sum + val, 0) / Object.keys(health.testCoverage).length;
    const healthScore = (avgCompleteness + avgCoverage) / 2;

    console.log('\n🏥 Overall Health Score:');
    const healthStatus = healthScore >= 80 ? '✅ Excellent' : healthScore >= 60 ? '⚠️ Good' : '❌ Needs Attention';
    console.log(`   ${healthStatus}: ${healthScore.toFixed(1)}%`);

    console.log('\n' + '='.repeat(50));
  }
}

// Run health check if called directly
if (require.main === module) {
  const checker = new MigrationHealthChecker();
  checker.runHealthCheck().catch(console.error);
}

export { MigrationHealthChecker, MigrationHealth };
