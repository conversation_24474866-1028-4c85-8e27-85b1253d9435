/**
 * Match Game Migration Script
 * Helps migrate from hardcoded questions to database-driven implementation
 */

import { matchGameQuestionsService } from '../services/matchGameQuestions.service';
import { matchGameAnswersService } from '../services/matchGameAnswers.service';
import { logger } from '../src/shared/utils/logger';

/**
 * Verify that questions were properly migrated to database
 */
export async function verifyQuestionMigration(): Promise<{
  success: boolean;
  message: string;
  stats?: {
    total_questions: number;
    questions_by_category: Record<string, number>;
    questions_by_difficulty: Record<string, number>;
  };
}> {
  try {
    logger.info('Verifying question migration...');
    
    const stats = await matchGameQuestionsService.getQuestionStats();
    const categories = await matchGameQuestionsService.getAllCategories();
    
    // Verify we have questions in all expected categories
    const expectedCategories = [
      'Food', 'Entertainment', 'Personal', 'Music', 'Travel',
      'Relationship', 'Work', 'Hobbies', 'Family', 'Values'
    ];
    
    const missingCategories = expectedCategories.filter(cat => !categories.includes(cat));
    
    if (missingCategories.length > 0) {
      return {
        success: false,
        message: `Missing categories: ${missingCategories.join(', ')}`,
        stats
      };
    }
    
    // Verify we have questions in all difficulty levels
    const difficulties = ['easy', 'medium', 'hard'];
    const missingDifficulties = difficulties.filter(diff => 
      !stats.questions_by_difficulty[diff] || stats.questions_by_difficulty[diff] === 0
    );
    
    if (missingDifficulties.length > 0) {
      return {
        success: false,
        message: `Missing difficulty levels: ${missingDifficulties.join(', ')}`,
        stats
      };
    }
    
    // Verify minimum question count
    if (stats.total_questions < 400) {
      return {
        success: false,
        message: `Expected at least 400 questions, found ${stats.total_questions}`,
        stats
      };
    }
    
    return {
      success: true,
      message: `Migration verified successfully! Found ${stats.total_questions} questions across ${categories.length} categories.`,
      stats
    };
  } catch (error) {
    logger.error('Error verifying question migration:', error);
    return {
      success: false,
      message: `Verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Test the new service functions
 */
export async function testServiceFunctions(): Promise<{
  success: boolean;
  message: string;
  results: Record<string, boolean>;
}> {
  try {
    logger.info('Testing service functions...');
    
    const results: Record<string, boolean> = {};
    
    // Test getRandomQuestions
    try {
      const randomQuestions = await matchGameQuestionsService.getRandomQuestions({ count: 5 });
      results.getRandomQuestions = randomQuestions.length === 5;
    } catch (error) {
      results.getRandomQuestions = false;
      logger.error('getRandomQuestions test failed:', error);
    }
    
    // Test getBalancedQuestions
    try {
      const balancedQuestions = await matchGameQuestionsService.getBalancedQuestions({ count: 10 });
      results.getBalancedQuestions = balancedQuestions.length === 10;
    } catch (error) {
      results.getBalancedQuestions = false;
      logger.error('getBalancedQuestions test failed:', error);
    }
    
    // Test getQuestionsByCategory
    try {
      const foodQuestions = await matchGameQuestionsService.getQuestionsByCategory('Food', 5);
      results.getQuestionsByCategory = foodQuestions.length === 5 && foodQuestions.every(q => q.category === 'Food');
    } catch (error) {
      results.getQuestionsByCategory = false;
      logger.error('getQuestionsByCategory test failed:', error);
    }
    
    // Test getQuestionsByDifficulty
    try {
      const easyQuestions = await matchGameQuestionsService.getQuestionsByDifficulty('easy', 5);
      results.getQuestionsByDifficulty = easyQuestions.length === 5 && easyQuestions.every(q => q.difficulty === 'easy');
    } catch (error) {
      results.getQuestionsByDifficulty = false;
      logger.error('getQuestionsByDifficulty test failed:', error);
    }
    
    // Test getAllCategories
    try {
      const categories = await matchGameQuestionsService.getAllCategories();
      results.getAllCategories = categories.length > 0 && categories.includes('Food');
    } catch (error) {
      results.getAllCategories = false;
      logger.error('getAllCategories test failed:', error);
    }
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    const success = successCount === totalTests;
    
    return {
      success,
      message: `Service tests completed: ${successCount}/${totalTests} passed`,
      results
    };
  } catch (error) {
    logger.error('Error testing service functions:', error);
    return {
      success: false,
      message: `Service testing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      results: {}
    };
  }
}

/**
 * Clean up old hardcoded data (after migration is complete)
 */
export async function cleanupOldData(): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    logger.info('Cleanup completed - old hardcoded data can be safely removed from utils/matchGameQuestions.ts');
    
    // Note: We don't actually delete the file here as that should be done manually
    // after confirming the migration is working correctly
    
    return {
      success: true,
      message: 'Cleanup instructions provided. Manual removal of utils/matchGameQuestions.ts recommended after verification.'
    };
  } catch (error) {
    logger.error('Error during cleanup:', error);
    return {
      success: false,
      message: `Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Run full migration verification
 */
export async function runMigrationVerification(): Promise<void> {
  logger.info('Starting Match Game Migration Verification...');
  
  // Step 1: Verify question migration
  const verificationResult = await verifyQuestionMigration();
  logger.info(`Question Migration: ${verificationResult.success ? 'PASSED' : 'FAILED'} - ${verificationResult.message}`);
  
  if (verificationResult.stats) {
    logger.info('Migration Stats:', verificationResult.stats);
  }
  
  // Step 2: Test service functions
  const serviceTestResult = await testServiceFunctions();
  logger.info(`Service Tests: ${serviceTestResult.success ? 'PASSED' : 'FAILED'} - ${serviceTestResult.message}`);
  
  Object.entries(serviceTestResult.results).forEach(([test, passed]: any) => {
    logger.info(`  ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  // Step 3: Provide cleanup instructions
  const cleanupResult = await cleanupOldData();
  logger.info(`Cleanup: ${cleanupResult.success ? 'READY' : 'FAILED'} - ${cleanupResult.message}`);
  
  // Summary
  const overallSuccess = verificationResult.success && serviceTestResult.success;
  logger.info(`\nMigration Verification ${overallSuccess ? 'COMPLETED SUCCESSFULLY' : 'FAILED'}`);
  
  if (overallSuccess) {
    logger.info('✅ All systems ready for database-driven match game!');
    logger.info('📝 Next steps:');
    logger.info('   1. Test the app with new services');
    logger.info('   2. Remove utils/matchGameQuestions.ts');
    logger.info('   3. Update any remaining imports');
    logger.info('   4. Deploy to production');
  } else {
    logger.error('❌ Migration verification failed. Please check the errors above.');
  }
}

// Export for use in other scripts
export default {
  verifyQuestionMigration,
  testServiceFunctions,
  cleanupOldData,
  runMigrationVerification
};
