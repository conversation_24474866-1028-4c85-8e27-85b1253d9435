const packageJson = require('./package.json');

export default {
  expo: {
    name: 'Everlasting Us',
    slug: 'everlasting-us',
    version: packageJson.version, // Dynamic from package.json
    orientation: 'portrait',
    splash: {
      backgroundColor: process.env.EXPO_PUBLIC_BRAND_BACKGROUND || '#F8BBD9'
    },
    ios: {
      buildNumber: process.env.EXPO_PUBLIC_BUILD_NUMBER || '1',
    },
    android: {
      versionCode: parseInt(process.env.EXPO_PUBLIC_VERSION_CODE || '1'),
    }
  }
};
