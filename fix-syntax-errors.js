#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix syntax errors in a file
function fixSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Fix the main syntax error: "index: number: any" -> "index: number"
    const beforeContent = content;
    content = content.replace(/index:\s*number:\s*any/g, 'index: number');
    
    if (content !== beforeContent) {
      modified = true;
      console.log(`${filePath}: Fixed syntax errors`);
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed syntax errors in: ${filePath}`);
    }
    
    return modified;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// List of files with syntax errors
const filesWithErrors = [
  'app/week-five.tsx',
  'app/week-six.tsx', 
  'app/week-two.tsx',
  'app/week-three.tsx',
  'app/week-ten.tsx',
  'app/week-eleven.tsx',
  'app/week-nine.tsx',
  'app/our-story.tsx',
  'app/week-eight.tsx',
  'app/scrapbook.tsx',
  'app/week-four.tsx',
  'app/week-seven.tsx',
  'app/week-one.tsx',
  'app/week-twelve.tsx',
  'scripts/migration-health-check.ts'
];

console.log('🔧 Fixing syntax errors...');
console.log(`📁 Found ${filesWithErrors.length} files with syntax errors`);

let totalFixed = 0;

filesWithErrors.forEach(file => {
  const fullPath = path.join('/Users/<USER>/everlasting-us', file);
  if (fs.existsSync(fullPath)) {
    if (fixSyntaxErrors(fullPath)) {
      totalFixed++;
    }
  } else {
    console.log(`⚠️  File not found: ${fullPath}`);
  }
});

console.log(`\n🎉 Fixed syntax errors in ${totalFixed} files!`);
console.log('🚀 Run "npx tsc --noEmit" to verify the fixes');
