# Database Schema Documentation

This document provides a comprehensive overview of the database schema, organized by functional area and schema.

## Table of Contents

- [Authentication Schema (auth)](#authentication-schema-auth)
- [Core Application Schema (public)](#core-application-schema-public)
- [System Schemas](#system-schemas)

---

## Authentication Schema (auth)

The `auth` schema handles user authentication, authorization, and session management.

### User Management

#### `users`
Core user account information and authentication data.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | - | Primary key |
| `email` | varchar | YES | - | User email address |
| `encrypted_password` | varchar | YES | - | Hashed password |
| `email_confirmed_at` | timestamptz | YES | - | Email confirmation timestamp |
| `phone` | text | YES | - | Phone number |
| `phone_confirmed_at` | timestamptz | YES | - | Phone confirmation timestamp |
| `is_super_admin` | boolean | YES | - | Admin privileges flag |
| `is_sso_user` | boolean | NO | false | SSO authentication flag |
| `is_anonymous` | boolean | NO | false | Anonymous user flag |
| `banned_until` | timestamptz | YES | - | User ban expiration |
| `deleted_at` | timestamptz | YES | - | Soft delete timestamp |
| `created_at` | timestamptz | YES | now() | Account creation date |
| `updated_at` | timestamptz | YES | now() | Last update timestamp |

#### `identities`
External identity provider connections (OAuth, SAML, etc.).

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `user_id` | uuid | NO | - | Foreign key to users |
| `provider` | text | NO | - | Identity provider name |
| `provider_id` | text | NO | - | Provider-specific user ID |
| `identity_data` | jsonb | NO | - | Provider user data |
| `email` | text | YES | - | Email from provider |
| `last_sign_in_at` | timestamptz | YES | - | Last sign-in timestamp |

### Session Management

#### `sessions`
Active user sessions and authentication state.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | - | Primary key |
| `user_id` | uuid | NO | - | Foreign key to users |
| `factor_id` | uuid | YES | - | MFA factor reference |
| `aal` | enum | YES | - | Authentication Assurance Level |
| `not_after` | timestamptz | YES | - | Session expiration |
| `refreshed_at` | timestamp | YES | - | Last refresh time |
| `user_agent` | text | YES | - | Client user agent |
| `ip` | inet | YES | - | Client IP address |
| `tag` | text | YES | - | Session tag/label |

#### `refresh_tokens`
Refresh token management for session renewal.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | bigint | NO | nextval() | Primary key |
| `token` | varchar | YES | - | Refresh token value |
| `user_id` | varchar | YES | - | User reference |
| `session_id` | uuid | YES | - | Associated session |
| `revoked` | boolean | YES | - | Token revocation status |
| `parent` | varchar | YES | - | Parent token reference |

### Multi-Factor Authentication

#### `mfa_factors`
Registered MFA methods for users.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | - | Primary key |
| `user_id` | uuid | NO | - | Foreign key to users |
| `friendly_name` | text | YES | - | User-defined name |
| `factor_type` | enum | NO | - | MFA method type |
| `status` | enum | NO | - | Factor status |
| `secret` | text | YES | - | TOTP secret |
| `phone` | text | YES | - | SMS phone number |
| `web_authn_credential` | jsonb | YES | - | WebAuthn credential data |

#### `mfa_challenges`
Active MFA challenge sessions.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | - | Primary key |
| `factor_id` | uuid | NO | - | Foreign key to mfa_factors |
| `verified_at` | timestamptz | YES | - | Challenge completion time |
| `ip_address` | inet | NO | - | Challenge origin IP |
| `otp_code` | text | YES | - | One-time password |
| `web_authn_session_data` | jsonb | YES | - | WebAuthn session data |

### SSO & OAuth

#### `sso_providers`
Single Sign-On provider configurations.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | - | Primary key |
| `resource_id` | text | YES | - | Provider resource identifier |
| `disabled` | boolean | YES | - | Provider status |

#### `oauth_clients`
OAuth client application registrations.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | - | Primary key |
| `client_id` | text | NO | - | OAuth client identifier |
| `client_secret_hash` | text | NO | - | Hashed client secret |
| `client_name` | text | YES | - | Application name |
| `redirect_uris` | text | NO | - | Allowed redirect URIs |
| `grant_types` | text | NO | - | Supported grant types |

---

## Core Application Schema (public)

The `public` schema contains the main application functionality for couple relationship tracking and activities.

### User & Couple Management

#### `couples`
Central relationship entity connecting two partners.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `partner1_user_id` | uuid | NO | - | First partner user ID |
| `partner2_user_id` | uuid | YES | - | Second partner user ID |
| `couple_code` | text | NO | - | Unique pairing code |
| `qr_code_data` | text | YES | - | QR code for sharing |
| `status` | text | NO | 'pending' | Relationship status |
| `expires_at` | timestamptz | NO | now() + 7 days | Code expiration |

#### `profiles`
Couple profile information and preferences.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | - | Primary key (couples.id) |
| `partner1_name` | text | NO | '' | First partner's name |
| `partner2_name` | text | NO | '' | Second partner's name |
| `partner1_icon` | text | NO | '' | First partner's avatar |
| `partner2_icon` | text | NO | '' | Second partner's avatar |
| `partner1_profile_picture` | text | YES | - | Profile photo URL |
| `partner2_profile_picture` | text | YES | - | Profile photo URL |
| `relationship_start_date` | date | YES | - | When relationship began |
| `is_complete` | boolean | NO | false | Profile completion status |

#### `pairing_attempts`
Tracks attempts to join couples using pairing codes.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `user_id` | uuid | NO | - | User attempting to pair |
| `attempted_code` | text | NO | - | Code that was tried |
| `success` | boolean | NO | false | Whether attempt succeeded |

### Content & Activities

#### `daily_questions`
Question bank for daily relationship prompts.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `question_id` | text | NO | - | Unique question identifier |
| `question_text` | text | NO | - | The actual question |
| `category` | text | NO | - | Question category |
| `tone` | text | YES | - | Question tone/style |
| `difficulty` | text | YES | - | Complexity level |
| `is_active` | boolean | YES | true | Question availability |

#### `daily_question_responses`
User answers to daily questions.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `user_id` | uuid | YES | - | Responding user |
| `couple_id` | uuid | YES | - | Associated couple |
| `question_id` | text | YES | - | Question reference |
| `response_text` | text | NO | - | User's answer |
| `question_date` | date | NO | - | Date of question |
| `is_visible_to_partner` | boolean | YES | true | Privacy setting |

#### `match_game_questions`
Questions for the couple matching game.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `question_id` | text | NO | - | Unique identifier |
| `question_text` | text | NO | - | Game question content |
| `category` | text | NO | - | Question category |
| `difficulty` | text | YES | - | Difficulty level |
| `question_type` | text | YES | - | Type of question |

#### `match_game_sessions`
Game session tracking for couples.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `couple_id` | uuid | YES | - | Playing couple |
| `total_questions` | integer | YES | - | Questions in session |
| `correct_matches` | integer | YES | - | Successful matches |
| `completed` | boolean | YES | false | Session completion status |

### Ideas & Suggestions

#### `date_night_ideas_global`
System-provided date night suggestions.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | uuid_generate_v4() | Primary key |
| `slug` | text | YES | - | URL-friendly identifier |
| `title` | text | NO | - | Idea title |
| `description` | text | NO | - | Detailed description |
| `category` | text | YES | - | Activity category |
| `difficulty` | enum | NO | - | Complexity level |
| `estimated_duration` | integer | YES | - | Duration in minutes |
| `cost` | enum | NO | - | Cost level |
| `indoor_outdoor` | enum | NO | - | Location type |
| `emoji` | text | YES | - | Representative emoji |

#### `date_night_ideas_user`
User-created date night ideas.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | uuid_generate_v4() | Primary key |
| `user_id` | uuid | YES | - | Creating user |
| `title` | text | NO | - | User's idea title |
| `description` | text | YES | - | Idea description |
| `category` | text | YES | - | User-defined category |
| `difficulty` | enum | YES | - | Difficulty assessment |
| `estimated_duration` | integer | YES | - | Time estimate |
| `cost` | enum | YES | - | Cost estimate |

#### `meal_ideas_global`
System meal suggestions and recipes.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `title` | text | NO | - | Recipe/meal name |
| `description` | text | YES | - | Meal description |
| `category` | text | NO | - | Meal category |
| `difficulty` | text | YES | 'easy' | Cooking difficulty |
| `prep_time` | integer | YES | - | Prep time in minutes |
| `cook_time` | integer | YES | - | Cook time in minutes |
| `servings` | integer | YES | 2 | Number of servings |
| `ingredients` | text[] | YES | - | Ingredient list |
| `instructions` | text[] | YES | - | Cooking steps |
| `tags` | text[] | YES | - | Recipe tags |
| `link` | text | YES | - | External recipe URL |

### Timeline & Memories

#### `timeline_events`
Major relationship milestones and events.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `couple_id` | uuid | NO | - | Associated couple |
| `event_type` | text | NO | - | Type of event |
| `title` | text | NO | - | Event title |
| `description` | text | YES | - | Event description |
| `event_date` | date | NO | - | When event occurred |
| `metadata` | jsonb | YES | {} | Additional event data |
| `source_type` | text | NO | - | How event was created |
| `source_id` | text | YES | - | Source reference |
| `created_by` | uuid | YES | - | User who added event |
| `is_visible` | boolean | YES | true | Visibility setting |
| `is_featured` | boolean | YES | false | Featured event flag |

#### `timeline_photos`
Photos associated with timeline events.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `timeline_event_id` | uuid | NO | - | Associated event |
| `photo_url` | text | NO | - | Photo file URL |
| `thumbnail_url` | text | YES | - | Thumbnail version |
| `caption` | text | YES | - | Photo caption |
| `file_size` | integer | YES | - | File size in bytes |
| `mime_type` | text | YES | - | File MIME type |
| `width` | integer | YES | - | Image width |
| `height` | integer | YES | - | Image height |
| `display_order` | integer | YES | 0 | Photo ordering |
| `uploaded_by` | uuid | YES | - | Uploading user |

### Milestones & Progress

#### `milestone_templates`
Predefined milestone types and their structure.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `milestone_key` | text | NO | - | Unique identifier |
| `category` | text | NO | - | Milestone category |
| `title` | text | NO | - | Display title |
| `description` | text | YES | - | Milestone description |
| `field_schema` | jsonb | NO | - | Required data fields |
| `ui_config` | jsonb | YES | {} | UI configuration |
| `display_order` | integer | YES | 0 | Ordering for display |
| `is_active` | boolean | YES | true | Template availability |
| `is_core_milestone` | boolean | YES | false | Core milestone flag |

#### `couple_milestones`
Couple-specific milestone instances.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `couple_id` | uuid | NO | - | Associated couple |
| `milestone_template_id` | uuid | NO | - | Template reference |
| `milestone_data` | jsonb | NO | {} | Milestone-specific data |
| `is_completed` | boolean | YES | false | Completion status |
| `completion_date` | date | YES | - | When completed |
| `completed_by` | uuid | YES | - | Completing user |
| `timeline_event_id` | uuid | YES | - | Associated timeline event |

### Stories & Documentation

#### `origin_story`
Couple's relationship origin story and key memories.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `user_id` | uuid | NO | - | Story author |
| `couple_id` | uuid | YES | - | Associated couple |
| `data` | jsonb | NO | {} | Story content data |
| `first_meeting_photos` | jsonb | YES | [] | First meeting photos |
| `knew_loved_photos` | jsonb | YES | [] | "Knew I loved you" photos |
| `first_kiss_photos` | jsonb | YES | [] | First kiss photos |
| `inside_jokes_photos` | jsonb | YES | [] | Inside jokes photos |
| `most_romantic_photos` | jsonb | YES | [] | Most romantic photos |
| `biggest_challenge_photos` | jsonb | YES | [] | Challenge photos |
| `best_memories_photos` | jsonb | YES | [] | Best memory photos |

#### `couple_stories`
Ongoing couple story documentation.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `couple_id` | uuid | NO | - | Associated couple |
| `story_data` | jsonb | NO | {} | Story content |
| `completed_sections` | text[] | NO | {} | Finished sections |
| `last_updated_by` | uuid | YES | - | Last editor |

#### `scrapbook`
User scrapbook entries and photos.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `user_id` | uuid | NO | - | Scrapbook owner |
| `entries` | jsonb | NO | [] | Scrapbook entries |
| `photos` | jsonb | YES | [] | Associated photos |

### User Engagement

#### `points_system`
Gamification and achievement tracking.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `user_id` | uuid | NO | - | Associated user |
| `total_points` | integer | NO | 0 | Accumulated points |
| `level` | integer | NO | 1 | Current user level |
| `achievements` | jsonb | NO | [] | Unlocked achievements |
| `last_activity` | timestamptz | YES | now() | Last point activity |

#### `user_events`
User activity and engagement tracking.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `user_id` | uuid | YES | - | Acting user |
| `couple_id` | uuid | YES | - | Associated couple |
| `event_name` | text | YES | - | Event identifier |
| `event_category` | text | YES | - | Event category |
| `event_type` | text | YES | - | Event type |
| `metadata` | jsonb | NO | {} | Event-specific data |
| `streak_eligible` | boolean | YES | true | Counts for streaks |
| `points_awarded` | integer | YES | 0 | Points from event |

#### `favorites`
User favorited items across the application.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | uuid | NO | gen_random_uuid() | Primary key |
| `user_id` | uuid | NO | - | User who favorited |
| `item_id` | text | NO | - | Favorited item ID |
| `type` | text | NO | - | Type of favorited item |
| `metadata` | jsonb | YES | {} | Additional favorite data |

### System Management

#### `error_logs`
Application error tracking and debugging.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `id` | text | NO | - | Primary key |
| `user_id` | uuid | YES | - | User experiencing error |
| `session_id` | text | NO | - | Session identifier |
| `error_message` | text | NO | - | Error description |
| `stack_trace` | text | YES | - | Technical stack trace |
| `component` | text | YES | - | Component where error occurred |
| `action` | text | YES | - | Action being performed |
| `severity` | text | YES | 'medium' | Error severity level |
| `metadata` | jsonb | YES | - | Additional error context |
| `user_agent` | text | YES | - | Client user agent |
| `url` | text | YES | - | URL where error occurred |

#### `onboarding_progress`
User onboarding completion tracking.

| Column | Type | Nullable | Default | Description |
|--------|------|----------|---------|-------------|
| `user_id` | uuid | YES | - | User progressing through onboarding |
| `started` | boolean | YES | - | Onboarding started |
| `intro_viewed` | boolean | YES | - | Introduction completed |
| `partner_done` | boolean | YES | - | Partner setup finished |
| `partner_invited` | boolean | YES | - | Partner invitation sent |
| `ritual_done` | boolean | YES | - | Daily ritual configured |
| `journal_done` | boolean | YES | - | Journal setup completed |
| `completed` | boolean | YES | - | Full onboarding finished |
| `last_event_at` | timestamptz | YES | - | Last onboarding activity |

---

## System Schemas

### Extensions Schema
- **`pg_stat_statements`**: PostgreSQL query performance statistics
- **`pg_stat_statements_info`**: Query statistics metadata

### Realtime Schema
- **`messages`**: Real-time messaging system
- **`subscription`**: Real-time subscription management
- **`schema_migrations`**: Realtime schema version tracking

### Storage Schema
- **`buckets`**: File storage bucket configuration
- **`objects`**: Stored file metadata and references
- **`migrations`**: Storage system migrations
- **`s3_multipart_uploads`**: Large file upload management

### Vault Schema
- **`secrets`**: Encrypted application secrets
- **`decrypted_secrets`**: Decrypted secret access view

### Migration Tracking
- **`supabase_migrations.schema_migrations`**: Database migration history
- **`supabase_migrations.seed_files`**: Database seed file tracking

---

## Key Relationships

### Core Relationships
- Users → Couples (many-to-many through partner1/partner2)
- Couples → Profiles (one-to-one)
- Couples → Timeline Events (one-to-many)
- Timeline Events → Photos (one-to-many)

### Content Relationships
- Users → Daily Question Responses (one-to-many)
- Couples → Match Game Sessions (one-to-many)
- Users → Date Night Ideas (one-to-many)
- Users → Meal Ideas (one-to-many)

### Progress Tracking
- Couples → Milestones (one-to-many)
- Milestone Templates → Couple Milestones (one-to-many)
- Users → Points System (one-to-one)
- Users → User Events (one-to-many)

This schema supports a comprehensive couple relationship application with authentication, content management, progress tracking, and rich media support.