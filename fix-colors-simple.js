#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix colors imports in a file
function fixColorsImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern to match colors imports - fix the common incorrect paths
    const patterns = [
      // Fix ../src/utils/colors -> ../src/shared/utils/colors
      { from: /from\s+['"]\.\.\/src\/utils\/colors['"]/g, to: "from '../src/shared/utils/colors'" },
      // Fix ../../utils/colors -> ../../shared/utils/colors (for files in src/journeys/*)
      { from: /from\s+['"]\.\.\/\.\.\/utils\/colors['"]/g, to: "from '../../shared/utils/colors'" },
      // Fix ../utils/colors -> ../shared/utils/colors (for files in src/shared/*)
      { from: /from\s+['"]\.\.\/utils\/colors['"]/g, to: "from '../shared/utils/colors'" }
    ];
    
    patterns.forEach(pattern => {
      if (pattern.from.test(content)) {
        content = content.replace(pattern.from, pattern.to);
        modified = true;
        console.log(`${filePath}: Fixed colors import`);
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed colors imports in: ${filePath}`);
    }
    
    return modified;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Files that need colors import fixes (from the TypeScript errors)
const filesToFix = [
  'app/app-settings.tsx',
  'app/auth.tsx',
  'app/couple-profile.tsx',
  'app/daily-questions-achievements.tsx',
  'app/join-couple.tsx',
  'app/landing.tsx',
  'app/login.tsx',
  'app/modules.tsx',
  'app/notifications.tsx',
  'app/onboarding.tsx',
  'app/our-story.tsx',
  'app/scrapbook.tsx',
  'app/settings.tsx',
  'app/test-pages/test-date-night-favorites.tsx',
  'app/test-pages/test-milestones.tsx',
  'app/welcome.tsx',
  'app/week-eight.tsx',
  'app/week-eleven.tsx',
  'app/week-five.tsx',
  'app/week-four.tsx',
  'app/week-nine.tsx',
  'app/week-one.tsx',
  'app/week-seven.tsx',
  'app/week-six.tsx',
  'app/week-ten.tsx',
  'app/week-three.tsx',
  'app/week-twelve.tsx',
  'app/week-two.tsx'
];

let totalFixed = 0;

filesToFix.forEach(file => {
  const fullPath = path.join('/Users/<USER>/everlasting-us', file);
  if (fs.existsSync(fullPath)) {
    if (fixColorsImports(fullPath)) {
      totalFixed++;
    }
  } else {
    console.log(`⚠️  File not found: ${fullPath}`);
  }
});

console.log(`\n🎉 Fixed colors imports in ${totalFixed} files!`);
