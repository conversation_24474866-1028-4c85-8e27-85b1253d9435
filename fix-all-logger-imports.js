#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to calculate the correct relative path to src/shared/utils/logger
function getCorrectLoggerPath(fromFile) {
  const projectRoot = '/Users/<USER>/everlasting-us';
  const loggerPath = path.join(projectRoot, 'src/shared/utils/logger');
  
  // Get the directory of the source file
  const fromDir = path.dirname(fromFile);
  
  // Calculate relative path
  const relativePath = path.relative(fromDir, loggerPath);
  
  // Normalize the path (convert backslashes to forward slashes and remove .ts extension)
  return relativePath.replace(/\\/g, '/').replace(/\.ts$/, '');
}

// Function to fix logger imports in a file
function fixLoggerImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern to match logger imports
    const loggerImportPattern = /from\s+['"]([^'"]*utils\/logger)['"]/g;
    
    content = content.replace(loggerImportPattern, (match, importPath) => {
      const correctPath = getCorrectLoggerPath(filePath);
      const newImport = `from '${correctPath}'`;
      console.log(`${filePath}: ${match} -> ${newImport}`);
      modified = true;
      return newImport;
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed logger imports in: ${filePath}`);
    }
    
    return modified;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Get all files that contain logger imports
function getAllFilesWithLoggerImports() {
  const { execSync } = require('child_process');
  try {
    const result = execSync(`grep -r "from ['\"][^'\"]*utils/logger['\"]" /Users/<USER>/everlasting-us/src /Users/<USER>/everlasting-us/app /Users/<USER>/everlasting-us/scripts --include="*.ts" --include="*.tsx" -l`, { encoding: 'utf8' });
    return result.trim().split('\n').filter(file => file.length > 0);
  } catch (error) {
    console.error('Error finding files:', error.message);
    return [];
  }
}

// Main execution
console.log('🔍 Finding all files with logger imports...');
const allFiles = getAllFilesWithLoggerImports();

console.log(`📁 Found ${allFiles.length} files with logger imports`);

let totalFixed = 0;

allFiles.forEach(file => {
  if (fs.existsSync(file)) {
    if (fixLoggerImports(file)) {
      totalFixed++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

console.log(`\n🎉 Fixed logger imports in ${totalFixed} files!`);
