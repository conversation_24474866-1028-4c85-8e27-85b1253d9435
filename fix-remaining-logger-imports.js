#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to calculate the correct relative path to src/shared/utils/logger
function getCorrectLoggerPath(fromFile) {
  const projectRoot = '/Users/<USER>/everlasting-us';
  const loggerPath = path.join(projectRoot, 'src/shared/utils/logger');
  
  // Get the directory of the source file
  const fromDir = path.dirname(fromFile);
  
  // Calculate relative path
  const relativePath = path.relative(fromDir, loggerPath);
  
  // Normalize the path (convert backslashes to forward slashes and remove .ts extension)
  return relativePath.replace(/\\/g, '/').replace(/\.ts$/, '');
}

// Function to fix logger imports in a file
function fixLoggerImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern to match logger imports - more flexible
    const loggerImportPattern = /from\s+['"]([^'"]*utils\/logger)['"]/g;
    
    content = content.replace(loggerImportPattern, (match, importPath) => {
      const correctPath = getCorrectLoggerPath(filePath);
      const newImport = `from '${correctPath}'`;
      console.log(`${filePath}: ${match} -> ${newImport}`);
      modified = true;
      return newImport;
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed logger imports in: ${filePath}`);
    }
    
    return modified;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Remaining files to fix
const remainingFiles = [
  'src/shared/types/auth.ts',
  'src/shared/components/layout/LoadingStateManager.tsx',
  'src/shared/components/layout/ErrorHandler.tsx',
  'src/shared/components/layout/UnifiedErrorBoundary.tsx',
  'src/shared/components/features/MilestoneForm.tsx',
  'src/shared/components/screens/PerformanceDashboard.tsx',
  'src/shared/hooks/useOptimizedData.ts',
  'src/shared/hooks/userPreferencesService.ts',
  'src/shared/hooks/useErrorReporting.ts',
  'src/shared/hooks/useHeartToggle.ts',
  'src/shared/services/middleware/securityMiddleware.ts',
  'src/shared/services/contexts/SettingsContext.tsx',
  'src/shared/services/performanceMonitor.ts',
  'src/shared/services/features/pointsSystemService.ts',
  'src/shared/services/storage/hybridStorageService.ts',
  'src/shared/services/storage/cacheManager.ts',
  'src/shared/services/supabase/client.web.ts',
  'src/shared/services/supabase/client.ts',
  'src/shared/services/realtime/useCoupleRealtime.ts',
  'src/shared/services/system/errorReportingService.ts'
];

let totalFixed = 0;

remainingFiles.forEach(file => {
  const fullPath = path.join('/Users/<USER>/everlasting-us', file);
  if (fs.existsSync(fullPath)) {
    if (fixLoggerImports(fullPath)) {
      totalFixed++;
    }
  } else {
    console.log(`⚠️  File not found: ${fullPath}`);
  }
});

console.log(`\n🎉 Fixed logger imports in ${totalFixed} files!`);
