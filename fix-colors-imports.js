#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix colors imports in a file
function fixColorsImports(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Pattern to match colors imports - fix the common incorrect paths
    const patterns = [
      // Fix ../src/utils/colors -> ../src/shared/utils/colors
      { from: /from\s+['"]\.\.\/src\/utils\/colors['"]/g, to: "from '../src/shared/utils/colors'" },
      // Fix ../../utils/colors -> ../../shared/utils/colors (for files in src/journeys/*)
      { from: /from\s+['"]\.\.\/\.\.\/utils\/colors['"]/g, to: "from '../../shared/utils/colors'" },
      // Fix ../utils/colors -> ../shared/utils/colors (for files in src/shared/*)
      { from: /from\s+['"]\.\.\/utils\/colors['"]/g, to: "from '../shared/utils/colors'" }
    ];
    
    patterns.forEach(pattern => {
      if (pattern.from.test(content)) {
        content = content.replace(pattern.from, pattern.to);
        modified = true;
        console.log(`${filePath}: Fixed colors import`);
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed colors imports in: ${filePath}`);
    }
    
    return modified;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Get all files that contain colors imports
function getAllFilesWithColorsImports() {
  const { execSync } = require('child_process');
  try {
    const result = execSync(`grep -r "from ['\"][^'\"]*utils/colors['\"]" /Users/<USER>/everlasting-us/src /Users/<USER>/everlasting-us/app --include="*.ts" --include="*.tsx" -l`, { encoding: 'utf8' });
    return result.trim().split('\n').filter(file => file.length > 0);
  } catch (error) {
    console.error('Error finding files:', error.message);
    return [];
  }
}

// Main execution
console.log('🔍 Finding all files with colors imports...');
const allFiles = getAllFilesWithColorsImports();

console.log(`📁 Found ${allFiles.length} files with colors imports`);

let totalFixed = 0;

allFiles.forEach(file => {
  if (fs.existsSync(file)) {
    if (fixColorsImports(file)) {
      totalFixed++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

console.log(`\n🎉 Fixed colors imports in ${totalFixed} files!`);
