src/shared/types/auth.ts
src/shared/components/layout/LoadingStateManager.tsx
src/shared/components/layout/ErrorHandler.tsx
src/shared/components/layout/UnifiedErrorBoundary.tsx
src/shared/components/features/MilestoneForm.tsx
src/shared/components/screens/PerformanceDashboard.tsx
src/shared/hooks/useOptimizedData.ts
src/shared/hooks/userPreferencesService.ts
src/shared/hooks/useErrorReporting.ts
src/shared/hooks/useHeartToggle.ts
src/shared/services/middleware/securityMiddleware.ts
src/shared/services/contexts/SettingsContext.tsx
src/shared/services/performanceMonitor.ts
src/shared/services/features/pointsSystemService.ts
src/shared/services/storage/hybridStorageService.ts
src/shared/services/storage/cacheManager.ts
src/shared/services/supabase/client.web.ts
src/shared/services/supabase/client.ts
src/shared/services/realtime/useCoupleRealtime.ts
src/shared/services/system/errorReportingService.ts
src/shared/services/system/subscriptionManager.ts
src/shared/services/system/queryOptimizationService.ts
src/shared/services/system/securityValidationService.ts
src/shared/services/system/ConfigurationManager.ts
src/shared/services/system/ErrorManager.ts
src/shared/services/system/simpleErrorService.ts
src/shared/services/data/dataService.ts
src/journeys/progress/performanceOptimizationService.ts
src/journeys/progress/eventLogger.ts
src/journeys/progress/usePointsSystemSupabase.ts
src/journeys/progress/useUserEvents.ts
src/journeys/planning/useUserPreferences.ts
src/journeys/daily/useWeekElevenData.ts
src/journeys/daily/useGenericWeekData.ts
src/journeys/daily/streakEventService.ts
src/journeys/daily/useWeekSixData.ts
src/journeys/daily/useDailyQuestions.ts
src/journeys/daily/dailyQuestionsNotificationService.ts
src/journeys/daily/useDailyChallenges.ts
src/journeys/daily/useWeekTwelveData.ts
src/journeys/daily/useStreakData.ts
src/journeys/daily/useWeekFiveData.ts
src/journeys/daily/useWeekNineData.ts
src/journeys/daily/useWeekSevenData.ts
src/journeys/daily/useWeekTenData.ts
src/journeys/daily/useWeekTwoData.ts
src/journeys/daily/useWeekEightData.ts
src/journeys/daily/useDailyQuestionsNotifications.ts
src/journeys/daily/useWeekThreeData.ts
src/journeys/daily/useWeekOneData.ts
src/journeys/daily/dailyQuestionsService.ts
src/journeys/activities/useDateNightIdeasSupabase.ts
src/journeys/activities/useMatchGame.ts
src/journeys/activities/dateNightHooks.ts
src/journeys/activities/useWeeklyDateNightIntegration.ts
src/journeys/activities/dateNightIdeasService.ts
src/journeys/activities/useDateNightPool.ts
src/journeys/activities/mealIdeasService.ts
src/journeys/activities/useFavorites.ts
src/journeys/activities/useMealIdeasSupabase.ts
src/journeys/activities/match-game/matchGameAnswers.service.ts
src/journeys/activities/match-game/matchGameQuestions.service.ts
src/journeys/activities/match-game/matchGameResults.service.ts
src/journeys/activities/match-game/matchGameSessions.service.ts
src/journeys/activities/dateNightApi.ts
src/journeys/activities/useDateNightFavorites.ts
src/journeys/activities/favoritesService.ts
src/journeys/memories/imageStorageService.ts
src/journeys/memories/enhancedImageStorageService.ts
src/journeys/memories/useTimeline.ts
src/journeys/memories/useRelationshipMilestones.ts
src/journeys/memories/useMilestones.ts
src/journeys/memories/milestoneService.ts
src/journeys/memories/useMilestoneIntegration.ts
src/journeys/memories/useOriginStoryData.ts
src/journeys/onboarding/AuthScreen.tsx
src/journeys/onboarding/auth/AuthProvider.tsx
src/journeys/onboarding/userPreferencesService.ts
src/journeys/onboarding/useUserProfile.ts
src/journeys/onboarding/useCoupleRealtime.ts
src/journeys/onboarding/couplePairingService.ts
src/journeys/onboarding/useCouplePairing.ts
app/login.tsx
app/our-story.tsx
app/test-pages/test-milestones.tsx
scripts/migrate-match-game.ts
