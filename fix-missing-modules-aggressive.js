#!/usr/bin/env node

/**
 * AGGRESSIVE MODULE RESOLUTION SCRIPT
 * 
 * This script systematically creates ALL missing re-export files
 * to eliminate TS2307 "Cannot find module" errors in bulk.
 */

const fs = require('fs');
const path = require('path');

// Ensure directories exist
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Create re-export file
function createReExport(filePath, exportPath, description) {
  ensureDir(path.dirname(filePath));
  
  const content = `/**
 * ${description}
 * Re-exports from ${exportPath}
 */

export * from '${exportPath}';`;

  fs.writeFileSync(filePath, content);
  console.log(`✅ Created: ${filePath} -> ${exportPath}`);
}

// MASSIVE BULK MODULE CREATION
console.log('🚀 AGGRESSIVE MODULE RESOLUTION - BULK CREATION');

// 1. TYPES RE-EXPORTS (5 errors each)
const typeReExports = [
  // Journey-level types
  ['src/journeys/activities/types.ts', '../../shared/types', 'Legacy types for activities'],
  ['src/journeys/daily/types.ts', '../../shared/types', 'Legacy types for daily'],
  ['src/journeys/memories/types.ts', '../../shared/types', 'Legacy types for memories'],
  ['src/journeys/onboarding/types.ts', '../../shared/types', 'Legacy types for onboarding'],
  ['src/journeys/progress/types.ts', '../../shared/types', 'Legacy types for progress'],
  
  // Component-level types
  ['src/components/types.ts', '../shared/types', 'Legacy types for components'],
  ['src/hooks/types.ts', '../shared/types', 'Legacy types for hooks'],
  ['src/services/types.ts', '../shared/types', 'Legacy types for services'],
  ['src/utils/types.ts', '../shared/types', 'Legacy types for utils'],
];

// 2. USEAUTH RE-EXPORTS (5+4 = 9 errors)
const useAuthReExports = [
  // Hook-level re-exports
  ['src/hooks/useAuth.ts', '../journeys/onboarding/useAuth', 'Legacy useAuth hook'],
  ['src/journeys/activities/hooks/useAuth.ts', '../../onboarding/useAuth', 'Legacy useAuth for activities'],
  ['src/journeys/daily/hooks/useAuth.ts', '../../onboarding/useAuth', 'Legacy useAuth for daily'],
  ['src/journeys/memories/hooks/useAuth.ts', '../../onboarding/useAuth', 'Legacy useAuth for memories'],
  ['src/journeys/progress/hooks/useAuth.ts', '../../onboarding/useAuth', 'Legacy useAuth for progress'],
  
  // Direct re-exports
  ['src/journeys/activities/useAuth.ts', '../onboarding/useAuth', 'Legacy useAuth for activities'],
  ['src/journeys/daily/useAuth.ts', '../onboarding/useAuth', 'Legacy useAuth for daily'],
  ['src/journeys/memories/useAuth.ts', '../onboarding/useAuth', 'Legacy useAuth for memories'],
  ['src/journeys/progress/useAuth.ts', '../onboarding/useAuth', 'Legacy useAuth for progress'],
];

// 3. OTHER HIGH-FREQUENCY HOOKS (2 errors each)
const hookReExports = [
  // useUserProfile
  ['src/journeys/activities/useUserProfile.ts', '../onboarding/useUserProfile', 'Legacy useUserProfile for activities'],
  ['src/journeys/memories/useUserProfile.ts', '../onboarding/useUserProfile', 'Legacy useUserProfile for memories'],
  
  // usePointsSystemSupabase
  ['src/journeys/activities/usePointsSystemSupabase.ts', '../progress/usePointsSystemSupabase', 'Legacy usePointsSystemSupabase for activities'],
  ['src/journeys/memories/usePointsSystemSupabase.ts', '../progress/usePointsSystemSupabase', 'Legacy usePointsSystemSupabase for memories'],
  
  // useWeekFiveData
  ['src/journeys/activities/useWeekFiveData.ts', '../memories/useWeekFiveData', 'Legacy useWeekFiveData for activities'],
  ['src/journeys/progress/useWeekFiveData.ts', '../memories/useWeekFiveData', 'Legacy useWeekFiveData for progress'],
  
  // useCouplePairing
  ['src/journeys/daily/useCouplePairing.ts', '../onboarding/useCouplePairing', 'Legacy useCouplePairing for daily'],
  ['src/journeys/memories/useCouplePairing.ts', '../onboarding/useCouplePairing', 'Legacy useCouplePairing for memories'],
  ['src/journeys/progress/useCouplePairing.ts', '../onboarding/useCouplePairing', 'Legacy useCouplePairing for progress'],
];

// 4. COMPONENT RE-EXPORTS (3 errors)
const componentReExports = [
  ['src/components/shared.ts', '../shared/components', 'Legacy shared components'],
  ['src/journeys/activities/components/shared.ts', '../../../shared/components', 'Legacy shared components for activities'],
  ['src/journeys/progress/components/shared.ts', '../../../shared/components', 'Legacy shared components for progress'],
];

// 5. UTILITY RE-EXPORTS (2 errors each)
const utilityReExports = [
  // Logger
  ['src/journeys/activities/logger.ts', '../../shared/utils/logger', 'Legacy logger for activities'],
  ['src/journeys/progress/logger.ts', '../../shared/utils/logger', 'Legacy logger for progress'],
  
  // Toast
  ['src/journeys/activities/utils/toast.ts', '../../../shared/utils/toast', 'Legacy toast for activities'],
  ['src/journeys/progress/utils/toast.ts', '../../../shared/utils/toast', 'Legacy toast for progress'],
  
  // Theme
  ['src/utils/theme.ts', '../shared/utils/theme', 'Legacy theme utils'],
  ['src/journeys/activities/utils/theme.ts', '../../../shared/utils/theme', 'Legacy theme for activities'],
];

// 6. SPECIALIZED COMPONENTS (2 errors each)
const specializedReExports = [
  // PointsDisplay
  ['src/journeys/activities/PointsDisplay.ts', '../../shared/components/features/PointsDisplay', 'Legacy PointsDisplay for activities'],
  ['src/journeys/progress/PointsDisplay.ts', '../../shared/components/features/PointsDisplay', 'Legacy PointsDisplay for progress'],
  
  // useContentFavorites
  ['src/journeys/activities/useContentFavorites.ts', '../activities/useFavorites', 'Legacy useContentFavorites for activities'],
  ['src/journeys/progress/useContentFavorites.ts', '../activities/useFavorites', 'Legacy useContentFavorites for progress'],
];

// EXECUTE ALL BULK CREATIONS
const allReExports = [
  ...typeReExports,
  ...useAuthReExports,
  ...hookReExports,
  ...componentReExports,
  ...utilityReExports,
  ...specializedReExports
];

console.log(`\n📦 Creating ${allReExports.length} re-export files...`);

allReExports.forEach(([filePath, exportPath, description]) => {
  createReExport(filePath, exportPath, description);
});

console.log(`\n🎉 BULK CREATION COMPLETE!`);
console.log(`✅ Created ${allReExports.length} re-export files`);
console.log(`🎯 Expected to eliminate 50+ TS2307 errors`);
console.log(`\n🚀 Run: npx tsc --noEmit | grep -o "TS[0-9]*" | sort | uniq -c | sort -nr`);
