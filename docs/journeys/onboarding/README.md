# Onboarding Journey

Authentication, couple pairing, and profile setup for new users.

## Journey Steps

1. **Welcome & Introduction** - App overview and account creation
2. **Couple Connection** - Partner invitation and verification
3. **Profile Setup** - Names, relationship date, photos
4. **Feature Introduction** - Preview of daily questions and activities
5. **First Experience** - Complete initial activities together

## Key Components

### `src/journeys/onboarding/useAuth.ts`
Authentication state management and login/logout functionality.

### `src/journeys/onboarding/useUserProfile.ts`
User and couple profile data management.

### `src/journeys/onboarding/useCouplePairing.ts`
Partner connection and couple relationship setup.

### `src/journeys/onboarding/AuthScreen.tsx`
Main authentication and onboarding flow component.

## Database Tables
- `onboarding_progress` - Step completion tracking
- `couples` - Couple relationships
- `profiles` - User profile data

## Usage

```typescript
import {
  useAuth,
  useUserProfile,
  useCouplePairing
} from 'src/journeys/onboarding';

const { user, signIn, signOut } = useAuth();
const { profile, updateProfile } = useUserProfile();
const { pairWithPartner, coupleStatus } = useCouplePairing();
```
