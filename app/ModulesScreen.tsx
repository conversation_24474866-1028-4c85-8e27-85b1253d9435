import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';

import { Lock, CircleCheck as CheckCircle, Clock, Users, Heart, MessageCircle, Calendar, Star, ArrowRight } from 'lucide-react-native';
import { router } from 'expo-router';
import { usePointsSystemSupabase } from '../src/hooks/usePointsSystemSupabase';
import { useAuth } from '../src/hooks/useAuth';
import { colors } from '../src/shared/utils/colors';
import HamburgerMenu from '../src/components/HamburgerMenu';

export default function ModulesScreen() {
  const { isInitialized } = useAuth();
  const { totalPoints, addPoints, data: pointsData } = usePointsSystemSupabase();

  // Don't render until auth is initialized
  if (!isInitialized) {
    return null;
  }

  const modules = [
    {
      week: 1,
      title: 'Getting to Know You',
      description: 'Rediscover each other through fun activities',
      completed: false,
      locked: false,
      activities: ['The Match Game', 'Date Night Plan', 'Chat Prompts', 'Soft Start-Up'],
      color: colors.primary,
      points: 0,
    },
    {
      week: 0,
      title: 'Date Night Ideas',
      description: 'Plan perfect evenings with activities + meals',
      completed: false,
      locked: false,
      activities: ['Date Night Planning', 'Meal Decision-Making', 'Combined Suggestions', 'Evening Planning'],
      color: colors.accent1,
      points: 0,
      isSpecial: true,
    },
    {
      week: 2,
      title: 'Strengths Bingo',
      description: 'Celebrate each other\'s strengths and build positivity',
      completed: false,
      locked: false,
      activities: ['Strengths Bingo', 'Date Night Plan', 'Chat Prompts', '5:1 Ratio'],
      color: colors.secondary,
      points: 0,
    },
    {
      week: 3,
      title: 'Would You Rather?',
      description: 'Explore quirks, pet peeves, and fears playfully',
      completed: false,
      locked: false,
      activities: ['Would You Rather?', 'Alphabet Date Night', 'Chat Prompts', 'Being Curious'],
      color: colors.blue,
      points: 0,
    },
    {
      week: 4,
      title: 'Create a Playlist',
      description: 'Express your story through music and creative connection',
      completed: false,
      locked: false,
      activities: ['Create a Playlist', 'Movie Theme Night', 'Chat Prompts', 'Emotional Regulation'],
      color: colors.green,
      points: 0,
    },
    {
      week: 5,
      title: 'Dream Vacation',
      description: 'Imagine and plan your ideal vacation together',
      completed: false,
      locked: false,
      activities: ['Dream Vacation', 'Date Night Plan', 'Chat Prompts', 'Conflict Style'],
      color: colors.orange,
      points: 0,
    },
    {
      week: 6,
      title: 'Sharing Memories',
      description: 'Share childhood moments and create a memory lane together',
      completed: false,
      locked: false,
      activities: ['Sharing Memories', 'Memory Lane Date', 'Chat Prompts', 'Validation Toolkit'],
      color: colors.red,
      points: 0,
    },
    {
      week: 7,
      title: 'Superhero Duo',
      description: 'Create your superhero identities and discover your combined powers',
      completed: false,
      locked: false,
      activities: ['Superhero Duo Chart', 'Thrift Shop Showdown', 'Chat Prompts', 'Turning Toward'],
      color: colors.purple,
      points: 0,
    },
    {
      week: 8,
      title: 'The Perfect Saturday',
      description: 'Design your ideal day together and explore deeper needs',
      completed: false,
      locked: false,
      activities: ['Perfect Saturday Game', 'Blended Perfect Day', 'Chat Prompts', 'Conflict Mapping'],
      color: colors.cyan,
      points: 0,
    },
    {
      week: 9,
      title: 'Custom Crest',
      description: 'Design your family crest and explore shared values',
      completed: false,
      locked: false,
      activities: ['Create a Crest', 'Live Show Date', 'Chat Prompts', 'Shared Values'],
      color: colors.lime,
      points: 0,
    },
    {
      week: 10,
      title: 'The Dream Bank',
      description: 'Plan your financial dreams and have meaningful money conversations',
      completed: false,
      locked: false,
      activities: ['Dream Bank ($5M)', 'Get Artsy Date', 'Chat Prompts', 'Money Talk'],
      color: colors.orangeRed,
      points: 0,
    },
    {
      week: 11,
      title: 'Love Languages',
      description: 'Discover your love languages and practice meeting each other\'s needs',
      completed: false,
      locked: false,
      activities: ['Love Language Quiz', 'Mini-Olympics', 'Chat Prompts', 'Love Language Practice'],
      color: colors.pink,
      points: 0,
    },
    {
      week: 12,
      title: 'Playful Tales',
      description: 'Create stories together and explore intimate connection',
      completed: false,
      locked: false,
      activities: ['Build-a-Story Game', 'Get Active Date', 'Chat Prompts', 'Sensate Focus'],
      color: colors.indigo,
      points: 0,
    },
  ];

  // Ensure modules array is always defined
  const safeModules = modules || [];


  // Check if activity is completed
  const isActivityCompleted = (moduleId: number, activityName: string) => {
    // Activity completion tracking will be implemented with Supabase integration
    return false;
  };

  const renderActivityIcon = (activity: string) => {
    switch (activity) {
      case 'Match Game':
      case 'Active Listening':
      case 'Create a Playlist':
        return <Users size={16} color="colors.white" />;
      case 'Date Night Plan':
      case 'Alphabet Date Night':
        return <Calendar size={16} color="colors.white" />;
      case 'Chat Prompts':
      case 'Soft Start-Up':
      case '5:1 Ratio':
      case 'Chat Prompts':
      case 'Emotional Regulation':
        return <MessageCircle size={16} color="colors.white" />;
      case 'Movie Theme Night':
        return <Calendar size={16} color="colors.white" />;
      default:
        return <Heart size={16} color="colors.white" />;
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Hamburger Menu */}
      <HamburgerMenu position="top-right" />
      
      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.primary }]}
      >
        <Text style={styles.headerTitle}>Weekly Modules</Text>
        <Text style={styles.headerSubtitle}>Complete activities together to strengthen your relationship</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>


        {safeModules.map((module, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.moduleCard, module.locked && styles.moduleCardLocked]}
            disabled={module.locked}
            onPress={() => {
              if (module.week === 0 && module.isSpecial) {
                router.push('/date-night');
              } else if (module.week === 1) {
                router.push('/week-one');
              } else if (module.week === 2) {
                router.push('/week-two');
              } else if (module.week === 3) {
                router.push('/week-three');
              } else if (module.week === 5) {
                router.push('/week-five' as any);
              } else if (module.week === 6) {
                router.push('/week-six');
              } else if (module.week === 7) {
                router.push('/week-seven');
              } else if (module.week === 8) {
                router.push('/week-eight');
              } else if (module.week === 9) {
                router.push('/week-nine');
              } else if (module.week === 10) {
                router.push('/week-ten');
              } else if (module.week === 11) {
                router.push('/week-eleven');
              } else if (module.week === 12) {
                router.push('/week-twelve');
              }
            }}
          >
            <View style={styles.moduleHeader}>
              <View style={styles.moduleWeek}>
                <Text style={styles.moduleWeekText}>
                  {module.isSpecial ? '✨ Special' : `Week ${module.week}`}
                </Text>
              </View>
              
              <View style={styles.moduleStatus}>
                {module.completed ? (
                  <CheckCircle size={24} color="colors.accent1" />
                ) : module.locked ? (
                  <Lock size={24} color="colors.textTertiary" />
                ) : (
                  <Clock size={24} color="colors.accent2" />
                )}
              </View>
            </View>

            <Text style={[styles.moduleTitle, module.locked && styles.moduleTitleLocked]}>
              {module.title}
            </Text>
            <Text style={[styles.moduleDescription, module.locked && styles.moduleDescriptionLocked]}>
              {module.description}
            </Text>

            {/* Points Display */}
            <View style={styles.pointsContainer}>
              <Star size={16} color={colors.warning} />
              <Text style={styles.pointsText}>{module.points} points earned</Text>
            </View>

            <View style={styles.activitiesContainer}>
              {module.activities?.map((activity, activityIndex) => (
                <TouchableOpacity
                  key={activityIndex}
                  style={[
                    styles.activityChip,
                    isActivityCompleted(module.week, activity) && styles.activityChipCompleted
                  ]}
                  disabled={module.locked}
                  onPress={() => {
                    if (module.locked) return;
                    
                    if (module.week === 1) {
                      if (activity === 'The Match Game') {
                        router.push('/week-one?section=0');
                      } else if (activity === 'Date Night Plan') {
                        router.push('/week-one?section=1');
                      } else if (activity === 'Chat Prompts') {
                        router.push('/week-one?section=2');
                      } else if (activity === 'Soft Start-Up') {
                        router.push('/week-one?section=3');
                      }
                    } else if (module.week === 2) {
                      if (activity === 'Strengths Bingo') {
                        router.push('/week-two?section=0');
                      } else if (activity === 'Date Night Plan') {
                        router.push('/week-two?section=1');
                      } else if (activity === 'Chat Prompts') {
                        router.push('/week-two?section=2');
                      } else if (activity === '5:1 Ratio') {
                        router.push('/week-two?section=3');
                      }
                    } else if (module.week === 3) {
                      if (activity === 'Would You Rather?') {
                        router.push('/week-three?section=0');
                      } else if (activity === 'Alphabet Date Night') {
                        router.push('/week-three?section=1');
                      } else if (activity === 'Chat Prompts') {
                        router.push('/week-three?section=2');
                      } else if (activity === 'Being Curious') {
                        router.push('/week-three?section=3');
                      }
                    } else if (module.week === 5) {
                      if (activity === 'Dream Vacation') {
                        router.push('/week-five?section=0' as any);
                      } else if (activity === 'Date Night Plan') {
                        router.push('/week-five?section=1' as any);
                      } else if (activity === 'Chat Prompts') {
                        router.push('/week-five?section=2' as any);
                      } else if (activity === 'Conflict Style') {
                        router.push('/week-five?section=3' as any);
                      }
                    }
                  }}
                >
                  <View
                    style={[styles.activityChipGradient, { backgroundColor: module.locked ? colors.backgroundTertiary : (module.color as string) }]}
                  >
                    {renderActivityIcon(activity)}
                    <Text style={[
                      styles.activityChipText,
                      module.locked && styles.activityChipTextLocked
                    ]}>
                      {activity}
                    </Text>
                    {isActivityCompleted(module.week, activity) && (
                      <CheckCircle size={14} color="colors.white" style={{ marginLeft: 4 }} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            {!module.locked && (
              <TouchableOpacity 
                style={styles.startButton}
                onPress={() => {
                  if (module.week === 1) {
                    router.push('/week-one');
                  } else if (module.week === 2) {
                    router.push('/week-two');
                  } else if (module.week === 3) {
                    router.push('/week-three');
                  } else if (module.week === 5) {
                    router.push('/week-five' as any);
                  } else if (module.week === 6) {
                    router.push('/week-six');
                  } else if (module.week === 7) {
                    router.push('/week-seven');
                  } else if (module.week === 8) {
                    router.push('/week-eight');
                  } else if (module.week === 9) {
                    router.push('/week-nine');
                  } else if (module.week === 10) {
                    router.push('/week-ten');
                  } else if (module.week === 11) {
                    router.push('/week-eleven');
                  } else if (module.week === 12) {
                    router.push('/week-twelve');
                  }
                }}
              >
                <View
                  style={[styles.startButtonGradient, { backgroundColor: module.completed ? colors.accent1 : (module.color as string) }]}
                >
                  <Text style={styles.startButtonText}>
                    {module.completed ? 'Review' : 'Start Module'}
                  </Text>
                </View>
              </TouchableOpacity>
            )}

            {module.locked && (
              <View style={styles.lockedMessage}>
                <Text style={styles.lockedText}>
                  Complete previous modules to unlock
                </Text>
              </View>
            )}
          </TouchableOpacity>
        ))}

        <View style={styles.completionCard}>
          <View
            style={[styles.completionGradient, { backgroundColor: colors.backgroundOrange }]}
          >
            <Heart size={32} color={colors.primary} />
            <Text style={styles.completionTitle}>Journey Completion</Text>
            <Text style={styles.completionText}>
              After completing all 12 weeks, unlock your personalized Relationship Yearbook - 
              a beautiful keepsake of your growth together.
            </Text>
            <View style={styles.totalPointsDisplay}>
              <Star size={20} color={colors.accent2} />
              <Text style={styles.totalPointsText}>
                Total Points: {totalPoints}
              </Text>
            </View>
          </View>
        </View>

        {/* Test Section */}
        <View style={styles.testSection}>
          <Text style={styles.testSectionTitle}>🧪 Test Features</Text>
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => router.push('/test-date-night-favorites')}
          >
            <Text style={styles.testButtonText}>Test Date Night Favorites</Text>
            <ArrowRight size={16} color={colors.white} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.testButton}
            onPress={() => router.push('/test-milestones')}
          >
            <Text style={styles.testButtonText}>Test Milestones System</Text>
            <ArrowRight size={16} color={colors.white} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundPrimary,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.textInverse,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.textInverse,
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  moduleCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  moduleCardLocked: {
    opacity: 0.6,
  },
  moduleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  moduleWeek: {
    backgroundColor: colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  moduleWeekText: {
    color: colors.textInverse,
    fontSize: 12,
    fontWeight: '600',
  },
  moduleStatus: {
    // Icon container
  },
  moduleTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  moduleTitleLocked: {
    color: 'colors.textTertiary',
  },
  moduleDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  moduleDescriptionLocked: {
    color: 'colors.borderMedium',
  },
  pointsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  pointsText: {
    color: colors.warning,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  activitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  activityChip: {
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 16,
    overflow: 'hidden',
  },
  activityChipCompleted: {
    opacity: 0.6,
  },
  activityChipGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  activityChipText: {
    color: colors.textInverse,
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  activityChipTextLocked: {
    color: 'colors.textTertiary',
  },
  startButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  startButtonGradient: {
    padding: 14,
    alignItems: 'center',
  },
  startButtonText: {
    color: colors.textInverse,
    fontSize: 16,
    fontWeight: '600',
  },
  lockedMessage: {
    backgroundColor: 'colors.backgroundTertiary',
    padding: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  lockedText: {
    color: 'colors.textTertiary',
    fontSize: 14,
    fontWeight: '500',
  },
  completionCard: {
    marginBottom: 40,
    borderRadius: 16,
    overflow: 'hidden',
  },
  completionGradient: {
    padding: 24,
    alignItems: 'center',
  },
  completionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.primary,
    marginTop: 12,
    marginBottom: 8,
  },
  completionText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  totalPointsDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  totalPointsText: {
    color: colors.warning,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 4,
  },
  testSection: {
    backgroundColor: colors.backgroundSecondary,
    margin: 20,
    borderRadius: 12,
    padding: 20,
    borderLeftWidth: 4,
    borderLeftColor: colors.info,
  },
  testSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.charcoalGray,
    marginBottom: 16,
  },
  testButton: {
    backgroundColor: colors.info,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  testButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },

});