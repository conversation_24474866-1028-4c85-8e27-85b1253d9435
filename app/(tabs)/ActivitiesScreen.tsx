import React from 'react';
import { SafeAreaView, StatusBar } from 'react-native';
import Activities from '../../src/journeys/activities/Activities';
import HamburgerMenu from '../../src/shared/components/layout/HamburgerMenu';
import { colors } from '../../src/shared/utils/colors';

export default function ActivitiesScreen() {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: colors.background }}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background} />
      <HamburgerMenu position="top-right" />
      <Activities />
    </SafeAreaView>
  );
}
