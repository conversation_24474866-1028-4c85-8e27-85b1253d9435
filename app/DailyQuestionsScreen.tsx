/**
 * Daily Questions Screen
 *
 * Main interface for daily couple bonding questions.
 * Allows users to answer questions, view partner responses, and interact.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  Alert,
  ActivityIndicator 
} from 'react-native';
import { router } from 'expo-router';
import { Heart, MessageCircle, Clock, CheckCircle, ArrowLeft, Send, Edit3 } from 'lucide-react-native';
import { useDailyQuestions } from '../src/hooks/useDailyQuestions';
import { useGlobalTheme } from '../src/components/shared/ThemeProvider';
import { tokens } from '../src/utils/theme';
import { DSCard, DSButton, DSInput } from '../src/components/shared';
import HamburgerMenu from '../src/shared/components/layout/HamburgerMenu';

export default function DailyQuestionsScreen() {
  const { currentTheme } = useGlobalTheme();
  const {
    todaysQuestion,
    userResponse,
    partnerResponse,
    hasUserAnswered,
    hasPartnerAnswered,
    isLoading,
    error,
    streakData,
    submitResponse,
    updateResponse,
    addReaction,
    addComment,
    skipQuestion
  } = useDailyQuestions();

  const [responseText, setResponseText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [isAddingComment, setIsAddingComment] = useState(false);

  const handleSubmitResponse = async () => {
    if (!responseText.trim()) {
      Alert.alert('Empty Response', 'Please enter your response before submitting.');
      return;
    }

    setIsSubmitting(true);
    try {
      const success = await submitResponse(responseText.trim());
      if (success) {
        setResponseText('');
        Alert.alert('Success', 'Your response has been saved!');
      } else {
        Alert.alert('Error', 'Failed to save your response. Please try again.');
      }
    } catch (err) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateResponse = async () => {
    if (!responseText.trim()) {
      Alert.alert('Empty Response', 'Please enter your response before updating.');
      return;
    }

    setIsSubmitting(true);
    try {
      const success = await updateResponse(responseText.trim());
      if (success) {
        setIsEditing(false);
        Alert.alert('Success', 'Your response has been updated!');
      } else {
        Alert.alert('Error', 'Failed to update your response. Please try again.');
      }
    } catch (err) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddReaction = async (responseId: string, reactionType: 'heart' | 'laugh' | 'surprise' | 'love') => {
    try {
      await addReaction(responseId, reactionType);
    } catch (err) {
      Alert.alert('Error', 'Failed to add reaction. Please try again.');
    }
  };

  const handleAddComment = async (responseId: string) => {
    if (!commentText.trim()) {
      Alert.alert('Empty Comment', 'Please enter a comment before submitting.');
      return;
    }

    setIsAddingComment(true);
    try {
      const success = await addComment(responseId, commentText.trim());
      if (success) {
        setCommentText('');
        Alert.alert('Success', 'Your comment has been added!');
      } else {
        Alert.alert('Error', 'Failed to add comment. Please try again.');
      }
    } catch (err) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsAddingComment(false);
    }
  };

  const handleSkipQuestion = async () => {
    Alert.alert(
      'Skip Question',
      'Are you sure you want to skip today\'s question? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Skip', 
          style: 'destructive',
          onPress: async () => {
            const success = await skipQuestion();
            if (success) {
              Alert.alert('Question Skipped', 'Today\'s question has been skipped.');
            } else {
              Alert.alert('Error', 'Failed to skip question. Please try again.');
            }
          }
        }
      ]
    );
  };

  const getCategoryEmoji = (category: string) => {
    const emojiMap: Record<string, string> = {
      deep: '💭',
      fun: '🎉',
      funny: '😄',
      memories: '📸',
      dreams: '✨',
      gratitude: '🙏'
    };
    return emojiMap[category] || '💬';
  };

  const getDifficultyColor = (difficulty: string) => {
    const colorMap: Record<string, string> = {
      easy: currentTheme.success,
      medium: currentTheme.warning,
      deep: currentTheme.error
    };
    return colorMap[difficulty] || currentTheme.textSecondary;
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.backgroundPrimary }]}>
        <HamburgerMenu position="top-right" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={currentTheme.primary} />
          <Text style={[styles.loadingText, { color: currentTheme.textSecondary }]}>
            Loading today's question...
          </Text>
        </View>
      </View>
    );
  }

  if (error || !todaysQuestion) {
    return (
      <View style={[styles.container, { backgroundColor: currentTheme.backgroundPrimary }]}>
        <HamburgerMenu position="top-right" />
        <View style={styles.errorContainer}>
          <MessageCircle size={48} color={currentTheme.textSecondary} />
          <Text style={[styles.errorTitle, { color: currentTheme.textPrimary }]}>
            {error ? 'Unable to Load Question' : 'No Question Today'}
          </Text>
          <Text style={[styles.errorText, { color: currentTheme.textSecondary }]}>
            {error || 'There\'s no question scheduled for today. Check back tomorrow!'}
          </Text>
          <DSButton
            title="Go Back"
            onPress={() => router.back()}
            variant="outline"
          />
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: currentTheme.backgroundPrimary }]}>
      <HamburgerMenu position="top-right" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color={currentTheme.textPrimary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: currentTheme.textPrimary }]}>
          Daily Question
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Question Card */}
        <DSCard style={styles.questionCard}>
          <View style={styles.questionHeader}>
            <View style={styles.categoryContainer}>
              <Text style={styles.categoryEmoji}>
                {getCategoryEmoji(todaysQuestion.category)}
              </Text>
              <Text style={[styles.categoryText, { color: currentTheme.textSecondary }]}>
                {todaysQuestion.category}
              </Text>
            </View>
            
            <View style={styles.difficultyContainer}>
              <View 
                style={[
                  styles.difficultyDot, 
                  { backgroundColor: getDifficultyColor(todaysQuestion.difficulty) }
                ]} 
              />
              <Text style={[styles.difficultyText, { color: currentTheme.textSecondary }]}>
                {todaysQuestion.difficulty}
              </Text>
            </View>
          </View>

          <Text style={[styles.questionText, { color: currentTheme.textPrimary }]}>
            {todaysQuestion.question_text}
          </Text>

          {/* Streak indicator */}
          {streakData && streakData.current_streak > 0 && (
            <View style={styles.streakContainer}>
              <Heart size={16} color={currentTheme.error} fill={currentTheme.error} />
              <Text style={[styles.streakText, { color: currentTheme.error }]}>
                {streakData.current_streak} day streak
              </Text>
            </View>
          )}
        </DSCard>

        {/* Response Section */}
        {!hasUserAnswered && (
          <DSCard style={styles.responseCard}>
            <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>
              Your Response
            </Text>
            <TextInput
              style={[
                styles.responseInput,
                { 
                  backgroundColor: currentTheme.backgroundSecondary,
                  color: currentTheme.textPrimary,
                  borderColor: currentTheme.border
                }
              ]}
              placeholder="Share your thoughts..."
              placeholderTextColor={currentTheme.textSecondary}
              value={responseText}
              onChangeText={setResponseText}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
            <View style={styles.responseActions}>
              <DSButton
                title="Skip Question"
                onPress={handleSkipQuestion}
                variant="outline"
              />
              <DSButton
                title={isSubmitting ? "Saving..." : "Submit Response"}
                onPress={handleSubmitResponse}
                disabled={isSubmitting || !responseText.trim()}
              />
            </View>
          </DSCard>
        )}

        {/* User's Response */}
        {hasUserAnswered && userResponse && (
          <DSCard style={styles.responseCard}>
            <View style={styles.responseHeader}>
              <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>
                Your Response
              </Text>
              {!isEditing && (
                <TouchableOpacity onPress={() => setIsEditing(true)}>
                  <Edit3 size={16} color={currentTheme.textSecondary} />
                </TouchableOpacity>
              )}
            </View>
            
            {isEditing ? (
              <>
                <TextInput
                  style={[
                    styles.responseInput,
                    { 
                      backgroundColor: currentTheme.backgroundSecondary,
                      color: currentTheme.textPrimary,
                      borderColor: currentTheme.border
                    }
                  ]}
                  value={responseText || userResponse}
                  onChangeText={setResponseText}
                  multiline
                  numberOfLines={4}
                  textAlignVertical="top"
                />
                <View style={styles.editActions}>
                  <DSButton
                    title="Cancel"
                    onPress={() => setIsEditing(false)}
                    variant="outline"
                  />
                  <DSButton
                    title={isSubmitting ? "Updating..." : "Update"}
                    onPress={handleUpdateResponse}
                    disabled={isSubmitting}
                  />
                </View>
              </>
            ) : (
              <Text style={[styles.responseText, { color: currentTheme.textPrimary }]}>
                {userResponse}
              </Text>
            )}
          </DSCard>
        )}

        {/* Partner's Response */}
        {hasPartnerAnswered && partnerResponse && (
          <DSCard style={styles.responseCard}>
            <Text style={[styles.sectionTitle, { color: currentTheme.textPrimary }]}>
              Partner's Response
            </Text>
            <Text style={[styles.responseText, { color: currentTheme.textPrimary }]}>
              {partnerResponse}
            </Text>
            
            {/* Reactions */}
            <View style={styles.reactionsContainer}>
              <Text style={[styles.reactionsLabel, { color: currentTheme.textSecondary }]}>
                React:
              </Text>
              <View style={styles.reactionsRow}>
                {['heart', 'laugh', 'surprise', 'love'].map((reaction) => (
                  <TouchableOpacity
                    key={reaction}
                    style={styles.reactionButton}
                    onPress={() => handleAddReaction('partner-response-id', reaction as any)}
                  >
                    <Text style={styles.reactionEmoji}>
                      {reaction === 'heart' ? '❤️' : 
                       reaction === 'laugh' ? '😂' : 
                       reaction === 'surprise' ? '😮' : '😍'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Comments */}
            <View style={styles.commentsContainer}>
              <Text style={[styles.commentsLabel, { color: currentTheme.textSecondary }]}>
                Add a comment:
              </Text>
              <View style={styles.commentInputRow}>
                <TextInput
                  style={[
                    styles.commentInput,
                    { 
                      backgroundColor: currentTheme.backgroundSecondary,
                      color: currentTheme.textPrimary,
                      borderColor: currentTheme.border
                    }
                  ]}
                  placeholder="Write a comment..."
                  placeholderTextColor={currentTheme.textSecondary}
                  value={commentText}
                  onChangeText={setCommentText}
                />
                <TouchableOpacity
                  style={[
                    styles.commentButton,
                    { backgroundColor: currentTheme.primary }
                  ]}
                  onPress={() => handleAddComment('partner-response-id')}
                  disabled={isAddingComment || !commentText.trim()}
                >
                  {isAddingComment ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <Send size={16} color="white" />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </DSCard>
        )}

        {/* Status Card */}
        <DSCard style={styles.statusCard}>
          <View style={styles.statusRow}>
            <View style={styles.statusItem}>
              <View style={[
                styles.statusDot, 
                { backgroundColor: hasUserAnswered ? currentTheme.success : currentTheme.border }
              ]} />
              <Text style={[styles.statusLabel, { color: currentTheme.textSecondary }]}>
                You {hasUserAnswered ? 'answered' : 'pending'}
              </Text>
            </View>
            
            <View style={styles.statusItem}>
              <View style={[
                styles.statusDot, 
                { backgroundColor: hasPartnerAnswered ? currentTheme.success : currentTheme.border }
              ]} />
              <Text style={[styles.statusLabel, { color: currentTheme.textSecondary }]}>
                Partner {hasPartnerAnswered ? 'answered' : 'pending'}
              </Text>
            </View>
          </View>
        </DSCard>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: tokens.spacing.md,
    fontSize: tokens.typography.fontSize.md,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: tokens.spacing.xl,
  },
  errorTitle: {
    fontSize: tokens.typography.fontSize.xl,
    fontWeight: tokens.typography.fontWeight.semibold,
    marginTop: tokens.spacing.lg,
    marginBottom: tokens.spacing.sm,
  },
  errorText: {
    fontSize: tokens.typography.fontSize.md,
    textAlign: 'center',
    marginBottom: tokens.spacing.xl,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing.lg,
    paddingVertical: tokens.spacing.md,
  },
  backButton: {
    padding: tokens.spacing.sm,
  },
  headerTitle: {
    flex: 1,
    fontSize: tokens.typography.fontSize.lg,
    fontWeight: tokens.typography.fontWeight.semibold,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: tokens.spacing.lg,
  },
  questionCard: {
    marginBottom: tokens.spacing.lg,
  },
  questionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing.md,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryEmoji: {
    fontSize: 20,
    marginRight: tokens.spacing.sm,
  },
  categoryText: {
    fontSize: tokens.typography.fontSize.sm,
    textTransform: 'capitalize',
  },
  difficultyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: tokens.spacing.xs,
  },
  difficultyText: {
    fontSize: tokens.typography.fontSize.sm,
    textTransform: 'capitalize',
  },
  questionText: {
    fontSize: tokens.typography.fontSize.lg,
    lineHeight: tokens.typography.lineHeight.relaxed,
    marginBottom: tokens.spacing.md,
  },
  streakContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  streakText: {
    marginLeft: tokens.spacing.xs,
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.medium,
  },
  responseCard: {
    marginBottom: tokens.spacing.lg,
  },
  responseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing.md,
  },
  sectionTitle: {
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
  },
  responseInput: {
    borderWidth: 1,
    borderRadius: tokens.radii.md,
    padding: tokens.spacing.md,
    fontSize: tokens.typography.fontSize.md,
    marginBottom: tokens.spacing.md,
    minHeight: 100,
  },
  responseText: {
    fontSize: tokens.typography.fontSize.md,
    lineHeight: tokens.typography.lineHeight.normal,
    marginBottom: tokens.spacing.md,
  },
  responseActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  reactionsContainer: {
    marginTop: tokens.spacing.md,
    paddingTop: tokens.spacing.md,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  reactionsLabel: {
    fontSize: tokens.typography.fontSize.sm,
    marginBottom: tokens.spacing.sm,
  },
  reactionsRow: {
    flexDirection: 'row',
  },
  reactionButton: {
    padding: tokens.spacing.sm,
    marginRight: tokens.spacing.sm,
  },
  reactionEmoji: {
    fontSize: 20,
  },
  commentsContainer: {
    marginTop: tokens.spacing.md,
  },
  commentsLabel: {
    fontSize: tokens.typography.fontSize.sm,
    marginBottom: tokens.spacing.sm,
  },
  commentInputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  commentInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: tokens.radii.md,
    padding: tokens.spacing.sm,
    fontSize: tokens.typography.fontSize.sm,
    marginRight: tokens.spacing.sm,
    minHeight: 40,
  },
  commentButton: {
    padding: tokens.spacing.sm,
    borderRadius: tokens.radii.md,
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
  },
  statusCard: {
    marginBottom: tokens.spacing.xl,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: tokens.spacing.sm,
  },
  statusLabel: {
    fontSize: tokens.typography.fontSize.sm,
  },
});
