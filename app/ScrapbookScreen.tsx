import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Alert, Share } from 'react-native';

import { Camera, Heart, Calendar, Download, BookOpen, ArrowRight, ArrowLeft, Share2 } from 'lucide-react-native';
import { router } from 'expo-router';
import { useWeekOneData } from '../src/hooks/useWeekOneData';
import { useWeekFiveData } from '../src/hooks/useWeekFiveData';
import { useOriginStoryData } from '../src/hooks/useOriginStoryData';
import { useDateNightIdeasSupabase } from '../src/hooks/useDateNightIdeasSupabase';
import { useUserProfile } from '../src/hooks/useUserProfile';
import { colors } from '../src/shared/utils/colors';
import HamburgerMenu from '../src/components/HamburgerMenu';
import { useTimeline } from '../src/hooks/useTimeline';
import { DSCard } from '../src/components/shared';


// Types for scrapbook memories
type MemoryType = 'activity' | 'reflection' | 'date-night' | 'date' | 'skill' | 'milestone' | 'photo';
interface Memory {
  id: string;
  title: string;
  date: string;
  type: MemoryType;
  content: string;
  image: string | null;
  points: number;
  action?: () => void;
  source?: 'global' | 'user';
  weekNumber?: number;
}

export default function ScrapbookScreen() {
  const { data: weekOneData } = useWeekOneData();
  const { data: weekFiveData } = useWeekFiveData();
  const { data: originStoryData } = useOriginStoryData();
  const { getCompletedIdeas, allIdeas } = useDateNightIdeasSupabase();
  const { items: timelineItems, loading: timelineLoading, error: timelineError } = useTimeline();

  const { profile } = useUserProfile();

  const handleViewOriginStory = () => {
    router.push('/our-story');
  };

  const handleViewWeekOne = () => {
    router.push('/week-one');
  };

  const handleViewWeekFive = () => {
    router.push('/week-five' as any);
  };

  // Add handlers for action buttons
  const handleShareJourney = async () => {
    try {
      const journeyText = `💕 Our Love Story Journey 💕

${hasOriginStory ? '✨ We have an amazing origin story!' : '✨ We\'re building our story together!'}

📅 Total Memories: ${memories.length}
🏆 Total Points: ${memories.reduce((sum: any, memory: any) => sum + memory.points, 0)}
💝 Completed Activities: ${memories.filter(m => m.type === 'activity').length}
🌙 Date Nights: ${memories.filter(m => m.type === 'date-night').length}

${memories.length > 0 ? `\nRecent Highlights:\n${memories.slice(0, 3).map(m => `• ${m.title} (${m.date})`).join('\n')}` : ''}

${hasOriginStory ? '\nWe\'ve shared our story - how we met, when we knew we were in love, our first kiss, and all the special moments that brought us together.' : ''}

💖 Built with Everlasting Us - the app that helps couples grow stronger together! 💖`;

      await Share.share({
        message: journeyText,
        title: 'Our Love Story Journey',
      });
    } catch (error) {
      console.error('Error sharing journey:', error);
      Alert.alert(
        'Share Error',
        'Unable to share your journey at this time. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleExportPDF = async () => {
    try {
      // For now, we'll create a comprehensive text summary that can be copied
      // In a real implementation, you'd use a library like react-native-html-to-pdf
      const pdfContent = `EVERLASTING US - RELATIONSHIP JOURNEY REPORT
Generated on: ${new Date().toLocaleDateString()}
==================================================

COUPLE PROFILE
${profile.partner1.name} & ${profile.partner2.name}

ORIGIN STORY
${hasOriginStory ? '✅ Completed' : '⏳ In Progress'}
${hasOriginStory ? 'We have shared our complete origin story including how we met, when we knew we were in love, our first kiss, and special moments.' : 'We are still building our origin story together.'}

JOURNEY TIMELINE
Total Memories: ${memories.length}
Total Points Earned: ${memories.reduce((sum: any, memory: any) => sum + memory.points, 0)}

${memories.map((memory: any, index: number) => `
${index + 1}. ${memory.title}
   Date: ${memory.date}
   Type: ${memory.type.charAt(0).toUpperCase() + memory.type.slice(1)}
   Points: +${memory.points}
   ${memory.content}
`).join('\n')}

WEEKLY PROGRESS
${weekOneData.completedSections.filter(Boolean).length}/4 Week 1 activities completed
${weekFiveData.completedSections.filter(Boolean).length}/2 Week 5 activities completed

RELATIONSHIP INSIGHTS
• We have completed ${memories.filter(m => m.type === 'activity').length} relationship-building activities
• We have planned and completed ${memories.filter(m => m.type === 'date-night' || m.type === 'date').length} date nights
• We have shared ${memories.filter(m => m.type === 'reflection').length} deep conversations
• We have learned ${memories.filter(m => m.type === 'skill').length} new relationship skills

This report was generated by Everlasting Us - helping couples grow stronger together! 💖`;

      // Copy to clipboard for now (in a real app, this would generate a PDF)
      const { Clipboard } = require('@react-native-clipboard/clipboard');
      await Clipboard.setString(pdfContent);

      Alert.alert(
        'Journey Report Copied! 📋',
        'Your complete relationship journey has been copied to your clipboard. You can paste it into any document or note-taking app.\n\nIn the future, this will generate a beautiful PDF report!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error exporting journey:', error);
      Alert.alert(
        'Export Error',
        'Unable to export your journey at this time. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleMemoryLike = async (memoryId: string) => {
    try {
      // In a real app, this would save to a database
      // For now, we'll show a success message
      Alert.alert(
        'Memory Liked! ❤️',
        'This memory has been added to your favorites!',
        [{ text: 'OK' }]
      );

      // TODO: Implement actual like functionality
      // - Save to local storage or database
      // - Update UI to show liked state
      // - Sync with partner's device
    } catch (error) {
      console.error('Error liking memory:', error);
      Alert.alert(
        'Error',
        'Unable to like this memory. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleMemoryShare = async (memoryId: string) => {
    try {
      const memory = memories.find((m): m is Memory => m.id === memoryId);
      if (!memory) return;

      const shareText = `💕 ${memory.title} 💕

${memory.content}

Date: ${memory.date}
Points: +${memory.points}

Shared from our Everlasting Us journey! 💖`;

      await Share.share({
        message: shareText,
        title: memory.title,
      });
    } catch (error) {
      console.error('Error sharing memory:', error);
      Alert.alert(
        'Share Error',
        'Unable to share this memory at this time. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // Check if origin story has meaningful content (not just empty strings)
  const hasOriginStory = originStoryData && (
    originStoryData.firstMeeting.trim() !== '' ||
    originStoryData.knewILovedYou.trim() !== '' ||
    originStoryData.firstKiss.trim() !== '' ||
    originStoryData.insideJokes.trim() !== '' ||
    originStoryData.mostRomantic.trim() !== '' ||
    originStoryData.biggestChallenge.trim() !== '' ||
    originStoryData.bestMemories.trim() !== ''
  );

  const completedDateNights = getCompletedIdeas();

  const memories: Memory[] = [
    // Add origin story at the top if it exists
    ...(hasOriginStory ? [{
      id: 'origin-story',
      title: 'Our Origin Story',
      date: 'The beginning of us',
      type: 'reflection' as const,
      content: 'We shared our story - how we met, when we knew we were in love, our first kiss, and all the special moments that brought us together.',
      image: null,
      points: 50,
      action: handleViewOriginStory,
    } satisfies Memory] : []),

    // Add completed Date Nights from the global pool
    ...completedDateNights.map((userIdea): Memory | null => {
      const idea = allIdeas.find(i => i.id === userIdea.ideaId);
      if (!idea) return null;

      return {
        id: `date-night-${userIdea.id}`,
        title: idea.title,
        date: userIdea.completedAt ? new Date(userIdea.completedAt).toLocaleDateString() : 'Recently',
        type: 'date-night',
        content: idea.description,
        image: null, // Ideas don't have images in the new structure
        points: 25, // base points
        action: () => router.push('/(tabs)/date-night'),
        source: idea.source,
        weekNumber: idea.weekNumber
      };
    }).filter((m): m is Memory => m !== null),

    ...(weekOneData.completedSections[0] ? [{
      id: 'week1-match-game',
      title: 'The Match Game',
      date: 'This week',
      type: 'activity',
      content: 'We played the match game and discovered how well we know each other! It was so fun to guess each other\'s answers.',
      image: null,
      points: 30,
      action: handleViewWeekOne,
    }] as Memory[] : []),
    ...(weekOneData.completedSections[1] ? [{
      id: 'week1-date-night',
      title: 'Date Night Plan',
      date: 'This week',
      type: 'date' as const,
      content: `We planned our game night date: ${weekOneData.dateNightPlan.when ? weekOneData.dateNightPlan.when + ' at ' : ''}${weekOneData.dateNightPlan.where || 'our favorite spot'}!`,
      image: null,
      points: 25,
      action: handleViewWeekOne,
    }] as Memory[] : []),
    ...(weekOneData.completedSections[2] ? [{
      id: 'week1-chat-prompts',
      title: 'Deep Conversations',
      date: 'This week',
      type: 'reflection',
      content: 'We shared our thoughts on happiness and what we wish we made more time for. Such meaningful conversations!',
      image: null,
      points: 20,
      action: handleViewWeekOne,
    }] as Memory[] : []),
    ...(weekOneData.completedSections[3] ? [{
      id: 'week1-soft-startup',
      title: 'Soft Start-Up Practice',
      date: 'This week',
      type: 'skill' as const,
      content: 'We practiced rewriting statements using "I" statements. Great communication skill building!',
      image: null,
      points: 25,
      action: handleViewWeekOne,
    }] as Memory[] : []),
    ...(weekFiveData.completedSections[0] ? [{
      id: 'week5-dream-vacation',
      title: 'Dream Vacation Adjectives',
      date: 'This week',
      type: 'activity',
      content: 'We described our perfect vacation using 5 adjectives each. It was so fun to see our vacation vibes side by side!',
      image: null,
      points: 30,
      action: handleViewWeekFive,
    }] as Memory[] : []),
    ...(weekFiveData.completedSections[1] ? [{
      id: 'week5-vacation-plan',
      title: 'Dream Vacation Planning',
      date: 'This week',
      type: 'date' as const,
      content: `We planned our ultimate dream vacation to ${weekFiveData.dreamVacationPlan.worldLocation || 'an amazing destination'} with a ${weekFiveData.dreamVacationPlan.vibe || 'perfect'} vibe!`,
      image: null,
      points: 35,
      action: handleViewWeekFive,
    }] as Memory[] : []),
    ...(weekFiveData.completedSections[2] ? [{
      id: 'week5-chat-prompts',
      title: 'Deep Conversations',
      date: 'This week',
      type: 'reflection',
      content: 'We explored meaningful questions about relationships and personal growth. Such insightful conversations!',
      image: null,
      points: 25,
      action: handleViewWeekFive,
    }] as Memory[] : []),
    ...(weekFiveData.completedSections[3] ? [{
      id: 'week5-conflict-style',
      title: 'Conflict Style Awareness',
      date: 'This week',
      type: 'skill' as const,
      content: 'We discovered our conflict resolution styles and explored alternatives. Great relationship skill building!',
      image: null,
      points: 30,
      action: handleViewWeekFive,
    }] as Memory[] : []),

    {
      id: '2',
      title: 'Gratitude Practice',
      date: '1 week ago',
      type: 'reflection',
      content: 'Today we practiced the 5:1 ratio and shared what we appreciate most about each other. I love how you always make me laugh!',
      image: null,
      points: 20,
    },
    {
      id: '3',
      title: 'Cooking Date Night',
      date: '5 days ago',
      type: 'date',
      content: 'Attempted the "Cooking Showdown" date idea. Neither of us won, but we had so much fun making a mess in the kitchen!',
      image: null,
      points: 30,
    },
    {
      id: '4',
      title: 'Communication Breakthrough',
      date: '3 days ago',
      type: 'skill',
      content: 'Used the soft start-up technique during a discussion. Amazing how much smoother our conversation flowed!',
      image: null,
      points: 25,
    },
  ];

  const milestones = [
    { title: 'Started Journey Together', date: 'Jan 15, 2024' },
    ...(hasOriginStory ? [{ title: 'Shared Origin Story', date: 'The beginning of us' }] : []),
    { title: 'Completed Week 1', date: 'Jan 22, 2024' },
    { title: 'Earned First Badge', date: 'Jan 25, 2024' },
  ];

  const getTypeColor = (type: MemoryType): string => {
    switch (type) {
      case 'activity':
        return colors.primary;
      case 'reflection':
        return colors.blue;
      case 'date':
      case 'date-night':
        return colors.warning;
      case 'skill':
        return colors.success;
      default:
        return colors.primary;
    }
  };

  const getTypeIcon = (type: MemoryType) => {
    switch (type) {
      case 'activity':
        return '🎯';
      case 'reflection':
        return '💭';
      case 'date':
      case 'date-night':
        return '💕';
      case 'skill':
        return '🧠';
      default:
        return '❤️';
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Hamburger Menu */}
      <HamburgerMenu position="top-right" />

      {/* Header */}
      <View
        style={[styles.header, { backgroundColor: colors.primary }]}
      >
        <View style={styles.headerTop}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={colors.textInverse} />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            <Text style={styles.headerTitle}>Our Scrapbook</Text>
            <Text style={styles.headerSubtitle}>Cherish your memories and moments together</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleShareJourney}>
            <Share2 size={16} color={colors.primary} />
            <Text style={styles.actionButtonText}>Share Journey</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton} onPress={handleExportPDF}>
            <Download size={16} color={colors.primary} />
            <Text style={styles.actionButtonText}>Export PDF</Text>
          </TouchableOpacity>
        </View>

        {/* Origin Story Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Our Origin Story</Text>
            <TouchableOpacity style={styles.viewStoryButton} onPress={handleViewOriginStory}>
              <BookOpen size={16} color={colors.primary} />
              <Text style={styles.viewStoryButtonText}>View Story</Text>
            </TouchableOpacity>
          </View>
          <DSCard style={styles.originStoryCard}>
            <View style={styles.originStoryIcon}>
              <Heart size={32} color={colors.primary} />
            </View>
            <Text style={styles.originStoryTitle}>The Beginning of Us</Text>
            <Text style={styles.originStoryText}>
              Your love story is the foundation of everything you build together. From your first meeting to your biggest challenges overcome, every moment shapes your relationship.
            </Text>
            <TouchableOpacity style={styles.originStoryButton} onPress={handleViewOriginStory}>
              <View
                style={[styles.originStoryButtonGradient, { backgroundColor: colors.primary }]}
              >
                <Text style={styles.originStoryButtonText}>Read Our Story</Text>
                <ArrowRight size={16} color={colors.white} />
              </View>
            </TouchableOpacity>
          </DSCard>
        </View>


	          {/* DB-backed Timeline (initial) */}
	          {timelineItems.length > 0 && (
	            <View style={{ marginTop: 8 }}>
	              {timelineItems.map(item => (
	                <DSCard key={item.id} style={styles.memoryCard}>
	                  <View style={styles.memoryHeader}>
	                    <View style={styles.memoryType}>
	                      <View style={[styles.memoryTypeGradient, { backgroundColor: colors.primary }]}>
	                        <Text style={styles.memoryTypeIcon}>🕘</Text>
	                      </View>
	                    </View>
	                    <View style={styles.memoryInfo}>
	                      <Text style={styles.memoryTitle}>{item.title}</Text>
	                      <View style={styles.memoryMeta}>
	                        <Calendar size={12} color={colors.textSecondary} />
	                        <Text style={styles.memoryDate}>{new Date(item.eventDate).toLocaleDateString()}</Text>
	                      </View>
	                    </View>
	                  </View>
	                  {item.imageUrl && (
	                    <Image source={{ uri: item.imageUrl }} style={styles.memoryImage} resizeMode="cover" />
	                  )}
	                  {item.description && (
	                    <Text style={styles.memoryContent}>{item.description}</Text>
	                  )}
	                </DSCard>
	              ))}
	            </View>
	          )}

        {/* Timeline */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Journey Timeline</Text>
          {memories.map((memory: any) => (
            <DSCard key={memory.id} style={styles.memoryCard}>
              <TouchableOpacity
                onPress={memory.action}
                disabled={!memory.action}
              >
                <View style={styles.memoryHeader}>
                  <View style={styles.memoryType}>
                    <View
                      style={[styles.memoryTypeGradient, { backgroundColor: colors.primary }]}
                    >
                      <Text style={styles.memoryTypeIcon}>{getTypeIcon(memory.type)}</Text>
                    </View>
                  </View>

                  <View style={styles.memoryInfo}>
                    <Text style={styles.memoryTitle}>{memory.title}</Text>
                    <View style={styles.memoryMeta}>
                      <Calendar size={12} color={colors.textSecondary} />
                      <Text style={styles.memoryDate}>{memory.date}</Text>
                      <View style={styles.pointsBadge}>
                        <Text style={styles.pointsText}>+{memory.points}</Text>
                      </View>
                    </View>
                  </View>
                </View>

                {memory.image && (
                  <Image
                    source={{ uri: memory.image }}
                    style={styles.memoryImage}
                    resizeMode="cover"
                  />
                )}

                <Text style={styles.memoryContent}>{memory.content}</Text>

                <View style={styles.memoryActions}>
                  <TouchableOpacity style={styles.memoryAction} onPress={() => handleMemoryLike(String(memory.id))}>
                    <Heart size={16} color={colors.primary} />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.memoryAction} onPress={() => handleMemoryShare(String(memory.id))}>
                    <Share2 size={16} color={colors.textSecondary} />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            </DSCard>
          ))}
        </View>

        {/* Milestones */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Milestones</Text>
          <View style={styles.milestonesCard}>
            {milestones.map((milestone: any, index: number) => (
              <View key={index} style={styles.milestoneItem}>
                <View style={styles.milestoneMarker} />
                <View style={styles.milestoneContent}>
                  <Text style={styles.milestoneTitle}>{milestone.title}</Text>
                  <Text style={styles.milestoneDate}>{milestone.date}</Text>
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Journey Stats */}
        <View style={styles.statsCard}>
          <View
            style={[styles.statsGradient, { backgroundColor: colors.backgroundOrange }]}
          >
            <Text style={styles.statsTitle}>Journey Summary</Text>
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{memories.length}</Text>
                <Text style={styles.statLabel}>Memories</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{memories.reduce((sum: any, memory: any) => sum + (memory?.points ?? 0), 0)}</Text>
                <Text style={styles.statLabel}>Points Earned</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{weekOneData.completedSections.every(Boolean) ? 1 : 0}</Text>
                <Text style={styles.statLabel}>Weeks Completed</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Future Yearbook */}
        <View style={styles.yearBookCard}>
          <View
            style={[styles.yearBookGradient, { backgroundColor: colors.primary }]}
          >
            <Text style={styles.yearBookTitle}>Coming Soon</Text>
            <Text style={styles.yearBookText}>
              Complete all 12 weeks to unlock your personalized Relationship Yearbook -
              a beautiful digital keepsake of your entire journey together.
            </Text>
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: `${(weekOneData.completedSections.filter(Boolean).length / 4) * 100}%` }]} />
              </View>
              <Text style={styles.progressText}>
                {weekOneData.completedSections.filter(Boolean).length} of 4 activities completed
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundOrange,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  backButton: {
    padding: 8,
    marginRight: 16,
  },
  headerContent: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 4,
    textAlign: 'center',
  },
  headerSubtitle: {
    fontSize: 16,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    marginBottom: 24,
    justifyContent: 'space-between',
  },
  actionButton: {
    backgroundColor: colors.white,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    flex: 0.48,
    justifyContent: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  actionButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  viewStoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  viewStoryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.primary,
    marginLeft: 6,
  },
  originStoryCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
    borderWidth: 0.5,
    borderColor: colors.borderLight,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    alignItems: 'center',
  },
  originStoryIcon: {
    marginBottom: 16,
  },
  originStoryTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 12,
    textAlign: 'center',
  },
  originStoryText: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 20,
  },
  originStoryButton: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  originStoryButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  originStoryButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  memoryCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    borderWidth: 0.5,
    borderColor: colors.borderLight,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  memoryHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  memoryType: {
    marginRight: 12,
  },
  memoryTypeGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  memoryTypeIcon: {
    fontSize: 18,
  },
  memoryInfo: {
    flex: 1,
  },
  memoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  memoryMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memoryDate: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
    marginRight: 8,
  },
  pointsBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  pointsText: {
    color: colors.white,
    fontSize: 10,
    fontWeight: '600',
  },
  memoryImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 12,
  },
  memoryContent: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 12,
  },
  memoryActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  memoryAction: {
    padding: 8,
    marginLeft: 8,
  },
  milestonesCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  milestoneItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  milestoneMarker: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.primary,
    marginRight: 16,
  },
  milestoneContent: {
    flex: 1,
  },
  milestoneTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 2,
  },
  milestoneDate: {
    fontSize: 12,
    color: '#6B7280',
  },
  statsCard: {
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
  },
  statsGradient: {
    padding: 20,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.primary,
    textAlign: 'center',
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  yearBookCard: {
    marginBottom: 40,
    borderRadius: 16,
    overflow: 'hidden',
  },
  yearBookGradient: {
    padding: 24,
    alignItems: 'center',
  },
  yearBookTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.white,
    marginBottom: 8,
  },
  yearBookText: {
    fontSize: 14,
    color: colors.white,
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 20,
    marginBottom: 16,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 8,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.white,
    borderRadius: 4,
  },
  progressText: {
    color: colors.white,
    fontSize: 12,
    opacity: 0.9,
  },
});