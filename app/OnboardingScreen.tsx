/**
 * Onboarding Screen - Optimized
 * 
 * Optimized version using shared authentication components.
 * Demonstrates significant code reduction and improved maintainability.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Heart, Users, Camera, Check } from 'lucide-react-native';
import { markOnboardingCompleted } from '../src/utils/onboardingStorage';
import { secureStorage } from '../src/utils/secureStorage';
import {
  CONTENT_CONSTANTS,
  UI_CONSTANTS,
  APP_CONSTANTS,
  ICON_MAPPINGS,
  ROUTES
} from '../src/utils/constants';
import { useGlobalTheme } from '../src/components/shared/ThemeProvider';
import { useUserProfile } from '../src/hooks/useUserProfile';
import { useCouplePairing } from '../src/hooks/useCouplePairing';
import { useUserEvents, USER_EVENTS } from '../src/hooks/useUserEvents';
import { useAuth } from '../src/hooks/useAuth';
import { simpleErrorService } from '../src/services/simpleErrorService';
import { colors } from '../src/shared/utils/colors';

// Import shared auth components
import {
  AuthScreenLayout,
  OnboardingStep,
  OnboardingNavigation,
  AuthInput,
} from '../src/components/shared/AuthComponents';

// Import DS components
import { DSButton } from '../src/components/shared';

interface OnboardingStepData {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  backgroundColor: string;
}



// Use constants for onboarding steps
const getOnboardingSteps = (theme: any): OnboardingStepData[] => [
  {
    id: 1,
    title: CONTENT_CONSTANTS.ONBOARDING.STEPS[0].title,
    description: CONTENT_CONSTANTS.ONBOARDING.STEPS[0].description,
    icon: <Heart size={UI_CONSTANTS.DIMENSIONS.ICON_SIZE_HERO} color={theme.textInverse} />,
    backgroundColor: theme.primary,
  },
  {
    id: 2,
    title: CONTENT_CONSTANTS.ONBOARDING.STEPS[1].title,
    description: CONTENT_CONSTANTS.ONBOARDING.STEPS[1].description,
    icon: <Camera size={UI_CONSTANTS.DIMENSIONS.ICON_SIZE_HERO} color={theme.textInverse} />,
    backgroundColor: theme.secondary,
  },
  {
    id: 3,
    title: CONTENT_CONSTANTS.ONBOARDING.STEPS[2].title,
    description: CONTENT_CONSTANTS.ONBOARDING.STEPS[2].description,
    icon: <Users size={UI_CONSTANTS.DIMENSIONS.ICON_SIZE_HERO} color={theme.textInverse} />,
    backgroundColor: theme.accent,
  },
];

// Additional setup steps after welcome + intro slides:
//  - 0: Welcome screen (Nestled branding)
//  - 1-3: Feature intro slides
//  - 4: Partner names + icons
//  - 5: Invite partner (create couple + share code)
//  - 6: Weekly ritual schedule
//  - 7: Journal icon preference
const TOTAL_STEPS = 1 + CONTENT_CONSTANTS.ONBOARDING.STEPS.length + 5; // +1 for welcome screen

export default function Onboarding() {
  const [currentStep, setCurrentStep] = useState(0);
  const [partnerName, setPartnerName] = useState('');
  // Partner setup
  const [partner1Name, setPartner1Name] = useState('');
  const [partner2Name, setPartner2Name] = useState('');
  const [partner1Icon, setPartner1Icon] = useState('');
  const [partner2Icon, setPartner2Icon] = useState('');
  // Weekly ritual
  const [ritualDay, setRitualDay] = useState<'Sun'|'Mon'|'Tue'|'Wed'|'Thu'|'Fri'|'Sat'>('Sun');
  const [ritualTime, setRitualTime] = useState('19:00');
  const [ritualReminders, setRitualReminders] = useState(true);
  // Journal icon preference
  const [journalIcon, setJournalIcon] = useState('heart');
  // Partner invitation state
  const [skipPartnerInvite, setSkipPartnerInvite] = useState(false);
  const [inviteCodeCopied, setInviteCodeCopied] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const { logEvent } = useUserEvents();
  const { isAuthenticated } = useAuth();
  const { updatePartner1, updatePartner2 } = useUserProfile();
  const { currentTheme } = useGlobalTheme();
  const {
    createCouple,
    couple,
    isCreatingCouple,
    invitationText,
    copyInvitationText,
    canCreateCouple
  } = useCouplePairing();

  useEffect(() => {
    logEvent(USER_EVENTS.ONBOARDING_STARTED);
  }, []);

  const handleNext = () => {
    if (currentStep < TOTAL_STEPS - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleComplete = async () => {
    try {
      setShowConfetti(true);
      // Persist partner profile
      if (partner1Name.trim() && partner2Name.trim() && partner1Icon && partner2Icon) {
        await updatePartner1({ name: partner1Name.trim(), icon: partner1Icon });
        await updatePartner2({ name: partner2Name.trim(), icon: partner2Icon });
      }
      // Persist ritual preferences
      await secureStorage.setItem(APP_CONSTANTS.STORAGE_KEYS.WEEKLY_RITUAL, {
        day: ritualDay,
        time: ritualTime,
        reminders: ritualReminders
      });
      // Persist journal icon preference
      await secureStorage.setItem(APP_CONSTANTS.STORAGE_KEYS.JOURNAL_ICON_PREFERENCE, { icon: journalIcon });

      // Log all onboarding events
      await logEvent(USER_EVENTS.ONBOARDING_PARTNER_PROFILE_SET);
      if (couple || skipPartnerInvite) {
        await logEvent(USER_EVENTS.ONBOARDING_PARTNER_INVITED);
      }
      await logEvent(USER_EVENTS.ONBOARDING_RITUAL_CONFIGURED);
      await logEvent(USER_EVENTS.ONBOARDING_JOURNAL_ICON_SELECTED);
      await logEvent(USER_EVENTS.ONBOARDING_COMPLETED);

      await markOnboardingCompleted();

      setTimeout(() => {
        router.replace(ROUTES.OUR_STORY + '?mode=edit');
      }, UI_CONSTANTS.ANIMATIONS.DURATION_ONBOARDING_COMPLETE);
    } catch (error) {
      console.error('Error completing onboarding:', error);
      await simpleErrorService.reportError(error as Error, {
        component: 'OnboardingScreen',
        action: 'handleComplete',
        metadata: { partnerName },
      });
      router.replace(ROUTES.TABS);
    }
  };

  const handleSkip = async () => {
    try {
      await markOnboardingCompleted();
      await logEvent(USER_EVENTS.ONBOARDING_COMPLETED);
      router.replace(ROUTES.TABS);
    } catch (error) {
      console.error('Error skipping onboarding:', error);
      await simpleErrorService.reportError(error as Error, {
        component: 'OnboardingScreen',
        action: 'handleSkip',
        metadata: { partnerName },
      });
      // TEMPORARY: Skip login and go directly to main app
      router.replace('/(tabs)');
    }
  };

  const onboardingSteps = getOnboardingSteps(currentTheme);

  const renderCurrentStep = () => {
    // Step 0: Welcome screen
    if (currentStep === 0) {
      return (
        <View style={styles.welcomeContainer}>
          <View style={styles.welcomeContent}>
            <Text style={styles.welcomeLogo}>{CONTENT_CONSTANTS.APP_NAME}</Text>
            <Text style={styles.welcomeTagline}>{CONTENT_CONSTANTS.APP_TAGLINE}</Text>
            <View style={{ alignSelf: 'stretch' }}>
              <DSButton
                title={CONTENT_CONSTANTS.ONBOARDING.WELCOME_BUTTON_TEXT}
                onPress={handleNext}
                variant="pill"
                tone="pink"
              />
            </View>
          </View>
        </View>
      );
    }

    // Steps 1-3: Feature intro slides (adjusted for new indexing)
    if (currentStep >= 1 && currentStep <= onboardingSteps.length) {
      const step = onboardingSteps[currentStep - 1]; // Adjust index since we added welcome screen
      return (
        <OnboardingStep
          title={step.title}
          description={step.description}
          icon={step.icon}
          backgroundColor={step.backgroundColor}
        />
      );
    }

    // Step 4: Partner names + icons (adjusted for welcome screen)
    if (currentStep === onboardingSteps.length + 1) {
      const iconOptions = [
        { id: 'heart', symbol: '❤️' },
        { id: 'star', symbol: '⭐' },
        { id: 'flower', symbol: '🌸' },
        { id: 'moon', symbol: '🌙' },
        { id: 'sun', symbol: '☀️' },
        { id: 'sparkles', symbol: '✨' },
        { id: 'crown', symbol: '👑' },
        { id: 'rainbow', symbol: '🌈' },
        { id: 'diamond', symbol: '💎' },
      ];

      const renderIconGrid = (currentIcon: string, onSelect: (id: string) => void) => (
        <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 12, justifyContent: 'center', marginTop: 12 }}>
          {iconOptions.map(opt => (
            <TouchableOpacity
              key={opt.id}
              onPress={() => onSelect(opt.id)}
              style={{
                width: 56,
                height: 56,
                borderRadius: 12,
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: currentIcon === opt.id ? colors.primary : colors.backgroundSecondary,
                borderWidth: 2,
                borderColor: currentIcon === opt.id ? colors.primaryDark : colors.borderLight,
              }}
            >
              <Text style={{ fontSize: 24 }}>{opt.symbol}</Text>
              {currentIcon === opt.id && (
                <View style={{ position: 'absolute', bottom: -6, backgroundColor: colors.primary, borderRadius: 10, paddingHorizontal: 6, paddingVertical: 2 }}>
                  <Text style={{ color: colors.white, fontSize: 10 }}>Selected</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      );

      return (
        <View style={styles.personalizationStep}>
          <View style={styles.personalizationContent}>
            <Text style={styles.personalizationTitle}>Set up your couple profile</Text>
            <Text style={styles.personalizationSubtitle}>Add names and choose icons for each partner</Text>

            <AuthInput
              value={partner1Name}
              onChangeText={setPartner1Name}
              placeholder="Partner 1 name"
              label="Partner 1 Name"
              style={styles.partnerNameInput}
            />
            {renderIconGrid(partner1Icon, setPartner1Icon)}

            <View style={{ height: 20 }} />

            <AuthInput
              value={partner2Name}
              onChangeText={setPartner2Name}
              placeholder="Partner 2 name"
              label="Partner 2 Name"
              style={styles.partnerNameInput}
            />
            {renderIconGrid(partner2Icon, setPartner2Icon)}
          </View>
        </View>
      );
    }

    // Step 5: Invite Partner (adjusted for welcome screen)
    if (currentStep === onboardingSteps.length + 2) {
      const handleCreateCouple = async () => {
        if (isAuthenticated) {
          const success = await createCouple();
          if (success) {
            await logEvent(USER_EVENTS.ONBOARDING_PARTNER_INVITED);
          }
        }
      };

      const handleCopyCode = async () => {
        const success = await copyInvitationText();
        if (success) {
          setInviteCodeCopied(true);
          setTimeout(() => setInviteCodeCopied(false), 2000);
        }
      };

      return (
        <View style={styles.personalizationStep}>
          <View style={styles.personalizationContent}>
            <Text style={styles.personalizationTitle}>Invite your partner</Text>
            <Text style={styles.personalizationSubtitle}>
              Create a connection code to link your journals together
            </Text>

            {!couple && !skipPartnerInvite && (
              <>
                <TouchableOpacity
                  onPress={handleCreateCouple}
                  disabled={isCreatingCouple || !canCreateCouple()}
                  style={{
                    backgroundColor: colors.primary,
                    paddingVertical: 16,
                    paddingHorizontal: 24,
                    borderRadius: 12,
                    alignItems: 'center',
                    marginTop: 20,
                    opacity: (isCreatingCouple || !canCreateCouple()) ? 0.6 : 1,
                  }}
                >
                  <Text style={{ color: colors.white, fontSize: 16, fontWeight: '600' }}>
                    {isCreatingCouple ? 'Creating...' : 'Create Couple Code'}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => setSkipPartnerInvite(true)}
                  style={{
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    marginTop: 16,
                    alignItems: 'center',
                  }}
                >
                  <Text style={{ color: 'rgba(255,255,255,0.7)', fontSize: 14 }}>
                    Skip for now (invite later)
                  </Text>
                </TouchableOpacity>
              </>
            )}

            {couple && (
              <View style={{ alignItems: 'center', marginTop: 20 }}>
                <View style={{
                  backgroundColor: colors.backgroundSecondary,
                  padding: 20,
                  borderRadius: 16,
                  alignItems: 'center',
                  marginBottom: 16,
                }}>
                  <Text style={{ color: colors.white, fontSize: 18, fontWeight: '700', letterSpacing: 2 }}>
                    {couple.couple_code}
                  </Text>
                  <Text style={{ color: 'rgba(255,255,255,0.7)', fontSize: 12, marginTop: 4 }}>
                    Share this code with your partner
                  </Text>
                </View>

                <TouchableOpacity
                  onPress={handleCopyCode}
                  style={{
                    backgroundColor: inviteCodeCopied ? colors.primary : colors.secondary,
                    paddingVertical: 12,
                    paddingHorizontal: 20,
                    borderRadius: 8,
                    marginBottom: 8,
                  }}
                >
                  <Text style={{ color: colors.white, fontSize: 14, fontWeight: '600' }}>
                    {inviteCodeCopied ? '✓ Copied!' : 'Copy Code'}
                  </Text>
                </TouchableOpacity>

                <Text style={{ color: 'rgba(255,255,255,0.6)', fontSize: 12, textAlign: 'center', marginTop: 8 }}>
                  Your partner can join by entering this code in their app
                </Text>
              </View>
            )}

            {skipPartnerInvite && (
              <View style={{ alignItems: 'center', marginTop: 20 }}>
                <Text style={{ color: colors.primary, fontSize: 16, fontWeight: '600' }}>
                  ✓ You can invite your partner later from Settings
                </Text>
                <TouchableOpacity
                  onPress={() => setSkipPartnerInvite(false)}
                  style={{ paddingVertical: 8, marginTop: 8 }}
                >
                  <Text style={{ color: 'rgba(255,255,255,0.7)', fontSize: 14 }}>
                    Changed your mind? Tap to invite now
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      );
    }

    // Step 6: Weekly ritual schedule (adjusted for welcome screen)
    if (currentStep === onboardingSteps.length + 3) {
      const days: Array<'Sun'|'Mon'|'Tue'|'Wed'|'Thu'|'Fri'|'Sat'> = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'];

      return (
        <View style={styles.personalizationStep}>
          <View style={styles.personalizationContent}>
            <Text style={styles.personalizationTitle}>Pick your weekly ritual</Text>
            <Text style={styles.personalizationSubtitle}>Choose a day and time to check in and journal together</Text>

            <View style={{ flexDirection: 'row', flexWrap: 'wrap', gap: 8, justifyContent: 'center' }}>
              {days.map(d => (
                <TouchableOpacity
                  key={d}
                  onPress={() => setRitualDay(d)}
                  style={{
                    paddingVertical: 10,
                    paddingHorizontal: 14,
                    borderRadius: 20,
                    backgroundColor: ritualDay === d ? colors.secondary : colors.backgroundSecondary,
                    borderWidth: 1,
                    borderColor: ritualDay === d ? colors.secondaryDark : colors.borderLight,
                    margin: 4,
                  }}
                >
                  <Text style={{ color: ritualDay === d ? colors.white : colors.textPrimary, fontWeight: '600' }}>{d}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={{ height: 12 }} />

            <AuthInput
              value={ritualTime}
              onChangeText={setRitualTime}
              placeholder="19:00"
              label="Reminder Time (24h)"
              style={styles.partnerNameInput}
            />

            <TouchableOpacity onPress={() => setRitualReminders(!ritualReminders)} style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginTop: 8 }}>
              <View style={{ width: 22, height: 22, borderRadius: 4, borderWidth: 2, borderColor: colors.primary, alignItems: 'center', justifyContent: 'center', backgroundColor: ritualReminders ? colors.primary : 'transparent' }}>
                {ritualReminders && <Check size={14} color={colors.white} />}
              </View>
              <Text style={{ color: colors.white }}>Send reminders to both partners</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    // Step 7: Journal icon preference (adjusted for welcome screen)
    if (currentStep === onboardingSteps.length + 4) {
      const journalIcons = [
        { id: 'heart', symbol: '❤️' },
        { id: 'book', symbol: '📖' },
        { id: 'sparkle', symbol: '✨' },
        { id: 'star', symbol: '⭐' },
      ];

      return (
        <View style={styles.personalizationStep}>
          <View style={styles.personalizationContent}>
            <Text style={styles.personalizationTitle}>Choose your journal icon</Text>
            <Text style={styles.personalizationSubtitle}>This icon will appear on your journal entries</Text>

            <View style={{ flexDirection: 'row', gap: 12, justifyContent: 'center', marginTop: 12 }}>
              {journalIcons.map(opt => (
                <TouchableOpacity
                  key={opt.id}
                  onPress={() => setJournalIcon(opt.id)}
                  style={{
                    width: 64,
                    height: 64,
                    borderRadius: 16,
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: journalIcon === opt.id ? colors.primary : colors.backgroundSecondary,
                    borderWidth: 2,
                    borderColor: journalIcon === opt.id ? colors.primaryDark : colors.borderLight,
                  }}
                >
                  <Text style={{ fontSize: 28 }}>{opt.symbol}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={{ color: 'rgba(255,255,255,0.8)', marginTop: 16, textAlign: 'center' }}>You can update this anytime in Settings</Text>
          </View>
        </View>
      );
    }

    // Final step: Complete
    return (
      <View style={styles.personalizationStep}>
        <View style={styles.personalizationContent}>
          <Text style={styles.personalizationTitle}>All set!</Text>
          <Text style={styles.personalizationSubtitle}>
            Welcome to Everlasting Us — your space for connection, laughter, and growth.
          </Text>

          {couple && couple.status === 'active' && (
            <View style={{ alignItems: 'center', marginTop: 20 }}>
              <Text style={{ color: colors.primary, fontSize: 16, fontWeight: '600' }}>
                🎉 Your partner has joined!
              </Text>
              <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 14, textAlign: 'center', marginTop: 8 }}>
                You're ready to start your journey together
              </Text>
            </View>
          )}

          {couple && couple.status === 'pending' && (
            <View style={{ alignItems: 'center', marginTop: 20 }}>
              <Text style={{ color: colors.secondary, fontSize: 16, fontWeight: '600' }}>
                ⏳ Waiting for your partner
              </Text>
              <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 14, textAlign: 'center', marginTop: 8 }}>
                They can join anytime using your code: {couple.couple_code}
              </Text>
            </View>
          )}

          {!couple && !skipPartnerInvite && (
            <View style={{ alignItems: 'center', marginTop: 20 }}>
              <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: 14, textAlign: 'center' }}>
                You can invite your partner anytime from Settings
              </Text>
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <AuthScreenLayout
      backgroundColor={
        currentStep === 0 ? colors.primary : // Welcome screen
        currentStep >= 1 && currentStep <= onboardingSteps.length ? onboardingSteps[currentStep - 1].backgroundColor : // Feature slides
        colors.primary // Setup steps
      }
      useKeyboardAvoiding={currentStep === onboardingSteps.length + 1}
      scrollable={false}
    >
      {renderCurrentStep()}
      
      {/* Hide navigation for welcome screen since it has custom button */}
      {currentStep > 0 && (
        <OnboardingNavigation
          currentStep={currentStep}
          totalSteps={TOTAL_STEPS}
          onNext={handleNext}
          onSkip={handleSkip}
          onComplete={handleComplete}
          showSkip={currentStep > 0 && currentStep <= onboardingSteps.length}
          // Disable Next until required fields set in current custom steps (adjusted for welcome screen)
          disabled={
            (currentStep === onboardingSteps.length + 1 && (!partner1Name.trim() || !partner2Name.trim() || !partner1Icon || !partner2Icon)) ||
            (currentStep === onboardingSteps.length + 2 && (!couple && !skipPartnerInvite)) ||
            (currentStep === onboardingSteps.length + 3 && (!ritualTime))
          }
        />
      )}
    </AuthScreenLayout>
  );
}

const styles = StyleSheet.create({
  // Welcome screen styles
  welcomeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  welcomeContent: {
    alignItems: 'center',
  },
  welcomeLogo: {
    fontSize: 48,
    fontWeight: '800',
    color: colors.white,
    textAlign: 'center',
    marginBottom: 10,
    letterSpacing: -1,
  },
  welcomeTagline: {
    fontSize: 18,
    fontWeight: '400',
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
    marginHorizontal: 20,
    marginBottom: 40,
  },
  welcomeButton: {
    backgroundColor: colors.accentPink,
    borderRadius: 25,
    paddingVertical: 18,
    paddingHorizontal: 40,
    shadowColor: colors.accentPink,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 25,
    elevation: 8,
  },
  welcomeButtonText: {
    color: '#8b4a47', // Darker color for contrast on the pink background
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
  },

  // Existing personalization styles
  personalizationStep: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  personalizationContent: {
    alignItems: 'center',
    width: '100%',
  },
  personalizationTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 16,
  },
  personalizationSubtitle: {
    fontSize: 18,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 26,
    marginBottom: 32,
  },
  partnerNameInput: {
    width: '100%',
    marginBottom: 16,
  },
  personalizationNote: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});