/**
 * useAppTheme Hook
 * 
 * Provides theme functionality throughout the app.
 * This is a compatibility layer that bridges the old useAppTheme usage
 * with the new theme system.
 */

import { useGlobalTheme } from '../components/common/ThemeProvider';
import { getCompleteTheme } from '../utils/theme';
import type { ThemeMode } from '../utils/theme';

export const useAppTheme = () => {
  const { currentTheme, isDarkMode } = useGlobalTheme();
  
  // Determine theme mode based on isDarkMode
  const mode: ThemeMode = isDarkMode ? 'dark' : 'light';
  
  // Get complete theme with tokens
  const completeTheme = getCompleteTheme(mode);
  
  return {
    theme: currentTheme,
    completeTheme,
    isDarkMode,
    mode,
    // Legacy compatibility
    colors: currentTheme,
    tokens: completeTheme.tokens,
  };
};

export default useAppTheme;
