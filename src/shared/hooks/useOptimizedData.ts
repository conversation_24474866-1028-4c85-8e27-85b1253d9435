/**
 * Optimized Data Hook
 * Enterprise-grade data fetching with caching, performance monitoring, and N+1 prevention
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth } from '../../journeys/onboarding/useAuth';
import { performanceMonitor } from '../services/performanceMonitor';
import { cacheManager } from '../services/storage/cacheManager';
import { subscriptionManager } from '../services/system/subscriptionManager';
import { logger } from '../utils/logger';

export interface OptimizedDataState<T> {
  data: T | null;
  isLoading: boolean;
  error: string | null;
  isRefreshing: boolean;
  lastUpdated: number | null;
  cacheHit: boolean;
}

export interface OptimizedDataOptions {
  cacheKey?: string;
  cacheTTL?: number;
  enableRealtime?: boolean;
  realtimeTable?: string;
  realtimeFilter?: string;
  batchSize?: number;
  retryAttempts?: number;
  retryDelay?: number;
  enablePrefetch?: boolean;
  prefetchKeys?: string[];
}

export interface DataFetcher<T> {
  (): Promise<T>;
}

/**
 * Enterprise-grade optimized data hook with comprehensive performance features
 */
export function useOptimizedData<T>(
  fetcher: DataFetcher<T>,
  dependencies: any[] = [],
  options: OptimizedDataOptions = {}
): OptimizedDataState<T> & {
  refresh: () => Promise<void>;
  prefetch: (keys: string[]) => Promise<void>;
  invalidateCache: () => void;
  getPerformanceMetrics: () => any;
} {
  const { user } = useAuth();
  const [state, setState] = useState<OptimizedDataState<T>>({
    data: null,
    isLoading: true,
    error: null,
    isRefreshing: false,
    lastUpdated: null,
    cacheHit: false
  });

  const {
    cacheKey = `optimized-data-${JSON.stringify(dependencies)}`,
    cacheTTL = 300000, // 5 minutes
    enableRealtime = false,
    realtimeTable,
    realtimeFilter,
    batchSize = 100,
    retryAttempts = 3,
    retryDelay = 1000,
    enablePrefetch = false,
    prefetchKeys = []
  } = options;

  const componentTracker = useRef(performanceMonitor.trackComponent('useOptimizedData'));
  const subscriptionId = useRef<string | null>(null);
  const retryCount = useRef(0);
  const abortController = useRef<AbortController | null>(null);

  /**
   * Optimized data fetching with caching and performance monitoring
   */
  const fetchData = useCallback(async (isRefresh = false): Promise<void> => {
    // Cancel any ongoing requests
    if (abortController.current) {
      abortController.current.abort();
    }
    abortController.current = new AbortController();

    componentTracker.current.startRender();
    const networkTracker = performanceMonitor.trackNetworkRequest(`/api/${cacheKey}`);
    networkTracker.start();

    try {
      setState(prev => ({
        ...prev,
        isLoading: !isRefresh,
        isRefreshing: isRefresh,
        error: null
      }));

      // Check cache first (unless refreshing)
      if (!isRefresh) {
        const cachedData = await cacheManager.get<T>(cacheKey);
        if (cachedData !== null) {
          setState(prev => ({
            ...prev,
            data: cachedData,
            isLoading: false,
            isRefreshing: false,
            lastUpdated: Date.now(),
            cacheHit: true
          }));
          networkTracker.end(true, 0); // Cache hit
          componentTracker.current.endRender();
          logger.info(`Data loaded from cache: ${cacheKey}`);
          return;
        }
      }

      // Fetch fresh data
      const startTime = Date.now();
      const data = await fetcher();
      const fetchDuration = Date.now() - startTime;

      // Cache the results
      await cacheManager.set(cacheKey, data, {
        ttl: cacheTTL,
        tags: [user?.id || 'anonymous', 'optimized-data']
      });

      setState(prev => ({
        ...prev,
        data,
        isLoading: false,
        isRefreshing: false,
        error: null,
        lastUpdated: Date.now(),
        cacheHit: false
      }));

      // Record performance metrics
      performanceMonitor.recordMetric(
        'data.fetch.success',
        fetchDuration
      );

      networkTracker.end(true, JSON.stringify(data).length);
      retryCount.current = 0; // Reset retry count on success

      logger.info(`Data fetched successfully: ${cacheKey} (${fetchDuration}ms)`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Implement retry logic
      if (retryCount.current < retryAttempts && !abortController.current?.signal.aborted) {
        retryCount.current++;
        logger.warn(`Fetch failed, retrying (${retryCount.current}/${retryAttempts}): ${errorMessage}`);

        setTimeout(() => {
          fetchData(isRefresh);
        }, retryDelay * retryCount.current);
        return;
      }

      logger.error(`Error fetching data: ${cacheKey}`, error);

      setState(prev => ({
        ...prev,
        isLoading: false,
        isRefreshing: false,
        error: errorMessage
      }));

      // Record error metrics
      performanceMonitor.recordMetric(
        'data.fetch.error',
        Date.now() - Date.now()
      );

      networkTracker.end(false);
    } finally {
      componentTracker.current.endRender();
    }
  }, [fetcher, cacheKey, cacheTTL, user, retryAttempts, retryDelay]);

  /**
   * Setup real-time subscriptions for data updates
   */
  const setupRealtime = useCallback(() => {
    if (!enableRealtime || !realtimeTable) return;

    subscriptionId.current = subscriptionManager.subscribe({
      table: realtimeTable,
      event: '*',
      filter: realtimeFilter,
      callback: (payload) => {
        logger.info(`Real-time update for ${cacheKey}:`, payload);

        // Invalidate cache and refresh data
        cacheManager.invalidate(cacheKey);
        fetchData(true);
      },
      onError: (error) => {
        logger.error(`Subscription error for ${cacheKey}:`, error);
      },
      autoReconnect: true,
      maxReconnectAttempts: 5
    });
  }, [enableRealtime, realtimeTable, realtimeFilter, cacheKey, fetchData]);

  /**
   * Cleanup subscriptions and abort requests
   */
  const cleanup = useCallback(() => {
    if (subscriptionId.current) {
      subscriptionManager.unsubscribe(subscriptionId.current);
      subscriptionId.current = null;
    }

    if (abortController.current) {
      abortController.current.abort();
      abortController.current = null;
    }
  }, []);

  /**
   * Prefetch related data to warm cache
   */
  const prefetch = useCallback(async (keys: string[]): Promise<void> => {
    if (!enablePrefetch || keys.length === 0) return;

    logger.info(`Prefetching ${keys.length} related data items`);

    await cacheManager.warmCache({
      keys,
      dataLoader: async (key) => {
        // This would need to be customized based on your data structure
        return fetcher();
      },
      priority: 'low'
    });
  }, [enablePrefetch, fetcher]);

  /**
   * Manual refresh function
   */
  const refresh = useCallback(async (): Promise<void> => {
    await fetchData(true);
  }, [fetchData]);

  /**
   * Invalidate cache manually
   */
  const invalidateCache = useCallback(() => {
    cacheManager.invalidate(cacheKey);
  }, [cacheKey]);

  /**
   * Get performance metrics for this hook
   */
  const getPerformanceMetrics = useCallback(() => {
    return {
      cacheMetrics: cacheManager.getMetrics(),
      subscriptionMetrics: subscriptionManager.getHealthMetrics(),
      componentMetrics: componentTracker.current
    };
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchData();
    setupRealtime();

    // Prefetch related data if enabled
    if (enablePrefetch && prefetchKeys.length > 0) {
      prefetch(prefetchKeys);
    }

    return cleanup;
  }, dependencies);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    ...state,
    refresh,
    prefetch,
    invalidateCache,
    getPerformanceMetrics
  };
}

/**
 * Batch data fetching hook to prevent N+1 queries
 */
export function useBatchOptimizedData<T>(
  batchFetcher: (ids: string[]) => Promise<T[]>,
  ids: string[],
  options: OptimizedDataOptions = {}
): OptimizedDataState<T[]> & {
  refresh: () => Promise<void>;
  addIds: (newIds: string[]) => void;
  removeIds: (removeIds: string[]) => void;
} {
  const [currentIds, setCurrentIds] = useState<string[]>(ids);

  const batchedFetcher = useCallback(async (): Promise<T[]> => {
    if (currentIds.length === 0) return [];

    // Use performance optimization service for batch operations
    const batchSize = options.batchSize || 100;
    const batches = [];

    for (let i = 0; i < currentIds.length; i += batchSize) {
      batches.push(currentIds.slice(i, i + batchSize));
    }

    const results = await Promise.all(
      batches.map(batch => batchFetcher(batch))
    );

    return results.flat();
  }, [batchFetcher, currentIds, options.batchSize]);

  const optimizedData = useOptimizedData(
    batchedFetcher,
    [currentIds],
    {
      ...options,
      cacheKey: `batch-${options.cacheKey || 'data'}-${currentIds.join(',')}`
    }
  );

  const addIds = useCallback((newIds: string[]) => {
    setCurrentIds(prev => [...new Set([...prev, ...newIds])]);
  }, []);

  const removeIds = useCallback((removeIds: string[]) => {
    setCurrentIds(prev => prev.filter(id => !removeIds.includes(id)));
  }, []);

  return {
    ...optimizedData,
    addIds,
    removeIds
  };
}

/**
 * Infinite scroll optimized data hook
 */
export function useInfiniteOptimizedData<T>(
  fetcher: (cursor?: string, limit?: number) => Promise<{ data: T[]; nextCursor?: string; hasMore: boolean }>,
  options: OptimizedDataOptions & { pageSize?: number } = {}
): OptimizedDataState<T[]> & {
  loadMore: () => Promise<void>;
  hasMore: boolean;
  isLoadingMore: boolean;
  reset: () => void;
} {
  const [pages, setPages] = useState<T[]>([]);
  const [nextCursor, setNextCursor] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const { pageSize = 50 } = options;

  const paginatedFetcher = useCallback(async (): Promise<T[]> => {
    const result = await fetcher(undefined, pageSize);
    setNextCursor(result.nextCursor);
    setHasMore(result.hasMore);
    return result.data;
  }, [fetcher, pageSize]);

  const optimizedData = useOptimizedData(
    paginatedFetcher,
    [],
    {
      ...options,
      cacheKey: `infinite-${options.cacheKey || 'data'}-page-0`
    }
  );

  const loadMore = useCallback(async (): Promise<void> => {
    if (!hasMore || isLoadingMore || !nextCursor) return;

    setIsLoadingMore(true);
    try {
      const result = await fetcher(nextCursor, pageSize);
      setPages(prev => [...prev, ...result.data]);
      setNextCursor(result.nextCursor);
      setHasMore(result.hasMore);
    } catch (error) {
      logger.error('Error loading more data:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [fetcher, nextCursor, hasMore, isLoadingMore, pageSize]);

  const reset = useCallback(() => {
    setPages([]);
    setNextCursor(undefined);
    setHasMore(true);
    optimizedData.refresh();
  }, [optimizedData]);

  // Combine initial data with additional pages
  const allData = optimizedData.data ? [...optimizedData.data, ...pages] : pages;

  return {
    ...optimizedData,
    data: allData,
    loadMore,
    hasMore,
    isLoadingMore,
    reset
  };
}
