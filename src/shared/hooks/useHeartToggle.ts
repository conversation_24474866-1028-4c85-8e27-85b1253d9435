import { useCallback, useRef, useState } from 'react';
import { logger } from '../utils/logger';

export interface UseHeartToggleOptions {
  /** Initial favorited state */
  initialFavorited?: boolean;
  /** Debounce delay in milliseconds */
  debounceMs?: number;
  /** Callback when toggle completes successfully */
  onSuccess?: (isFavorited: boolean) => void;
  /** Callback when toggle fails */
  onError?: (error: Error) => void;
}

export interface UseHeartToggleReturn {
  /** Current favorited state (optimistic) */
  isFavorited: boolean;
  /** Whether a toggle operation is in progress */
  isLoading: boolean;
  /** Function to toggle the heart state */
  toggle: (newState: boolean) => Promise<void>;
  /** Function to set the heart state directly */
  setFavorited: (isFavorited: boolean) => void;
}

/**
 * Hook for managing heart/favorite toggle state with optimistic updates and debouncing
 */
export const useHeartToggle = (
  onToggle: (isFavorited: boolean) => Promise<void>,
  options: UseHeartToggleOptions = {}
): UseHeartToggleReturn => {
  const {
    initialFavorited = false,
    debounceMs = 300,
    onSuccess,
    onError,
  } = options;

  const [isFavorited, setIsFavorited] = useState(initialFavorited);
  const [isLoading, setIsLoading] = useState(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastToggleRef = useRef<boolean | null>(null);

  const setFavorited = useCallback((newState: boolean) => {
    setIsFavorited(newState);
  }, []);

  const toggle = useCallback(async (newState: boolean) => {
    // Clear any existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // If we're already loading and the state is the same, ignore
    if (isLoading && lastToggleRef.current === newState) {
      return;
    }

    // Store the last toggle state
    lastToggleRef.current = newState;

    // Optimistic update
    setIsFavorited(newState);
    setIsLoading(true);

    try {
      // Debounce the actual API call
      await new Promise<void>((resolve) => {
        debounceTimeoutRef.current = setTimeout(async () => {
          try {
            await onToggle(newState);
            onSuccess?.(newState);
            resolve();
          } catch (error) {
            // Rollback optimistic update on error
            setIsFavorited(!newState);
            const errorObj = error instanceof Error ? error : new Error('Toggle failed');
            onError?.(errorObj);
            logger.error('Heart toggle failed:', errorObj);
            throw errorObj;
          }
        }, debounceMs);
      });
    } catch (error) {
      // Error handling is done in the timeout above
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, onToggle, debounceMs, onSuccess, onError]);

  return {
    isFavorited,
    isLoading,
    toggle,
    setFavorited,
  };
};

export default useHeartToggle;
