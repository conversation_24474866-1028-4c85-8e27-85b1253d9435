import { Platform } from 'react-native';
import { colors } from './colors';

// Design tokens and helpers for the Nestled design system
export const radii = {
  xs: 6,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 30,
  pill: 999,
} as const;

// spacing moved to theme system

export const shadows = {
  sm: Platform.select({
    ios: { shadowColor: colors.shadow, shadowOpacity: 0.08, shadowOffset: { width: 0, height: 2 }, shadowRadius: 6 },
    android: { elevation: 2 },
    default: {},
  }),
  md: Platform.select({
    ios: { shadowColor: colors.shadow, shadowOpacity: 0.1, shadowOffset: { width: 0, height: 6 }, shadowRadius: 12 },
    android: { elevation: 4 },
    default: {},
  }),
  lg: Platform.select({
    ios: { shadowColor: colors.shadow, shadowOpacity: 0.15, shadowOffset: { width: 0, height: 12 }, shadowRadius: 24 },
    android: { elevation: 6 },
    default: {},
  }),
} as const;

export const borders = {
  hairline: 0.5,
  thin: 1,
  thick: 2,
} as const;

// translucency moved to theme system

export const brand = {
  primary: colors.greenSage,      // #9CAF88
  secondary: colors.lavenderPurple, // #CBC3E3
  pink: colors.accentPink,        // #F3E0DA
  darkerPink: colors.darkerPink,  // #F7D0D6
  text: colors.charcoalGray,      // #393939
  white: colors.white,
  black: colors.black,
} as const;

export type Tone = 'sage' | 'lavender' | 'pink' | 'neutral' | 'warning' | 'success' | 'info';

export function toneToColor(tone: Tone, variant: 'solid' | 'soft' | 'outline' = 'soft') {
  switch (tone) {
    case 'sage':
      return variant === 'solid' ? brand.primary : brand.primary;
    case 'lavender':
      return brand.secondary;
    case 'pink':
      return brand.pink;
    case 'warning':
      return '#FFD700';
    case 'success':
      return brand.primary;
    case 'info':
      return brand.secondary;
    default:
      return brand.text;
  }
}

// Helper for glass-like background (no blur dependency)
export function getGlassStyle(opacity: number = 0.8) {
  return {
    backgroundColor: `rgba(255,255,255,${opacity})`,
    borderWidth: borders.thin,
    borderColor: 'rgba(0,0,0,0.05)',
  } as const;
}

export const ds = { radii, shadows, borders, brand };
export default ds;

