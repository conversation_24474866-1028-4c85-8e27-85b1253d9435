import { StyleSheet } from 'react-native';
import { colors } from './colors';

// Common card styles used across multiple components
export const cardStyles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 12,
  },
});

// Common button styles
export const buttonStyles = StyleSheet.create({
  primary: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  gradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  text: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '600',
    marginRight: 8,
  },
});

// Common input styles
export const inputStyles = StyleSheet.create({
  container: {
    borderWidth: 2,
    borderColor: colors.borderLight,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: colors.backgroundSecondary,
    marginBottom: 20,
    minHeight: 80,
  },
  list: {
    minHeight: 100,
  },
});

// Common avatar styles
export const avatarStyles = StyleSheet.create({
  small: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  medium: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
});

// Common text styles
export const textStyles = StyleSheet.create({
  primary: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  secondary: {
    fontSize: 16,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  caption: {
    fontSize: 14,
    color: colors.textTertiary,
    lineHeight: 20,
  },
  label: {
    fontSize: 14,
    color: colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 8,
  },
});

// Common animation configurations
export const animationConfig = {
  fast: { duration: 200, useNativeDriver: true },
  medium: { duration: 300, useNativeDriver: true },
  slow: { duration: 600, useNativeDriver: true },
};
