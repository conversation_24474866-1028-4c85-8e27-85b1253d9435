/**
 * Enterprise Accessibility Management System
 * WCAG 2.1 AA compliant accessibility features with screen reader support
 */

import { AccessibilityInfo, Platform } from 'react-native';
import { enterpriseLogger } from '../logging/EnterpriseLogger';

// =============================================================================
// ACCESSIBILITY TYPES & INTERFACES
// =============================================================================

export enum AccessibilityLevel {
  A = 'A',
  AA = 'AA',
  AAA = 'AAA'
}

export enum AccessibilityRole {
  BUTTON = 'button',
  LINK = 'link',
  TEXT = 'text',
  HEADING = 'header',
  IMAGE = 'image',
  LIST = 'list',
  LIST_ITEM = 'listitem',
  TAB = 'tab',
  TAB_LIST = 'tablist',
  TAB_PANEL = 'tabpanel',
  DIALOG = 'dialog',
  ALERT = 'alert',
  MENU = 'menu',
  MENU_ITEM = 'menuitem',
  SEARCH = 'search',
  FORM = 'form',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  SWITCH = 'switch',
  SLIDER = 'slider',
  PROGRESS = 'progressbar'
}

export interface AccessibilityProps {
  accessible?: boolean;
  accessibilityRole?: AccessibilityRole;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityValue?: {
    min?: number;
    max?: number;
    now?: number;
    text?: string;
  };
  accessibilityState?: {
    disabled?: boolean;
    selected?: boolean;
    checked?: boolean | 'mixed';
    busy?: boolean;
    expanded?: boolean;
  };
  accessibilityActions?: Array<{
    name: string;
    label?: string;
  }>;
  onAccessibilityAction?: (event: { nativeEvent: { actionName: string } }) => void;
  accessibilityLiveRegion?: 'none' | 'polite' | 'assertive';
  accessibilityElementsHidden?: boolean;
  importantForAccessibility?: 'auto' | 'yes' | 'no' | 'no-hide-descendants';
}

export interface ColorContrastResult {
  ratio: number;
  level: AccessibilityLevel;
  passes: boolean;
  recommendation?: string;
}

export interface AccessibilityAuditResult {
  component: string;
  issues: AccessibilityIssue[];
  score: number;
  level: AccessibilityLevel;
}

export interface AccessibilityIssue {
  type: 'error' | 'warning' | 'info';
  rule: string;
  message: string;
  element?: string;
  suggestion?: string;
}

// =============================================================================
// ACCESSIBILITY MANAGER
// =============================================================================

class AccessibilityManager {
  private static instance: AccessibilityManager;
  private isScreenReaderEnabled = false;
  private isReduceMotionEnabled = false;
  private isHighContrastEnabled = false;
  private fontSize = 16;
  private listeners: Set<(settings: AccessibilitySettings) => void> = new Set();

  private constructor() {
    this.initializeAccessibilitySettings();
    this.setupAccessibilityListeners();
  }

  public static getInstance(): AccessibilityManager {
    if (!AccessibilityManager.instance) {
      AccessibilityManager.instance = new AccessibilityManager();
    }
    return AccessibilityManager.instance;
  }

  /**
   * Get current accessibility settings
   */
  public getSettings(): AccessibilitySettings {
    return {
      screenReaderEnabled: this.isScreenReaderEnabled,
      reduceMotionEnabled: this.isReduceMotionEnabled,
      highContrastEnabled: this.isHighContrastEnabled,
      fontSize: this.fontSize,
      voiceOverEnabled: this.isScreenReaderEnabled && Platform.OS === 'ios',
      talkBackEnabled: this.isScreenReaderEnabled && Platform.OS === 'android'
    };
  }

  /**
   * Check if screen reader is enabled
   */
  public isScreenReaderActive(): boolean {
    return this.isScreenReaderEnabled;
  }

  /**
   * Check if reduce motion is enabled
   */
  public shouldReduceMotion(): boolean {
    return this.isReduceMotionEnabled;
  }

  /**
   * Get recommended font size
   */
  public getRecommendedFontSize(baseFontSize: number): number {
    const scaleFactor = this.fontSize / 16; // 16 is default font size
    return Math.round(baseFontSize * scaleFactor);
  }

  /**
   * Generate accessibility props for components
   */
  public generateProps(config: {
    role: AccessibilityRole;
    label: string;
    hint?: string;
    value?: string | number;
    state?: {
      disabled?: boolean;
      selected?: boolean;
      checked?: boolean | 'mixed';
      busy?: boolean;
      expanded?: boolean;
    };
    actions?: Array<{ name: string; label?: string }>;
    liveRegion?: 'none' | 'polite' | 'assertive';
  }): AccessibilityProps {
    const props: AccessibilityProps = {
      accessible: true,
      accessibilityRole: config.role,
      accessibilityLabel: config.label
    };

    if (config.hint) {
      props.accessibilityHint = config.hint;
    }

    if (config.value !== undefined) {
      props.accessibilityValue = {
        text: String(config.value)
      };
    }

    if (config.state) {
      props.accessibilityState = config.state;
    }

    if (config.actions) {
      props.accessibilityActions = config.actions;
    }

    if (config.liveRegion) {
      props.accessibilityLiveRegion = config.liveRegion;
    }

    return props;
  }

  /**
   * Calculate color contrast ratio
   */
  public calculateColorContrast(
    foreground: string,
    background: string
  ): ColorContrastResult {
    const fgLuminance = this.getLuminance(foreground);
    const bgLuminance = this.getLuminance(background);

    const ratio = (Math.max(fgLuminance, bgLuminance) + 0.05) /
                  (Math.min(fgLuminance, bgLuminance) + 0.05);

    let level: AccessibilityLevel;
    let passes: boolean;
    let recommendation: string | undefined;

    if (ratio >= 7) {
      level = AccessibilityLevel.AAA;
      passes = true;
    } else if (ratio >= 4.5) {
      level = AccessibilityLevel.AA;
      passes = true;
    } else if (ratio >= 3) {
      level = AccessibilityLevel.A;
      passes = false;
      recommendation = 'Consider using colors with higher contrast for better accessibility';
    } else {
      level = AccessibilityLevel.A;
      passes = false;
      recommendation = 'Colors do not meet accessibility standards. Please choose higher contrast colors';
    }

    return { ratio, level, passes, recommendation };
  }

  /**
   * Audit component for accessibility issues
   */
  public auditComponent(
    componentName: string,
    props: any,
    children?: any[]
  ): AccessibilityAuditResult {
    const issues: AccessibilityIssue[] = [];
    let score = 100;

    // Check for missing accessibility label
    if (!props.accessibilityLabel && !props.children) {
      issues.push({
        type: 'error',
        rule: 'missing-label',
        message: 'Component is missing accessibility label',
        suggestion: 'Add accessibilityLabel prop'
      });
      score -= 20;
    }

    // Check for interactive elements without proper role
    if (props.onPress && !props.accessibilityRole) {
      issues.push({
        type: 'warning',
        rule: 'missing-role',
        message: 'Interactive element is missing accessibility role',
        suggestion: 'Add accessibilityRole="button" or appropriate role'
      });
      score -= 10;
    }

    // Check for images without alt text
    if (props.source && !props.accessibilityLabel) {
      issues.push({
        type: 'error',
        rule: 'missing-alt-text',
        message: 'Image is missing alternative text',
        suggestion: 'Add accessibilityLabel with descriptive text'
      });
      score -= 15;
    }

    // Check for color contrast (if colors are provided)
    if (props.style?.color && props.style?.backgroundColor) {
      const contrast = this.calculateColorContrast(
        props.style.color,
        props.style.backgroundColor
      );

      if (!contrast.passes) {
        issues.push({
          type: 'warning',
          rule: 'low-contrast',
          message: `Color contrast ratio ${contrast.ratio.toFixed(2)} is below recommended standards`,
          suggestion: contrast.recommendation
        });
        score -= 15;
      }
    }

    // Check for minimum touch target size
    if (props.onPress && props.style) {
      const { width, height } = props.style;
      const minSize = 44; // iOS HIG and Android Material Design minimum

      if ((width && width < minSize) || (height && height < minSize)) {
        issues.push({
          type: 'warning',
          rule: 'small-touch-target',
          message: 'Touch target is smaller than recommended minimum (44x44)',
          suggestion: 'Increase touch target size or add padding'
        });
        score -= 10;
      }
    }

    // Determine accessibility level based on score
    let level: AccessibilityLevel;
    if (score >= 90) {
      level = AccessibilityLevel.AAA;
    } else if (score >= 70) {
      level = AccessibilityLevel.AA;
    } else {
      level = AccessibilityLevel.A;
    }

    return {
      component: componentName,
      issues,
      score: Math.max(0, score),
      level
    };
  }

  /**
   * Announce message to screen reader
   */
  public announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    if (!this.isScreenReaderEnabled) return;

    try {
      if (Platform.OS === 'ios') {
        AccessibilityInfo.announceForAccessibility(message);
      } else if (Platform.OS === 'android') {
        AccessibilityInfo.announceForAccessibility(message);
      }

      enterpriseLogger.debug('Accessibility announcement made', { message, priority });
    } catch (error) {
      enterpriseLogger.error('Failed to make accessibility announcement', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * Focus on element (for screen readers)
   */
  public focusOnElement(elementRef: any): void {
    if (!this.isScreenReaderEnabled || !elementRef?.current) return;

    try {
      AccessibilityInfo.setAccessibilityFocus(elementRef.current);
      enterpriseLogger.debug('Accessibility focus set');
    } catch (error) {
      enterpriseLogger.error('Failed to set accessibility focus', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * Add accessibility settings listener
   */
  public addListener(listener: (settings: AccessibilitySettings) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Get accessibility-friendly animation duration
   */
  public getAnimationDuration(baseDuration: number): number {
    return this.isReduceMotionEnabled ? 0 : baseDuration;
  }

  /**
   * Get accessibility-friendly colors
   */
  public getAccessibleColors(theme: any): any {
    if (!this.isHighContrastEnabled) return theme;

    // Return high contrast version of theme
    return {
      ...theme,
      text: '#000000',
      background: '#FFFFFF',
      primary: '#0000FF',
      error: '#FF0000',
      success: '#008000',
      warning: '#FFA500'
    };
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private async initializeAccessibilitySettings(): Promise<void> {
    try {
      // Check screen reader status
      this.isScreenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();

      // Check reduce motion (iOS only)
      if (Platform.OS === 'ios') {
        this.isReduceMotionEnabled = await AccessibilityInfo.isReduceMotionEnabled();
      }

      // Log initial settings
      enterpriseLogger.info('Accessibility settings initialized', {
        screenReader: this.isScreenReaderEnabled,
        reduceMotion: this.isReduceMotionEnabled,
        platform: Platform.OS
      });

      this.notifyListeners();
    } catch (error) {
      enterpriseLogger.error('Failed to initialize accessibility settings', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  private setupAccessibilityListeners(): void {
    // Listen for screen reader changes
    AccessibilityInfo.addEventListener('screenReaderChanged', (enabled) => {
      this.isScreenReaderEnabled = enabled;
      enterpriseLogger.info('Screen reader status changed', { enabled });
      this.notifyListeners();
    });

    // Listen for reduce motion changes (iOS only)
    if (Platform.OS === 'ios') {
      AccessibilityInfo.addEventListener('reduceMotionChanged', (enabled) => {
        this.isReduceMotionEnabled = enabled;
        enterpriseLogger.info('Reduce motion status changed', { enabled });
        this.notifyListeners();
      });
    }
  }

  private notifyListeners(): void {
    const settings = this.getSettings();
    this.listeners.forEach(listener => {
      try {
        listener(settings);
      } catch (error) {
        enterpriseLogger.error('Accessibility listener error', { error: error instanceof Error ? error.message : String(error) });
      }
    });
  }

  private getLuminance(color: string): number {
    // Convert hex color to RGB
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    // Calculate relative luminance
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  }
}

// =============================================================================
// TYPES
// =============================================================================

export interface AccessibilitySettings {
  screenReaderEnabled: boolean;
  reduceMotionEnabled: boolean;
  highContrastEnabled: boolean;
  fontSize: number;
  voiceOverEnabled: boolean;
  talkBackEnabled: boolean;
}

// =============================================================================
// EXPORTS
// =============================================================================

export { AccessibilityManager };
export const accessibilityManager = AccessibilityManager.getInstance();

// Convenience functions
export const getAccessibilityProps = (config: Parameters<typeof accessibilityManager.generateProps>[0]) =>
  accessibilityManager.generateProps(config);

export const announceToScreenReader = (message: string, priority?: 'polite' | 'assertive') =>
  accessibilityManager.announce(message, priority);

export const isScreenReaderActive = () => accessibilityManager.isScreenReaderActive();
export const shouldReduceMotion = () => accessibilityManager.shouldReduceMotion();
export const getAccessibleAnimationDuration = (duration: number) =>
  accessibilityManager.getAnimationDuration(duration);
