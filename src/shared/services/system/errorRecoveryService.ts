/**
 * Enterprise Error Recovery Service
 * Intelligent error recovery with retry logic, fallback states, and user guidance
 */

import { router } from 'expo-router';
import { AppError, ErrorCategory } from '../../utils/errorHandler';
import { enterpriseLogger } from '../logging/enterpriseLogger';

// =============================================================================
// RECOVERY TYPES & INTERFACES
// =============================================================================

export enum RecoveryStrategy {
  RETRY = 'retry',
  FALLBACK = 'fallback',
  REDIRECT = 'redirect',
  REFRESH = 'refresh',
  OFFLINE_MODE = 'offline_mode',
  CACHE_FALLBACK = 'cache_fallback',
  ALTERNATIVE_ENDPOINT = 'alternative_endpoint',
  USER_INTERVENTION = 'user_intervention'
}

export enum RecoveryPriority {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  CRITICAL = 4
}

export interface RecoveryAction {
  strategy: RecoveryStrategy;
  priority: RecoveryPriority;
  description: string;
  execute: () => Promise<boolean>;
  canExecute?: () => boolean;
  userMessage?: string;
  requiresUserConsent?: boolean;
  maxAttempts?: number;
  cooldownMs?: number;
}

export interface RecoveryPlan {
  errorCode: string;
  category: ErrorCategory;
  actions: RecoveryAction[];
  fallbackMessage: string;
  preventRecurrence?: () => Promise<void>;
}

export interface RecoveryContext {
  userId?: string;
  sessionId?: string;
  component?: string;
  operation?: string;
  data?: unknown;
  timestamp: number;
  attemptCount: number;
  lastAttempt?: number;
}

export interface RecoveryResult {
  success: boolean;
  strategy: RecoveryStrategy;
  message: string;
  data?: unknown;
  requiresUserAction?: boolean;
  nextSteps?: string[];
}

// =============================================================================
// ERROR RECOVERY SERVICE
// =============================================================================

class ErrorRecoveryService {
  private static instance: ErrorRecoveryService;
  private recoveryPlans: Map<string, RecoveryPlan> = new Map();
  private recoveryHistory: Map<string, RecoveryContext[]> = new Map();
  private activeRecoveries: Set<string> = new Set();

  private constructor() {
    this.setupDefaultRecoveryPlans();
  }

  public static getInstance(): ErrorRecoveryService {
    if (!ErrorRecoveryService.instance) {
      ErrorRecoveryService.instance = new ErrorRecoveryService();
    }
    return ErrorRecoveryService.instance;
  }

  /**
   * Attempt to recover from an error
   */
  public async recoverFromError(
    error: AppError,
    context: Partial<RecoveryContext> = {}
  ): Promise<RecoveryResult> {
    const recoveryKey = this.getRecoveryKey(error, context);

    // Prevent concurrent recovery attempts for the same error
    if (this.activeRecoveries.has(recoveryKey)) {
      return {
        success: false,
        strategy: RecoveryStrategy.USER_INTERVENTION,
        message: 'Recovery already in progress',
        requiresUserAction: true
      };
    }

    this.activeRecoveries.add(recoveryKey);

    try {
      const fullContext: RecoveryContext = {
        timestamp: Date.now(),
        attemptCount: 0,
        ...context
      };

      // Update recovery history
      this.updateRecoveryHistory(recoveryKey, fullContext);

      // Get recovery plan
      const plan = this.getRecoveryPlan(error);
      if (!plan) {
        return this.createGenericRecoveryResult(error);
      }

      // Execute recovery actions in priority order
      const sortedActions = plan.actions.sort((a, b) => b.priority - a.priority);

      for (const action of sortedActions) {
        if (action.canExecute && !action.canExecute()) {
          continue;
        }

        // Check cooldown
        if (this.isInCooldown(recoveryKey, action)) {
          continue;
        }

        // Check max attempts
        if (this.hasExceededMaxAttempts(recoveryKey, action)) {
          continue;
        }

        try {
          enterpriseLogger.info(`Attempting recovery strategy: ${action.strategy}`, {
            errorCode: error.code,
            strategy: action.strategy,
            context: fullContext
          });

          const success = await action.execute();

          if (success) {
            enterpriseLogger.info(`Recovery successful: ${action.strategy}`, {
              errorCode: error.code,
              strategy: action.strategy
            });

            // Execute prevention measures if available
            if (plan.preventRecurrence) {
              try {
                await plan.preventRecurrence();
              } catch (preventionError) {
                enterpriseLogger.warn('Error prevention failed', { error: preventionError instanceof Error ? preventionError.message : String(preventionError) });
              }
            }

            return {
              success: true,
              strategy: action.strategy,
              message: action.userMessage || action.description,
              requiresUserAction: action.requiresUserConsent
            };
          }
        } catch (recoveryError) {
          enterpriseLogger.error(`Recovery strategy failed: ${action.strategy}`, { error: recoveryError });
        }
      }

      // All recovery strategies failed
      return {
        success: false,
        strategy: RecoveryStrategy.USER_INTERVENTION,
        message: plan.fallbackMessage,
        requiresUserAction: true,
        nextSteps: this.generateNextSteps(error, plan)
      };

    } finally {
      this.activeRecoveries.delete(recoveryKey);
    }
  }

  /**
   * Register custom recovery plan
   */
  public registerRecoveryPlan(errorCode: string, plan: RecoveryPlan): void {
    this.recoveryPlans.set(errorCode, plan);
    enterpriseLogger.debug(`Recovery plan registered for error: ${errorCode}`);
  }

  /**
   * Get recovery statistics
   */
  public getRecoveryStats(): {
    totalRecoveries: number;
    successRate: number;
    strategiesUsed: Record<RecoveryStrategy, number>;
    mostCommonErrors: Array<{ code: string; count: number }>;
  } {
    const stats = {
      totalRecoveries: 0,
      successRate: 0,
      strategiesUsed: {} as Record<RecoveryStrategy, number>,
      mostCommonErrors: [] as Array<{ code: string; count: number }>
    };

    // Initialize strategy counters
    Object.values(RecoveryStrategy).forEach(strategy => {
      stats.strategiesUsed[strategy] = 0;
    });

    // Calculate statistics from recovery history
    let successfulRecoveries = 0;
    const errorCounts: Record<string, number> = {};

    this.recoveryHistory.forEach((contexts, key) => {
      const [errorCode] = key.split(':');
      errorCounts[errorCode] = (errorCounts[errorCode] || 0) + contexts.length;
      stats.totalRecoveries += contexts.length;
    });

    // Sort most common errors
    stats.mostCommonErrors = Object.entries(errorCounts)
      .map(([code, count]) => ({ code, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    stats.successRate = stats.totalRecoveries > 0
      ? (successfulRecoveries / stats.totalRecoveries) * 100
      : 0;

    return stats;
  }

  /**
   * Clear recovery history (for privacy/memory management)
   */
  public clearRecoveryHistory(olderThanMs?: number): void {
    const cutoff = olderThanMs ? Date.now() - olderThanMs : 0;

    this.recoveryHistory.forEach((contexts, key) => {
      if (olderThanMs) {
        const filteredContexts = contexts.filter(ctx => ctx.timestamp > cutoff);
        if (filteredContexts.length > 0) {
          this.recoveryHistory.set(key, filteredContexts);
        } else {
          this.recoveryHistory.delete(key);
        }
      } else {
        this.recoveryHistory.delete(key);
      }
    });

    enterpriseLogger.info('Recovery history cleared', { cutoff });
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private setupDefaultRecoveryPlans(): void {
    // Network error recovery
    this.registerRecoveryPlan('NETWORK_ERROR', {
      errorCode: 'NETWORK_ERROR',
      category: ErrorCategory.NETWORK,
      actions: [
        {
          strategy: RecoveryStrategy.RETRY,
          priority: RecoveryPriority.HIGH,
          description: 'Retry network request',
          userMessage: 'Retrying connection...',
          maxAttempts: 3,
          cooldownMs: 2000,
          execute: async () => {
            // Exponential backoff retry logic would go here
            await new Promise(resolve => setTimeout(resolve, 1000));
            return Math.random() > 0.3; // Simulate 70% success rate
          }
        },
        {
          strategy: RecoveryStrategy.CACHE_FALLBACK,
          priority: RecoveryPriority.MEDIUM,
          description: 'Use cached data',
          userMessage: 'Loading from cache...',
          execute: async () => {
            // Cache fallback logic would go here
            return true;
          }
        },
        {
          strategy: RecoveryStrategy.OFFLINE_MODE,
          priority: RecoveryPriority.LOW,
          description: 'Switch to offline mode',
          userMessage: 'Switching to offline mode',
          requiresUserConsent: true,
          execute: async () => {
            // Offline mode activation would go here
            return true;
          }
        }
      ],
      fallbackMessage: 'Unable to connect. Please check your internet connection and try again.',
      preventRecurrence: async () => {
        // Network monitoring setup would go here
      }
    });

    // Authentication error recovery
    this.registerRecoveryPlan('AUTH_SESSION_EXPIRED', {
      errorCode: 'AUTH_SESSION_EXPIRED',
      category: ErrorCategory.AUTHENTICATION,
      actions: [
        {
          strategy: RecoveryStrategy.REFRESH,
          priority: RecoveryPriority.HIGH,
          description: 'Refresh authentication token',
          userMessage: 'Refreshing session...',
          execute: async () => {
            // Token refresh logic would go here
            return Math.random() > 0.2; // Simulate 80% success rate
          }
        },
        {
          strategy: RecoveryStrategy.REDIRECT,
          priority: RecoveryPriority.MEDIUM,
          description: 'Redirect to login',
          userMessage: 'Please sign in again',
          requiresUserConsent: true,
          execute: async () => {
            router.replace('/auth/login');
            return true;
          }
        }
      ],
      fallbackMessage: 'Your session has expired. Please sign in again.'
    });

    // Data validation error recovery
    this.registerRecoveryPlan('VALIDATION_ERROR', {
      errorCode: 'VALIDATION_ERROR',
      category: ErrorCategory.VALIDATION,
      actions: [
        {
          strategy: RecoveryStrategy.USER_INTERVENTION,
          priority: RecoveryPriority.HIGH,
          description: 'Request user to correct input',
          userMessage: 'Please correct the highlighted fields',
          requiresUserConsent: false,
          execute: async () => {
            // Focus on invalid field, show validation messages
            return false; // Always requires user action
          }
        }
      ],
      fallbackMessage: 'Please check your input and try again.'
    });

    // System error recovery
    this.registerRecoveryPlan('SYSTEM_ERROR', {
      errorCode: 'SYSTEM_ERROR',
      category: ErrorCategory.SYSTEM,
      actions: [
        {
          strategy: RecoveryStrategy.REFRESH,
          priority: RecoveryPriority.HIGH,
          description: 'Refresh application',
          userMessage: 'Refreshing application...',
          requiresUserConsent: true,
          execute: async () => {
            // App refresh logic would go here
            if (typeof window !== 'undefined') {
              window.location.reload();
              return true;
            }
            return false;
          }
        },
        {
          strategy: RecoveryStrategy.REDIRECT,
          priority: RecoveryPriority.MEDIUM,
          description: 'Return to home screen',
          userMessage: 'Returning to home screen',
          execute: async () => {
            router.replace('/(tabs)');
            return true;
          }
        }
      ],
      fallbackMessage: 'A system error occurred. Please restart the application.'
    });
  }

  private getRecoveryPlan(error: AppError): RecoveryPlan | undefined {
    // Try exact error code match first
    let plan = this.recoveryPlans.get(error.code);
    if (plan) return plan;

    // Try category-based fallback
    plan = this.recoveryPlans.get(error.category);
    if (plan) return plan;

    // Try generic error code patterns
    const genericCode = error.code.split('_')[0] + '_ERROR';
    return this.recoveryPlans.get(genericCode);
  }

  private getRecoveryKey(error: AppError, context: Partial<RecoveryContext>): string {
    return `${error.code}:${context.component || 'unknown'}:${context.operation || 'unknown'}`;
  }

  private updateRecoveryHistory(key: string, context: RecoveryContext): void {
    const history = this.recoveryHistory.get(key) || [];
    history.push(context);

    // Keep only last 10 attempts per key
    if (history.length > 10) {
      history.splice(0, history.length - 10);
    }

    this.recoveryHistory.set(key, history);
  }

  private isInCooldown(key: string, action: RecoveryAction): boolean {
    if (!action.cooldownMs) return false;

    const history = this.recoveryHistory.get(key) || [];
    const lastAttempt = history[history.length - 1];

    return lastAttempt &&
           (Date.now() - lastAttempt.timestamp) < action.cooldownMs;
  }

  private hasExceededMaxAttempts(key: string, action: RecoveryAction): boolean {
    if (!action.maxAttempts) return false;

    const history = this.recoveryHistory.get(key) || [];
    const recentAttempts = history.filter(ctx =>
      Date.now() - ctx.timestamp < 300000 // Last 5 minutes
    );

    return recentAttempts.length >= action.maxAttempts;
  }

  private createGenericRecoveryResult(error: AppError): RecoveryResult {
    return {
      success: false,
      strategy: RecoveryStrategy.USER_INTERVENTION,
      message: 'An unexpected error occurred. Please try again or contact support.',
      requiresUserAction: true,
      nextSteps: [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists'
      ]
    };
  }

  private generateNextSteps(error: AppError, plan: RecoveryPlan): string[] {
    const steps: string[] = [];

    switch (error.category) {
      case ErrorCategory.NETWORK:
        steps.push(
          'Check your internet connection',
          'Try again in a few moments',
          'Switch to a different network if available'
        );
        break;

      case ErrorCategory.AUTHENTICATION:
        steps.push(
          'Sign out and sign back in',
          'Clear your browser cache',
          'Contact support if you continue having issues'
        );
        break;

      case ErrorCategory.VALIDATION:
        steps.push(
          'Check all required fields are filled',
          'Ensure data is in the correct format',
          'Try submitting again'
        );
        break;

      default:
        steps.push(
          'Refresh the application',
          'Try the action again',
          'Contact support if the problem persists'
        );
    }

    return steps;
  }
}

// =============================================================================
// EXPORTS
// =============================================================================

export { ErrorRecoveryService };
export const errorRecoveryService = ErrorRecoveryService.getInstance();

// Convenience functions
export const recoverFromError = (error: AppError, context?: Partial<RecoveryContext>) =>
  errorRecoveryService.recoverFromError(error, context);

export const registerRecoveryPlan = (errorCode: string, plan: RecoveryPlan) =>
  errorRecoveryService.registerRecoveryPlan(errorCode, plan);
