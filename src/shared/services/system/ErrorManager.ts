/**
 * Enterprise Error Management System
 * Unified error handling with proper categorization, recovery, and user feedback
 */

import { logger } from '../../utils/logger';
import { performanceMonitor } from '../performanceMonitor';

// =============================================================================
// ERROR TYPES & INTERFACES
// =============================================================================

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  PERMISSION = 'permission',
  DATA = 'data',
  UI = 'ui',
  SYSTEM = 'system',
  BUSINESS_LOGIC = 'business_logic'
}

export interface ErrorContext {
  userId?: string;
  sessionId?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, unknown>;
  timestamp?: number;
  stackTrace?: string;
  field?: string;
  value?: any;
}

export interface AppError extends Error {
  code: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  context: ErrorContext;
  userMessage: string;
  isRetryable: boolean;
  retryCount?: number;
  maxRetries?: number;
}

export interface ErrorRecoveryStrategy {
  canRecover: (error: AppError) => boolean;
  recover: (error: AppError) => Promise<boolean>;
  fallback?: () => void;
}

export interface ErrorHandlerConfig {
  maxRetries: number;
  retryDelayMs: number;
  enableUserNotifications: boolean;
  enableTelemetry: boolean;
  rateLimitWindowMs: number;
  maxErrorsPerWindow: number;
}

// =============================================================================
// CUSTOM ERROR CLASSES
// =============================================================================

export class NetworkError extends Error implements AppError {
  code: string;
  category = ErrorCategory.NETWORK;
  severity: ErrorSeverity;
  context: ErrorContext;
  userMessage: string;
  isRetryable = true;
  maxRetries = 3;

  constructor(message: string, code: string, context: ErrorContext = {}, severity: ErrorSeverity = ErrorSeverity.MEDIUM) {
    super(message);
    this.name = 'NetworkError';
    this.code = code;
    this.severity = severity;
    this.context = { ...context, timestamp: Date.now() };
    this.userMessage = this.generateUserMessage();
  }

  private generateUserMessage(): string {
    switch (this.code) {
      case 'NETWORK_TIMEOUT':
        return 'Connection timed out. Please check your internet connection and try again.';
      case 'NETWORK_OFFLINE':
        return 'You appear to be offline. Please check your connection.';
      case 'SERVER_ERROR':
        return 'Server is temporarily unavailable. Please try again in a moment.';
      default:
        return 'Network error occurred. Please try again.';
    }
  }
}

export class ValidationError extends Error implements AppError {
  code: string;
  category = ErrorCategory.VALIDATION;
  severity = ErrorSeverity.LOW;
  context: ErrorContext;
  userMessage: string;
  isRetryable = false;

  constructor(message: string, field: string, value: unknown, context: ErrorContext = {}) {
    super(message);
    this.name = 'ValidationError';
    this.code = `VALIDATION_${field.toUpperCase()}_INVALID`;
    this.context = { ...context, field, value, timestamp: Date.now() };
    this.userMessage = message;
  }
}

export class AuthenticationError extends Error implements AppError {
  code: string;
  category = ErrorCategory.AUTHENTICATION;
  severity = ErrorSeverity.HIGH;
  context: ErrorContext;
  userMessage: string;
  isRetryable = false;

  constructor(message: string, code: string, context: ErrorContext = {}) {
    super(message);
    this.name = 'AuthenticationError';
    this.code = code;
    this.context = { ...context, timestamp: Date.now() };
    this.userMessage = this.generateUserMessage();
  }

  private generateUserMessage(): string {
    switch (this.code) {
      case 'AUTH_SESSION_EXPIRED':
        return 'Your session has expired. Please sign in again.';
      case 'AUTH_INVALID_CREDENTIALS':
        return 'Invalid email or password. Please try again.';
      case 'AUTH_ACCOUNT_LOCKED':
        return 'Account temporarily locked. Please try again later.';
      default:
        return 'Authentication failed. Please sign in again.';
    }
  }
}

// =============================================================================
// ERROR MANAGER
// =============================================================================

class ErrorManager {
  private static instance: ErrorManager;
  private config: ErrorHandlerConfig;
  private recoveryStrategies: Map<ErrorCategory, ErrorRecoveryStrategy[]> = new Map();
  private errorCounts: Map<string, { count: number; firstOccurrence: number }> = new Map();
  private listeners: Set<(error: AppError) => void> = new Set();

  private constructor(config?: Partial<ErrorHandlerConfig>) {
    this.config = {
      maxRetries: 3,
      retryDelayMs: 1000,
      enableUserNotifications: true,
      enableTelemetry: true,
      rateLimitWindowMs: 60000, // 1 minute
      maxErrorsPerWindow: 10,
      ...config
    };

    this.setupDefaultRecoveryStrategies();
  }

  public static getInstance(config?: Partial<ErrorHandlerConfig>): ErrorManager {
    if (!ErrorManager.instance) {
      ErrorManager.instance = new ErrorManager(config);
    }
    return ErrorManager.instance;
  }

  /**
   * Handle any error with comprehensive processing
   */
  public async handleError(error: Error | AppError, context: ErrorContext = {}): Promise<void> {
    const appError = this.normalizeError(error, context);

    // Rate limiting
    if (!this.shouldProcessError(appError)) {
      return;
    }

    // Record error occurrence
    this.recordError(appError);

    // Log error
    this.logError(appError);

    // Track performance impact
    this.trackErrorPerformance(appError);

    // Attempt recovery
    const recovered = await this.attemptRecovery(appError);

    if (!recovered) {
      // Notify listeners (UI components, etc.)
      this.notifyListeners(appError);
    }

    // Send telemetry if enabled
    if (this.config.enableTelemetry) {
      this.sendTelemetry(appError);
    }
  }

  /**
   * Register error recovery strategy
   */
  public registerRecoveryStrategy(category: ErrorCategory, strategy: ErrorRecoveryStrategy): void {
    if (!this.recoveryStrategies.has(category)) {
      this.recoveryStrategies.set(category, []);
    }
    this.recoveryStrategies.get(category)!.push(strategy);
  }

  /**
   * Add error listener
   */
  public addListener(listener: (error: AppError) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Create typed error instances
   */
  public createNetworkError(message: string, code: string, context?: ErrorContext): NetworkError {
    return new NetworkError(message, code, context);
  }

  public createValidationError(message: string, field: string, value: unknown, context?: ErrorContext): ValidationError {
    return new ValidationError(message, field, value, context);
  }

  public createAuthError(message: string, code: string, context?: ErrorContext): AuthenticationError {
    return new AuthenticationError(message, code, context);
  }

  /**
   * Wrap async functions with error handling
   */
  public async withErrorHandling<T>(
    fn: () => Promise<T>,
    context: ErrorContext = {},
    fallback?: T
  ): Promise<T | undefined> {
    try {
      return await fn();
    } catch (error) {
      await this.handleError(error as Error, context);
      return fallback;
    }
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<ErrorCategory, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    topErrors: Array<{ code: string; count: number }>;
  } {
    const stats = {
      totalErrors: 0,
      errorsByCategory: {} as Record<ErrorCategory, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      topErrors: [] as Array<{ code: string; count: number }>
    };

    // Initialize counters
    Object.values(ErrorCategory).forEach(category => {
      stats.errorsByCategory[category] = 0;
    });
    Object.values(ErrorSeverity).forEach(severity => {
      stats.errorsBySeverity[severity] = 0;
    });

    // Count errors (implementation would track these)
    this.errorCounts.forEach((data, code) => {
      stats.totalErrors += data.count;
      stats.topErrors.push({ code, count: data.count });
    });

    // Sort top errors
    stats.topErrors.sort((a, b) => b.count - a.count);
    stats.topErrors = stats.topErrors.slice(0, 10);

    return stats;
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private normalizeError(error: Error | AppError, context: ErrorContext): AppError {
    if (this.isAppError(error)) {
      return { ...error, context: { ...error.context, ...context } };
    }

    // Convert generic error to AppError
    return {
      name: error.name || 'Error',
      message: error.message,
      code: 'GENERIC_ERROR',
      category: ErrorCategory.SYSTEM,
      severity: ErrorSeverity.MEDIUM,
      context: { ...context, timestamp: Date.now(), stackTrace: error.stack },
      userMessage: 'An unexpected error occurred. Please try again.',
      isRetryable: true,
      stack: error.stack
    } as AppError;
  }

  private isAppError(error: Error | AppError): error is AppError {
    return 'code' in error && 'category' in error && 'severity' in error;
  }

  private shouldProcessError(error: AppError): boolean {
    const key = `${error.code}:${error.context.component || 'unknown'}`;
    const now = Date.now();
    const errorData = this.errorCounts.get(key);

    if (!errorData) {
      this.errorCounts.set(key, { count: 1, firstOccurrence: now });
      return true;
    }

    // Check if we're within the rate limit window
    if (now - errorData.firstOccurrence < this.config.rateLimitWindowMs) {
      if (errorData.count >= this.config.maxErrorsPerWindow) {
        logger.warn(`Error rate limit exceeded for ${key}`, { count: errorData.count });
        return false;
      }
      errorData.count++;
    } else {
      // Reset window
      this.errorCounts.set(key, { count: 1, firstOccurrence: now });
    }

    return true;
  }

  private recordError(error: AppError): void {
    // Implementation would store error in analytics/monitoring system
    logger.debug('Error recorded', { code: error.code, category: error.category });
  }

  private logError(error: AppError): void {
    const logLevel = this.getLogLevel(error.severity);
    logger[logLevel](`[${error.category}] ${error.message}`, {
      code: error.code,
      severity: error.severity,
      context: error.context,
      userMessage: error.userMessage,
      isRetryable: error.isRetryable
    });
  }

  private getLogLevel(severity: ErrorSeverity): 'debug' | 'info' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW: return 'debug';
      case ErrorSeverity.MEDIUM: return 'info';
      case ErrorSeverity.HIGH: return 'warn';
      case ErrorSeverity.CRITICAL: return 'error';
      default: return 'error';
    }
  }

  private trackErrorPerformance(error: AppError): void {
    performanceMonitor.recordMetric(
      `error.${error.category}.${error.code}`,
      1,
      {
        severity: error.severity,
        component: error.context.component || 'unknown',
        retryable: error.isRetryable.toString()
      }
    );
  }

  private async attemptRecovery(error: AppError): Promise<boolean> {
    const strategies = this.recoveryStrategies.get(error.category) || [];

    for (const strategy of strategies) {
      if (strategy.canRecover(error)) {
        try {
          const recovered = await strategy.recover(error);
          if (recovered) {
            logger.info(`Error recovered using strategy`, {
              code: error.code,
              category: error.category
            });
            return true;
          }
        } catch (recoveryError) {
          logger.warn('Recovery strategy failed', {
            originalError: error.code,
            recoveryError: (recoveryError as Error).message
          });
        }
      }
    }

    return false;
  }

  private notifyListeners(error: AppError): void {
    this.listeners.forEach(listener => {
      try {
        listener(error);
      } catch (listenerError) {
        logger.error('Error listener failed', listenerError);
      }
    });
  }

  private sendTelemetry(error: AppError): void {
    // Implementation would send to analytics service
    logger.debug('Error telemetry sent', { code: error.code });
  }

  private setupDefaultRecoveryStrategies(): void {
    // Network error recovery
    this.registerRecoveryStrategy(ErrorCategory.NETWORK, {
      canRecover: (error) => error.isRetryable && (error.retryCount || 0) < (error.maxRetries || this.config.maxRetries),
      recover: async (error) => {
        const retryCount = (error.retryCount || 0) + 1;
        const delay = this.config.retryDelayMs * Math.pow(2, retryCount - 1); // Exponential backoff

        await new Promise(resolve => setTimeout(resolve, delay));

        // This would retry the original operation
        // Implementation depends on how operations are structured
        return false; // Placeholder - actual implementation would retry
      }
    });

    // Authentication error recovery
    this.registerRecoveryStrategy(ErrorCategory.AUTHENTICATION, {
      canRecover: (error) => error.code === 'AUTH_SESSION_EXPIRED',
      recover: async (error) => {
        // This would attempt to refresh the session
        // Implementation depends on auth system
        return false; // Placeholder
      }
    });
  }
}

// =============================================================================
// EXPORTS
// =============================================================================

export const errorManager = ErrorManager.getInstance();

// Convenience functions
export const handleError = (error: Error | AppError, context?: ErrorContext) =>
  errorManager.handleError(error, context);

export const withErrorHandling = <T>(fn: () => Promise<T>, context?: ErrorContext, fallback?: T) =>
  errorManager.withErrorHandling(fn, context, fallback);

export const createNetworkError = (message: string, code: string, context?: ErrorContext) =>
  errorManager.createNetworkError(message, code, context);

export const createValidationError = (message: string, field: string, value: unknown, context?: ErrorContext) =>
  errorManager.createValidationError(message, field, value, context);

export const createAuthError = (message: string, code: string, context?: ErrorContext) =>
  errorManager.createAuthError(message, code, context);
