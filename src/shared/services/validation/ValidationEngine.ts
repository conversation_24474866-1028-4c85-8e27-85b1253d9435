/**
 * Validation Engine
 *
 * Centralized validation system for the application
 */

export enum ValidationType {
  EMAIL = 'email',
  PASSWORD = 'password',
  UUID = 'uuid',
  TEXT = 'text',
  PHONE = 'phone',
  URL = 'url',
  DATE = 'date',
  NUMBER = 'number',
  STRING = 'string',
  INTEGER = 'integer',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
  ENUM = 'enum'
}

export interface ValidationSchema {
  type: ValidationType;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Array<string | { code: string; message: string }>;
  warnings?: string[];
  sanitizedValue?: any;
}

class ValidationEngine {
  validateEmail(email: string): ValidationResult {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);

    return {
      isValid,
      errors: isValid ? [] : [{ code: 'INVALID_EMAIL', message: 'Invalid email format' }],
      sanitizedValue: email.toLowerCase().trim()
    };
  }

  validatePassword(password: string): ValidationResult {
    const errors: Array<string | { code: string; message: string }> = [];

    if (password.length < 8) {
      errors.push({ code: 'WEAK_PASSWORD', message: 'Password must be at least 8 characters long' });
    }

    if (!/[A-Z]/.test(password)) {
      errors.push({ code: 'WEAK_PASSWORD', message: 'Password must contain at least one uppercase letter' });
    }

    if (!/[a-z]/.test(password)) {
      errors.push({ code: 'WEAK_PASSWORD', message: 'Password must contain at least one lowercase letter' });
    }

    if (!/\d/.test(password)) {
      errors.push({ code: 'WEAK_PASSWORD', message: 'Password must contain at least one number' });
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: password
    };
  }

  validateUUID(uuid: string): ValidationResult {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const isValid = uuidRegex.test(uuid);

    return {
      isValid,
      errors: isValid ? [] : ['Invalid UUID format'],
      sanitizedValue: uuid.toLowerCase()
    };
  }

  validate(value: any, schema: ValidationSchema): ValidationResult {
    const errors: string[] = [];

    if (schema.required && (!value || value === '')) {
      errors.push('This field is required');
      return { isValid: false, errors };
    }

    if (!value) {
      return { isValid: true, errors: [] };
    }

    switch (schema.type) {
      case ValidationType.EMAIL:
        return this.validateEmail(value);
      case ValidationType.PASSWORD:
        return this.validatePassword(value);
      case ValidationType.UUID:
        return this.validateUUID(value);
      default:
        break;
    }

    if (schema.minLength && value.length < schema.minLength) {
      errors.push(`Minimum length is ${schema.minLength} characters`);
    }

    if (schema.maxLength && value.length > schema.maxLength) {
      errors.push(`Maximum length is ${schema.maxLength} characters`);
    }

    if (schema.pattern && !schema.pattern.test(value)) {
      errors.push('Invalid format');
    }

    if (schema.custom && !schema.custom(value)) {
      errors.push('Custom validation failed');
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitizedValue: value
    };
  }

  validateObject(obj: any, schema: Record<string, ValidationSchema>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const sanitizedValue: any = {};

    for (const [key, fieldSchema] of Object.entries(schema)) {
      const result = this.validate(obj[key], fieldSchema);

      if (!result.isValid) {
        errors.push(...result.errors.map(error =>
          typeof error === 'string' ? `${key}: ${error}` : `${key}: ${error.message}`
        ));
      }

      if (result.warnings) {
        warnings.push(...result.warnings.map(warning => `${key}: ${warning}`));
      }

      sanitizedValue[key] = result.sanitizedValue !== undefined ? result.sanitizedValue : obj[key];
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings: warnings.length > 0 ? warnings : undefined,
      sanitizedValue
    };
  }
}

export const validationEngine = new ValidationEngine();

// Legacy exports for backward compatibility
export const validateEmail = (email: string) => validationEngine.validateEmail(email);
export const validatePassword = (password: string) => validationEngine.validatePassword(password);
export const validateUUID = (uuid: string) => validationEngine.validateUUID(uuid);
