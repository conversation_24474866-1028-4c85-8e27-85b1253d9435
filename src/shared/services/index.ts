/**
 * Shared Services Index
 *
 * Centralized exports for all shared services
 */

// Context providers
export * from './contexts/FavoritesContext';
export * from './contexts/SettingsContext';

// Data services
export * from './data/dataService';

// Feature services
export * from './features/pointsSystemService';

// Middleware
export * from './middleware/securityMiddleware';

// Performance monitoring
export * from './performanceMonitor';

// Realtime services
export * from './realtime/useCoupleRealtime';

// Storage services
export * from './storage/cacheManager';
export * from './storage/hybridStorageService';

// Supabase services
export * from './supabase';

// System services
export * from './system/ErrorManager';
export * from './system/errorReportingService';
export * from './system/queryOptimizationService';
export * from './system/securityValidationService';
export * from './system/simpleErrorService';
export * from './system/subscriptionManager';

// Validation services
export * from './validation/ValidationEngine';

// Logging services
export * from './logging/EnterpriseLogger';

// Primary exports (avoid duplicates)
export {
    ConfigurationManager,
    configManager
} from './system/ConfigurationManager';

export {
    ErrorRecoveryService,
    errorRecoveryService,
    recoverFromError
} from './system/ErrorRecoveryService';

export {
    OfflineManager, OperationType, isOnline, offlineManager, queueOfflineOperation
} from './storage/OfflineManager';

export {
    AccessibilityManager, AccessibilityRole, accessibilityManager,
    getAccessibilityProps
} from './system/AccessibilityManager';
