/**
 * Shared Services Index
 *
 * Centralized exports for all shared services
 */

// Context providers
export * from './contexts/FavoritesContext';
export * from './contexts/SettingsContext';

// Data services
export * from './data/dataService';

// Feature services
export * from './features/pointsSystemService';

// Middleware
export * from './middleware/securityMiddleware';

// Performance monitoring
export * from './performanceMonitor';

// Realtime services
export * from './realtime/useCoupleRealtime';

// Storage services
export * from './storage/cacheManager';
export * from './storage/hybridStorageService';

// Supabase services
export * from './supabase';

// System services
export * from './system/errorReportingService';
export * from './system/queryOptimizationService';
export * from './system/securityValidationService';
export * from './system/simpleErrorService';
export * from './system/subscriptionManager';

// Validation services
// Note: Validation functionality is now in shared/utils/validation.ts

// Logging services
export * from './logging/enterpriseLogger';

// Primary exports (avoid duplicates)
export {
    ConfigurationManager,
    configManager
} from './system/configurationManager';

export {
    ErrorRecoveryService,
    errorRecoveryService,
    recoverFromError
} from './system/errorRecoveryService';

export {
    OfflineManager, OperationType, isOnline, offlineManager, queueOfflineOperation
} from './storage/offlineManager';

export {
    AccessibilityManager, AccessibilityRole, accessibilityManager,
    getAccessibilityProps
} from './system/accessibilityManager';
