/**
 * Performance Monitor Service
 * Simple wrapper around performance optimization service for dashboard
 */

import { performanceOptimizationService } from '../../journeys/progress/performanceOptimizationService';
import { logger } from '../utils/logger';

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;

  private constructor() {}

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Get performance summary for dashboard
   */
  getPerformanceSummary() {
    try {
      const metrics = performanceOptimizationService.getPerformanceMetrics();
      return {
        averageQueryTime: metrics.averageQueryTime,
        totalQueries: metrics.totalQueries,
        cacheHitRate: metrics.cacheHitRate,
        memoryUsage: metrics.memoryUsage,
        slowQueries: metrics.slowQueries.length
      };
    } catch (error) {
      logger.error('Error getting performance summary:', error);
      return {
        averageQueryTime: 0,
        totalQueries: 0,
        cacheHitRate: 0,
        memoryUsage: 0,
        slowQueries: 0
      };
    }
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number): void {
    this.metrics.set(name, value);
  }

  /**
   * Track network request performance
   */
  trackNetworkRequest(url: string): { start: () => void; end: (success: boolean, size?: number) => void } {
    const startTime = performance.now();
    return {
      start: () => {
        this.recordMetric(`network_request_start_${url}`, performance.now());
      },
      end: (success: boolean, size?: number) => {
        const duration = performance.now() - startTime;
        this.recordMetric(`network_request_${success ? 'success' : 'error'}_${url}`, duration);
        if (size) {
          this.recordMetric(`network_request_size_${url}`, size);
        }
      }
    };
  }

  /**
   * Track component performance
   */
  trackComponent(componentName: string): { startRender: () => void; endRender: () => void } {
    const startTime = performance.now();
    return {
      startRender: () => {
        this.recordMetric(`${componentName}_render_start`, performance.now());
      },
      endRender: () => {
        this.recordMetric(`${componentName}_render_duration`, performance.now() - startTime);
      }
    };
  }

  /**
   * Execute batch operation with performance tracking
   */
  async executeBatchOperation<T>(operation: any): Promise<any> {
    const startTime = performance.now();
    try {
      const result = await operation();
      this.recordMetric('batch_operation_success', performance.now() - startTime);
      return result;
    } catch (error) {
      this.recordMetric('batch_operation_error', performance.now() - startTime);
      throw error;
    }
  }

  /**
   * Get performance metrics summary
   */
  getPerformanceMetrics(): Record<string, any> {
    return {
      totalMetrics: this.metrics?.length || 0,
      averageResponseTime: this.calculateAverageResponseTime(),
      errorRate: this.calculateErrorRate(),
      lastUpdated: new Date().toISOString()
    };
  }

  private calculateAverageResponseTime(): number {
    if (!this.metrics || this.metrics.length === 0) return 0;
    const total = this.metrics.reduce((sum, metric) => sum + (metric.value || 0), 0);
    return total / this.metrics.length;
  }

  private calculateErrorRate(): number {
    if (!this.metrics || this.metrics.length === 0) return 0;
    const errors = this.metrics.filter(m => m.name?.includes('error')).length;
    return (errors / this.metrics.length) * 100;
  }

  /**
   * Track component performance
   */
  trackComponent(componentName: string): { startRender: () => void; endRender: () => void } {
    const startTime = performance.now();
    return {
      startRender: () => {
        this.recordMetric(`${componentName}_render_start`, performance.now());
      },
      endRender: () => {
        this.recordMetric(`${componentName}_render_duration`, performance.now() - startTime);
      }
    };
  }

  /**
   * Execute batch operation with performance tracking
   */
  async executeBatchOperation<T>(operation: any): Promise<any> {
    const startTime = performance.now();
    try {
      const result = await operation();
      this.recordMetric('batch_operation_success', performance.now() - startTime);
      return result;
    } catch (error) {
      this.recordMetric('batch_operation_error', performance.now() - startTime);
      throw error;
    }
  }

  /**
   * Generate performance recommendations
   */
  generateRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];

    if (metrics.averageQueryTime > 1000) {
      recommendations.push('Optimize slow queries or add database indexes');
    }

    if (metrics.slowQueries.length > 10) {
      recommendations.push('Review and optimize slow query patterns');
    }

    if (metrics.memoryUsage > 100) {
      recommendations.push('Monitor memory usage and consider cleanup');
    }

    return recommendations.length > 0 ? recommendations : ['System performance is optimal'];
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();
export default performanceMonitor;
