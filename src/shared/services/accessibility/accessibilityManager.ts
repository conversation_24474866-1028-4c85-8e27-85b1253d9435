/**
 * Accessibility Manager
 * 
 * Handles accessibility features and compliance
 */

export enum AccessibilityRole {
  BUTTON = 'button',
  LINK = 'link',
  TEXT = 'text',
  HEADING = 'heading',
  IMAGE = 'image',
  LIST = 'list',
  LISTITEM = 'listitem',
  MENU = 'menu',
  MENUITEM = 'menuitem',
  TAB = 'tab',
  TABLIST = 'tablist',
  TABPANEL = 'tabpanel',
  TEXTBOX = 'textbox',
  CHECKBOX = 'checkbox',
  RADIO = 'radio',
  SWITCH = 'switch',
  SLIDER = 'slider',
  PROGRESSBAR = 'progressbar',
  ALERT = 'alert',
  DIALOG = 'dialog',
  BANNER = 'banner',
  MAIN = 'main',
  NAVIGATION = 'navigation',
  COMPLEMENTARY = 'complementary',
  CONTENTINFO = 'contentinfo'
}

export interface AccessibilityProps {
  accessibilityRole?: AccessibilityRole;
  accessibilityLabel?: string;
  accessibilityHint?: string;
  accessibilityValue?: {
    min?: number;
    max?: number;
    now?: number;
    text?: string;
  };
  accessibilityState?: {
    disabled?: boolean;
    selected?: boolean;
    checked?: boolean | 'mixed';
    busy?: boolean;
    expanded?: boolean;
  };
  accessibilityActions?: Array<{
    name: string;
    label?: string;
  }>;
  accessible?: boolean;
  importantForAccessibility?: 'auto' | 'yes' | 'no' | 'no-hide-descendants';
}

export interface AccessibilitySettings {
  screenReaderEnabled: boolean;
  highContrastEnabled: boolean;
  largeTextEnabled: boolean;
  reduceMotionEnabled: boolean;
  voiceOverEnabled: boolean;
}

class AccessibilityManager {
  private settings: AccessibilitySettings = {
    screenReaderEnabled: false,
    highContrastEnabled: false,
    largeTextEnabled: false,
    reduceMotionEnabled: false,
    voiceOverEnabled: false
  };

  private listeners: ((settings: AccessibilitySettings) => void)[] = [];

  constructor() {
    this.initializeAccessibilitySettings();
  }

  private async initializeAccessibilitySettings(): Promise<void> {
    try {
      // In React Native, you'd use AccessibilityInfo API
      // For now, we'll use basic web APIs where available
      
      // Check for screen reader
      if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
        // Basic detection - in production use proper accessibility APIs
        this.settings.screenReaderEnabled = false;
      }

      // Check for reduced motion preference
      if (typeof window !== 'undefined' && window.matchMedia) {
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        this.settings.reduceMotionEnabled = prefersReducedMotion.matches;
        
        prefersReducedMotion.addEventListener('change', (e) => {
          this.settings.reduceMotionEnabled = e.matches;
          this.notifyListeners();
        });
      }

      // Check for high contrast preference
      if (typeof window !== 'undefined' && window.matchMedia) {
        const prefersHighContrast = window.matchMedia('(prefers-contrast: high)');
        this.settings.highContrastEnabled = prefersHighContrast.matches;
        
        prefersHighContrast.addEventListener('change', (e) => {
          this.settings.highContrastEnabled = e.matches;
          this.notifyListeners();
        });
      }

      this.notifyListeners();
    } catch (error) {
      console.warn('Failed to initialize accessibility settings:', error);
    }
  }

  getAccessibilitySettings(): AccessibilitySettings {
    return { ...this.settings };
  }

  onAccessibilityChange(listener: (settings: AccessibilitySettings) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.settings));
  }

  getAccessibilityProps(
    role: AccessibilityRole,
    label?: string,
    hint?: string,
    additionalProps?: Partial<AccessibilityProps>
  ): AccessibilityProps {
    return {
      accessibilityRole: role,
      accessibilityLabel: label,
      accessibilityHint: hint,
      accessible: true,
      ...additionalProps
    };
  }

  // Helper methods for common accessibility patterns
  getButtonProps(label: string, hint?: string): AccessibilityProps {
    return this.getAccessibilityProps(AccessibilityRole.BUTTON, label, hint);
  }

  getLinkProps(label: string, hint?: string): AccessibilityProps {
    return this.getAccessibilityProps(AccessibilityRole.LINK, label, hint);
  }

  getHeadingProps(label: string, level: number = 1): AccessibilityProps {
    return this.getAccessibilityProps(AccessibilityRole.HEADING, label, undefined, {
      accessibilityValue: { text: `Heading level ${level}` }
    });
  }

  getTextInputProps(label: string, hint?: string, required: boolean = false): AccessibilityProps {
    return this.getAccessibilityProps(AccessibilityRole.TEXTBOX, label, hint, {
      accessibilityState: { disabled: false },
      accessibilityHint: required ? `${hint || ''} Required field`.trim() : hint
    });
  }

  announceForAccessibility(message: string): void {
    // In React Native, you'd use AccessibilityInfo.announceForAccessibility
    // For web, we can use aria-live regions or screen reader APIs
    if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
      // Simple announcement using speech synthesis as fallback
      const utterance = new SpeechSynthesisUtterance(message);
      utterance.volume = 0; // Silent, just for screen readers
      window.speechSynthesis.speak(utterance);
    }
    
    console.log(`Accessibility announcement: ${message}`);
  }

  isScreenReaderEnabled(): boolean {
    return this.settings.screenReaderEnabled;
  }

  isReduceMotionEnabled(): boolean {
    return this.settings.reduceMotionEnabled;
  }

  isHighContrastEnabled(): boolean {
    return this.settings.highContrastEnabled;
  }
}

export const accessibilityManager = new AccessibilityManager();

// Legacy exports
export const getAccessibilityProps = (
  role: AccessibilityRole,
  label?: string,
  hint?: string,
  additionalProps?: Partial<AccessibilityProps>
) => accessibilityManager.getAccessibilityProps(role, label, hint, additionalProps);
