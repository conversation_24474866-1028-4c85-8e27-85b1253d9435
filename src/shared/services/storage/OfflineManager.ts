/**
 * Enterprise Offline Management System
 * Comprehensive offline-first capabilities with intelligent sync and conflict resolution
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import { enterpriseLogger } from '../logging/EnterpriseLogger';
import { configManager } from '../system/ConfigurationManager';

// =============================================================================
// OFFLINE TYPES & INTERFACES
// =============================================================================

export enum SyncStatus {
  IDLE = 'idle',
  SYNCING = 'syncing',
  SUCCESS = 'success',
  ERROR = 'error',
  CONFLICT = 'conflict'
}

export enum OperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  BATCH = 'batch'
}

export enum ConflictResolution {
  CLIENT_WINS = 'client_wins',
  SERVER_WINS = 'server_wins',
  MERGE = 'merge',
  USER_DECIDES = 'user_decides'
}

export interface OfflineOperation {
  id: string;
  type: OperationType;
  entity: string;
  data: unknown;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: number;
  dependencies?: string[];
  metadata?: Record<string, unknown>;
}

export interface SyncResult {
  success: boolean;
  operationsProcessed: number;
  conflicts: ConflictData[];
  errors: SyncError[];
  duration: number;
}

export interface ConflictData {
  operationId: string;
  entity: string;
  clientData: unknown;
  serverData: unknown;
  resolution?: ConflictResolution;
  resolvedData?: unknown;
}

export interface SyncError {
  operationId: string;
  error: Error;
  retryable: boolean;
}

export interface NetworkState {
  isConnected: boolean;
  type: string;
  isInternetReachable: boolean;
  quality: 'poor' | 'fair' | 'good' | 'excellent';
}

export interface OfflineConfig {
  enableOfflineMode: boolean;
  maxQueueSize: number;
  syncIntervalMs: number;
  retryDelayMs: number;
  maxRetries: number;
  conflictResolution: ConflictResolution;
  enableBackgroundSync: boolean;
  compressionEnabled: boolean;
}

// =============================================================================
// OFFLINE MANAGER
// =============================================================================

class OfflineManager {
  private static instance: OfflineManager;
  private config: OfflineConfig;
  private networkState: NetworkState = {
    isConnected: false,
    type: 'unknown',
    isInternetReachable: false,
    quality: 'poor'
  };
  private operationQueue: OfflineOperation[] = [];
  private syncStatus: SyncStatus = SyncStatus.IDLE;
  private syncListeners: Set<(status: SyncStatus, result?: SyncResult) => void> = new Set();
  private networkListeners: Set<(state: NetworkState) => void> = new Set();
  private syncInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.config = this.loadConfig();
    this.initializeNetworkMonitoring();
    this.loadOfflineQueue();
    this.startSyncInterval();
  }

  public static getInstance(): OfflineManager {
    if (!OfflineManager.instance) {
      OfflineManager.instance = new OfflineManager();
    }
    return OfflineManager.instance;
  }

  /**
   * Get current network state
   */
  public getNetworkState(): NetworkState {
    return { ...this.networkState };
  }

  /**
   * Check if device is online
   */
  public isOnline(): boolean {
    return this.networkState.isConnected && this.networkState.isInternetReachable;
  }

  /**
   * Check if offline mode is enabled
   */
  public isOfflineModeEnabled(): boolean {
    return this.config.enableOfflineMode;
  }

  /**
   * Queue operation for offline execution
   */
  public async queueOperation(operation: Omit<OfflineOperation, 'id' | 'timestamp' | 'retryCount'>): Promise<string> {
    const fullOperation: OfflineOperation = {
      ...operation,
      id: this.generateOperationId(),
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: operation.maxRetries || this.config.maxRetries
    };

    // Check queue size limit
    if (this.operationQueue.length >= this.config.maxQueueSize) {
      // Remove oldest low-priority operations
      this.operationQueue = this.operationQueue
        .sort((a, b) => b.priority - a.priority || a.timestamp - b.timestamp)
        .slice(0, this.config.maxQueueSize - 1);
    }

    this.operationQueue.push(fullOperation);
    await this.saveOfflineQueue();

    enterpriseLogger.info('Operation queued for offline sync', {
      operationId: fullOperation.id,
      type: fullOperation.type,
      entity: fullOperation.entity,
      queueSize: this.operationQueue.length
    });

    // Try immediate sync if online
    if (this.isOnline() && this.syncStatus === SyncStatus.IDLE) {
      this.syncOperations();
    }

    return fullOperation.id;
  }

  /**
   * Remove operation from queue
   */
  public async removeOperation(operationId: string): Promise<boolean> {
    const initialLength = this.operationQueue.length;
    this.operationQueue = this.operationQueue.filter(op => op.id !== operationId);

    if (this.operationQueue.length < initialLength) {
      await this.saveOfflineQueue();
      enterpriseLogger.debug('Operation removed from queue', { operationId });
      return true;
    }

    return false;
  }

  /**
   * Get pending operations
   */
  public getPendingOperations(): OfflineOperation[] {
    return [...this.operationQueue];
  }

  /**
   * Get sync status
   */
  public getSyncStatus(): SyncStatus {
    return this.syncStatus;
  }

  /**
   * Force sync operations
   */
  public async forcSync(): Promise<SyncResult> {
    if (!this.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    return this.syncOperations();
  }

  /**
   * Add sync status listener
   */
  public addSyncListener(listener: (status: SyncStatus, result?: SyncResult) => void): () => void {
    this.syncListeners.add(listener);
    return () => this.syncListeners.delete(listener);
  }

  /**
   * Add network state listener
   */
  public addNetworkListener(listener: (state: NetworkState) => void): () => void {
    this.networkListeners.add(listener);
    return () => this.networkListeners.delete(listener);
  }

  /**
   * Cache data for offline access
   */
  public async cacheData(key: string, data: unknown, ttl?: number): Promise<void> {
    const cacheEntry = {
      data,
      timestamp: Date.now(),
      ttl: ttl || 86400000 // 24 hours default
    };

    try {
      await AsyncStorage.setItem(`offline_cache_${key}`, JSON.stringify(cacheEntry));
      enterpriseLogger.debug('Data cached for offline access', { key, size: JSON.stringify(data).length });
    } catch (error) {
      enterpriseLogger.error('Failed to cache data', { error });
    }
  }

  /**
   * Get cached data
   */
  public async getCachedData<T = unknown>(key: string): Promise<T | null> {
    try {
      const cached = await AsyncStorage.getItem(`offline_cache_${key}`);
      if (!cached) return null;

      const cacheEntry = JSON.parse(cached);
      const now = Date.now();

      // Check if cache is expired
      if (cacheEntry.timestamp + cacheEntry.ttl < now) {
        await AsyncStorage.removeItem(`offline_cache_${key}`);
        return null;
      }

      return cacheEntry.data as T;
    } catch (error) {
      enterpriseLogger.error('Failed to get cached data', { error });
      return null;
    }
  }

  /**
   * Clear expired cache entries
   */
  public async clearExpiredCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('offline_cache_'));
      const now = Date.now();

      for (const key of cacheKeys) {
        const cached = await AsyncStorage.getItem(key);
        if (cached) {
          const cacheEntry = JSON.parse(cached);
          if (cacheEntry.timestamp + cacheEntry.ttl < now) {
            await AsyncStorage.removeItem(key);
          }
        }
      }

      enterpriseLogger.debug('Expired cache entries cleared');
    } catch (error) {
      enterpriseLogger.error('Failed to clear expired cache', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  /**
   * Get offline statistics
   */
  public getOfflineStats(): {
    queueSize: number;
    pendingOperations: number;
    lastSyncTime: number | null;
    networkQuality: string;
    cacheSize: number;
  } {
    return {
      queueSize: this.operationQueue.length,
      pendingOperations: this.operationQueue.filter(op => op.retryCount < op.maxRetries).length,
      lastSyncTime: null, // Would track actual last sync time
      networkQuality: this.networkState.quality,
      cacheSize: 0 // Would calculate actual cache size
    };
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private loadConfig(): OfflineConfig {
    return {
      enableOfflineMode: configManager.isFeatureEnabled('enableOfflineMode'),
      maxQueueSize: 1000,
      syncIntervalMs: 30000, // 30 seconds
      retryDelayMs: 5000, // 5 seconds
      maxRetries: 3,
      conflictResolution: ConflictResolution.CLIENT_WINS,
      enableBackgroundSync: true,
      compressionEnabled: true
    };
  }

  private async initializeNetworkMonitoring(): Promise<void> {
    try {
      // Get initial network state
      const state = await NetInfo.fetch();
      this.updateNetworkState(state);

      // Listen for network changes
      NetInfo.addEventListener(this.updateNetworkState.bind(this));

      enterpriseLogger.info('Network monitoring initialized', {
        isConnected: this.networkState.isConnected,
        type: this.networkState.type
      });
    } catch (error) {
      enterpriseLogger.error('Failed to initialize network monitoring', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  private updateNetworkState(state: NetInfoState): void {
    const previousState = { ...this.networkState };

    this.networkState = {
      isConnected: state.isConnected || false,
      type: state.type || 'unknown',
      isInternetReachable: state.isInternetReachable || false,
      quality: this.calculateNetworkQuality(state)
    };

    // Log network state changes
    if (previousState.isConnected !== this.networkState.isConnected) {
      enterpriseLogger.info('Network connectivity changed', {
        wasConnected: previousState.isConnected,
        isConnected: this.networkState.isConnected,
        type: this.networkState.type
      });

      // Trigger sync when coming back online
      if (this.networkState.isConnected && !previousState.isConnected) {
        setTimeout(() => this.syncOperations(), 1000);
      }
    }

    // Notify listeners
    this.networkListeners.forEach(listener => {
      try {
        listener(this.networkState);
      } catch (error) {
        enterpriseLogger.error('Network listener error', { error: error instanceof Error ? error.message : String(error) });
      }
    });
  }

  private calculateNetworkQuality(state: NetInfoState): 'poor' | 'fair' | 'good' | 'excellent' {
    if (!state.isConnected) return 'poor';

    // This would use actual network metrics in a real implementation
    switch (state.type) {
      case 'wifi':
        return 'excellent';
      case 'cellular':
        return 'good';
      case 'ethernet':
        return 'excellent';
      default:
        return 'fair';
    }
  }

  private async loadOfflineQueue(): Promise<void> {
    try {
      const queueData = await AsyncStorage.getItem('offline_operation_queue');
      if (queueData) {
        this.operationQueue = JSON.parse(queueData);
        enterpriseLogger.info('Offline queue loaded', { queueSize: this.operationQueue.length });
      }
    } catch (error) {
      enterpriseLogger.error('Failed to load offline queue', { error: error instanceof Error ? error.message : String(error) });
      this.operationQueue = [];
    }
  }

  private async saveOfflineQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem('offline_operation_queue', JSON.stringify(this.operationQueue));
    } catch (error) {
      enterpriseLogger.error('Failed to save offline queue', { error: error instanceof Error ? error.message : String(error) });
    }
  }

  private startSyncInterval(): void {
    if (!this.config.enableBackgroundSync) return;

    this.syncInterval = setInterval(() => {
      if (this.isOnline() && this.syncStatus === SyncStatus.IDLE && this.operationQueue.length > 0) {
        this.syncOperations();
      }
    }, this.config.syncIntervalMs);
  }

  private async syncOperations(): Promise<SyncResult> {
    if (this.syncStatus === SyncStatus.SYNCING) {
      throw new Error('Sync already in progress');
    }

    this.syncStatus = SyncStatus.SYNCING;
    this.notifySyncListeners(SyncStatus.SYNCING);

    const startTime = Date.now();
    const result: SyncResult = {
      success: true,
      operationsProcessed: 0,
      conflicts: [],
      errors: [],
      duration: 0
    };

    try {
      // Sort operations by priority and dependencies
      const sortedOperations = this.sortOperationsByPriority(this.operationQueue);

      for (const operation of sortedOperations) {
        try {
          await this.executeOperation(operation);
          result.operationsProcessed++;

          // Remove successful operation from queue
          this.operationQueue = this.operationQueue.filter(op => op.id !== operation.id);
        } catch (error) {
          operation.retryCount++;

          if (operation.retryCount >= operation.maxRetries) {
            result.errors.push({
              operationId: operation.id,
              error: error as Error,
              retryable: false
            });

            // Remove failed operation from queue
            this.operationQueue = this.operationQueue.filter(op => op.id !== operation.id);
          } else {
            result.errors.push({
              operationId: operation.id,
              error: error as Error,
              retryable: true
            });
          }
        }
      }

      await this.saveOfflineQueue();

      result.success = result.errors.length === 0;
      result.duration = Date.now() - startTime;

      this.syncStatus = result.success ? SyncStatus.SUCCESS : SyncStatus.ERROR;

      enterpriseLogger.info('Sync completed', {
        success: result.success,
        operationsProcessed: result.operationsProcessed,
        errors: result.errors.length,
        duration: result.duration
      });

    } catch (error) {
      result.success = false;
      result.duration = Date.now() - startTime;
      this.syncStatus = SyncStatus.ERROR;

      enterpriseLogger.error('Sync failed', { error: error instanceof Error ? error.message : String(error) });
    }

    this.notifySyncListeners(this.syncStatus, result);

    // Reset status after a delay
    setTimeout(() => {
      this.syncStatus = SyncStatus.IDLE;
    }, 2000);

    return result;
  }

  private sortOperationsByPriority(operations: OfflineOperation[]): OfflineOperation[] {
    return [...operations].sort((a, b) => {
      // Sort by priority first, then by timestamp
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      return a.timestamp - b.timestamp;
    });
  }

  private async executeOperation(operation: OfflineOperation): Promise<void> {
    // This would contain the actual API call logic
    // For now, we'll simulate the operation

    enterpriseLogger.debug('Executing offline operation', {
      operationId: operation.id,
      type: operation.type,
      entity: operation.entity
    });

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // Simulate occasional failures
    if (Math.random() < 0.1) {
      throw new Error('Simulated network error');
    }
  }

  private notifySyncListeners(status: SyncStatus, result?: SyncResult): void {
    this.syncListeners.forEach(listener => {
      try {
        listener(status, result);
      } catch (error) {
        enterpriseLogger.error('Sync listener error', { error: error instanceof Error ? error.message : String(error) });
      }
    });
  }

  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// =============================================================================
// EXPORTS
// =============================================================================

export { OfflineManager };
export const offlineManager = OfflineManager.getInstance();

// Convenience functions
export const isOnline = () => offlineManager.isOnline();
export const queueOfflineOperation = (operation: Parameters<typeof offlineManager.queueOperation>[0]) =>
  offlineManager.queueOperation(operation);
export const cacheForOffline = (key: string, data: unknown, ttl?: number) =>
  offlineManager.cacheData(key, data, ttl);
export const getCachedData = <T = unknown>(key: string) =>
  offlineManager.getCachedData<T>(key);
