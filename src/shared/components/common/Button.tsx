/**
 * Button Component
 * 
 * Unified button component that replaces all the scattered button implementations.
 * Uses the new design system tokens and provides consistent API across the app.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import React from 'react';
import { TouchableOpacity, Text, View, ActivityIndicator, ViewStyle, TextStyle } from 'react-native';
import { getButtonStyle, getTextStyle, tokens } from '../../utils/theme';
import type { Theme } from '../../utils/theme/colors';

// =============================================================================
// TYPES
// =============================================================================

export interface ButtonProps {
  // Content
  title: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  
  // Behavior
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  
  // Appearance
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  
  // Styling
  style?: ViewStyle;
  textStyle?: TextStyle;
  
  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;
}

// =============================================================================
// COMPONENT
// =============================================================================

export const Button: React.FC<ButtonProps & { theme: Theme }> = ({
  title,
  leftIcon,
  rightIcon,
  onPress,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  style,
  textStyle,
  accessibilityLabel,
  accessibilityHint,
  testID,
  theme,
}) => {
  // Get base styles from design system
  const buttonStyle = getButtonStyle(variant!, size, theme);
  const baseTextStyle = getTextStyle('body', theme);
  
  // Determine text color based on variant
  const getTextColor = () => {
    switch (variant) {
      case 'primary':
        return theme.textInverse;
      case 'secondary':
        return theme.textInverse;
      case 'outline':
        return theme.primary;
      case 'ghost':
        return theme.primary;
      default:
        return theme.textInverse;
    }
  };
  
  // Determine if button should be disabled
  const isDisabled = disabled || loading;
  
  // Build final styles
  const finalButtonStyle: ViewStyle = {
    ...buttonStyle,
    ...(fullWidth && { width: '100%' }),
    ...(isDisabled && { opacity: tokens.opacity.disabled }),
    ...style,
  };
  
  const finalTextStyle: TextStyle = {
    ...baseTextStyle,
    color: getTextColor(),
    fontWeight: tokens.typography.fontWeight.semibold,
    ...textStyle,
  };
  
  return (
    <TouchableOpacity
      style={finalButtonStyle}
      onPress={onPress}
      disabled={isDisabled}
      accessibilityLabel={accessibilityLabel || title}
      accessibilityHint={accessibilityHint}
      accessibilityRole="button"
      testID={testID}
    >
      {/* Left Icon */}
      {leftIcon && !loading && (
        <View style={{ marginRight: tokens.spacing.xs }}>
          {leftIcon}
        </View>
      )}
      
      {/* Loading Indicator */}
      {loading && (
        <View style={{ marginRight: tokens.spacing.xs }}>
          <ActivityIndicator 
            size="small" 
            color={getTextColor()} 
          />
        </View>
      )}
      
      {/* Title */}
      <Text style={finalTextStyle}>
        {loading ? 'Loading...' : title}
      </Text>
      
      {/* Right Icon */}
      {rightIcon && !loading && (
        <View style={{ marginLeft: tokens.spacing.xs }}>
          {rightIcon}
        </View>
      )}
    </TouchableOpacity>
  );
};

// =============================================================================
// VARIANTS
// =============================================================================

/**
 * Primary Button - Main call-to-action
 */
export const PrimaryButton: React.FC<Omit<ButtonProps, 'variant'> & { theme: Theme }> = (props) => (
  <Button {...props} variant="primary" />
);

/**
 * Secondary Button - Secondary actions
 */
export const SecondaryButton: React.FC<Omit<ButtonProps, 'variant'> & { theme: Theme }> = (props) => (
  <Button {...props} variant="secondary" />
);

/**
 * Outline Button - Subtle actions
 */
export const OutlineButton: React.FC<Omit<ButtonProps, 'variant'> & { theme: Theme }> = (props) => (
  <Button {...props} variant="outline" />
);

/**
 * Ghost Button - Minimal actions
 */
export const GhostButton: React.FC<Omit<ButtonProps, 'variant'> & { theme: Theme }> = (props) => (
  <Button {...props} variant="ghost" />
);

// =============================================================================
// LEGACY SUPPORT
// =============================================================================

/**
 * @deprecated Use Button component instead
 */
export const DSButton = Button;

export default Button;
