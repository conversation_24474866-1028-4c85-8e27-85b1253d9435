import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Alert,
  ActivityIndicator
} from 'react-native';

import { 
  Sparkles, 
  Shuffle, 
  Heart, 
  Utensils,
  Star,
  Zap,
  Gift
} from 'lucide-react-native';
import { colors } from '../../utils/colors';
// Removed animations for simpler implementation

const { width } = Dimensions.get('window');

export interface SurpriseItem {
  id: string;
  title: string;
  description?: string;
  emoji?: string;
  category?: string;
  difficulty?: string;
  estimatedDuration?: number;
  costLevel?: string;
  indoorOutdoor?: string;
  prepTime?: number;
  cookTime?: number;
  servings?: number;
  ingredients?: string[];
  instructions?: string[];
  tags?: string[];
  source?: string;
  weekNumber?: number;
}

export interface SurpriseMeProps {
  type: 'date-night' | 'meal' | 'combined';
  onSurprise: () => Promise<SurpriseItem[]>;
  onItemSelect?: (item: SurpriseItem) => void;
  onItemSave?: (item: SurpriseItem) => void;
  onItemPlan?: (item: SurpriseItem) => void;
  title?: string;
  subtitle?: string;
  buttonText?: string;
  backgroundColor?: string;
  icon?: React.ReactNode;
  maxItems?: number;
  showLoadingState?: boolean;
  disabled?: boolean;
  style?: any;
}

export default function SurpriseMe({
  type,
  onSurprise,
  onItemSelect,
  onItemSave,
  onItemPlan,
  title = "Surprise Me!",
  subtitle = "Get random suggestions",
  buttonText = "🎲 Surprise Me!",
  backgroundColor = colors.accent1,
  icon,
  maxItems = 3,
  showLoadingState = true,
  disabled = false,
  style
}: SurpriseMeProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [surpriseItems, setSurpriseItems] = useState<SurpriseItem[]>([]);
  const [showResults, setShowResults] = useState(false);

  const handleSurprise = useCallback(async () => {
    if (disabled || isLoading) return;

    try {
      setIsLoading(true);
      setShowResults(false);
      
      // Get surprise items
      const items = await onSurprise();
      const limitedItems = items.slice(0, maxItems);
      
      setSurpriseItems(limitedItems);
      setShowResults(true);
      
    } catch (error) {
      console.error('Error getting surprise items:', error);
      Alert.alert('Oops!', 'Something went wrong. Please try again!');
    } finally {
      setIsLoading(false);
    }
  }, [onSurprise, maxItems, disabled, isLoading]);

  const handleItemAction = useCallback((item: SurpriseItem, action: 'select' | 'save' | 'plan') => {
    switch (action) {
      case 'select':
        onItemSelect?.(item);
        break;
      case 'save':
        onItemSave?.(item);
        break;
      case 'plan':
        onItemPlan?.(item);
        break;
    }
  }, [onItemSelect, onItemSave, onItemPlan]);

  const clearResults = useCallback(() => {
    setShowResults(false);
    setSurpriseItems([]);
  }, []);

  const getTypeIcon = () => {
    if (icon) return icon;
    
    switch (type) {
      case 'date-night':
        return <Heart size={24} color={colors.white} />;
      case 'meal':
        return <Utensils size={24} color={colors.white} />;
      case 'combined':
        return <Star size={24} color={colors.white} />;
      default:
        return <Sparkles size={24} color={colors.white} />;
    }
  };

  const getTypeEmoji = () => {
    switch (type) {
      case 'date-night':
        return '💕';
      case 'meal':
        return '🍽️';
      case 'combined':
        return '✨';
      default:
        return '🎲';
    }
  };

  const renderSurpriseItem = (item: SurpriseItem, index: number) => {
    const isDateNight = type === 'date-night' || (type === 'combined' && !item.ingredients);
    const isMeal = type === 'meal' || (type === 'combined' && item.ingredients);
    
    return (
      <View
        key={item.id}
        style={styles.resultCard}
      >
        <View
          style={[styles.resultCardGradient, { backgroundColor: isDateNight ? colors.primary : colors.secondary }]}
        >
          <View style={styles.resultCardHeader}>
            <View style={styles.resultCardTitleRow}>
              {item.emoji && <Text style={styles.resultEmoji}>{item.emoji}</Text>}
              <Text style={styles.resultTitle} numberOfLines={1}>
                {item.title}
              </Text>
            </View>
            <View style={styles.resultTypeBadge}>
              <Text style={styles.resultTypeText}>
                {isDateNight ? '💕' : '🍽️'}
              </Text>
            </View>
          </View>

          {item.description && (
            <Text style={styles.resultDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}

          <View style={styles.resultMeta}>
            {item.category && (
              <View style={styles.metaBadge}>
                <Text style={styles.metaBadgeText}>{item.category}</Text>
              </View>
            )}
            {item.difficulty && (
              <View style={styles.metaBadge}>
                <Text style={styles.metaBadgeText}>{item.difficulty}</Text>
              </View>
            )}
            {item.estimatedDuration && (
              <View style={styles.metaBadge}>
                <Text style={styles.metaBadgeText}>{item.estimatedDuration}min</Text>
              </View>
            )}
            {item.prepTime && (
              <View style={styles.metaBadge}>
                <Text style={styles.metaBadgeText}>{item.prepTime}min prep</Text>
              </View>
            )}
            {item.servings && (
              <View style={styles.metaBadge}>
                <Text style={styles.metaBadgeText}>{item.servings} servings</Text>
              </View>
            )}
          </View>

          <View style={styles.resultActions}>
            {onItemSelect && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleItemAction(item, 'select')}
              >
                <Text style={styles.actionButtonText}>View</Text>
              </TouchableOpacity>
            )}
            {onItemSave && (
              <TouchableOpacity
                style={[styles.actionButton, styles.saveButton]}
                onPress={() => handleItemAction(item, 'save')}
              >
                <Heart size={14} color={colors.white} />
                <Text style={styles.actionButtonText}>Save</Text>
              </TouchableOpacity>
            )}
            {onItemPlan && (
              <TouchableOpacity
                style={[styles.actionButton, styles.planButton]}
                onPress={() => handleItemAction(item, 'plan')}
              >
                <Zap size={14} color={colors.white} />
                <Text style={styles.actionButtonText}>Plan</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {/* Main Surprise Button */}
      <TouchableOpacity
        style={[styles.surpriseButton, disabled && styles.disabledButton]}
        onPress={handleSurprise}
        disabled={disabled || isLoading}
        activeOpacity={0.8}
      >
          <View
            style={[styles.surpriseButtonGradient, { backgroundColor }]}
          >
            <View style={styles.surpriseButtonContent}>
              {isLoading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <>
                  {getTypeIcon()}
                  <View style={styles.surpriseButtonTextContainer}>
                    <Text style={styles.surpriseButtonText}>
                      {buttonText}
                    </Text>
                    <Text style={styles.surpriseButtonSubtext}>
                      {subtitle}
                    </Text>
                  </View>
                  <Shuffle size={20} color={colors.white} />
                </>
              )}
            </View>
          </View>
        </TouchableOpacity>

      {/* Results Section */}
      {showResults && surpriseItems.length > 0 && (
        <View style={styles.resultsContainer}>
          <View style={styles.resultsHeader}>
            <View style={styles.resultsTitleContainer}>
              <Text style={styles.resultsTitle}>
                {getTypeEmoji()} Your Surprise Picks!
              </Text>
              <Text style={styles.resultsSubtitle}>
                {surpriseItems.length} {type === 'combined' ? 'suggestions' : type === 'date-night' ? 'date nights' : 'meals'} found
              </Text>
            </View>
            <TouchableOpacity
              style={styles.clearButton}
              onPress={clearResults}
            >
              <Text style={styles.clearButtonText}>Clear</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.resultsList}>
            {surpriseItems.map((item, index) => renderSurpriseItem(item, index))}
          </View>
        </View>
      )}

      {/* Removed confetti animation */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  surpriseButton: {
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 4,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  surpriseButtonGradient: {
    paddingVertical: 20,
    paddingHorizontal: 24,
  },
  surpriseButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  surpriseButtonTextContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  surpriseButtonText: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.white,
    textAlign: 'center',
  },
  surpriseButtonSubtext: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 2,
  },
  resultsContainer: {
    marginTop: 20,
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 16,
    padding: 16,
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  resultsTitleContainer: {
    flex: 1,
  },
  resultsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  resultsSubtitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 2,
  },
  clearButton: {
    backgroundColor: colors.backgroundTertiary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  clearButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  resultsList: {
    gap: 12,
  },
  resultCard: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  resultCardGradient: {
    padding: 16,
  },
  resultCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultCardTitleRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  resultEmoji: {
    fontSize: 20,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    flex: 1,
  },
  resultTypeBadge: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  resultTypeText: {
    fontSize: 12,
  },
  resultDescription: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
    lineHeight: 18,
    marginBottom: 12,
  },
  resultMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginBottom: 12,
  },
  metaBadge: {
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  metaBadgeText: {
    fontSize: 11,
    fontWeight: '500',
    color: colors.primary,
  },
  resultActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    backgroundColor: colors.white,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  saveButton: {
    backgroundColor: colors.error,
  },
  planButton: {
    backgroundColor: colors.success,
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
  },
});
