/**
 * Error Boundary Component
 * 
 * React Error Boundary that catches JavaScript errors anywhere in the child
 * component tree, logs those errors, and displays a fallback UI.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import React, { Component, ReactNode } from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { simpleErrorService } from '../../services/system/simpleErrorService';
import { CONTENT_CONSTANTS, UI_CONSTANTS } from '../../utils/constants';
import { tokens } from '../../utils/theme';
import type { Theme } from '../../utils/theme/colors';

// =============================================================================
// TYPES
// =============================================================================

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  theme: Theme;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

// =============================================================================
// COMPONENT
// =============================================================================

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Update state with error info
    this.setState({
      error,
      errorInfo,
    });

    // Report to error service
    simpleErrorService.reportError(error, {
      component: 'ErrorBoundary',
      action: 'componentDidCatch',
      metadata: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
      },
    });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <View style={[styles.container, { backgroundColor: this.props.theme.background }]}>
          <View style={[styles.content, { backgroundColor: this.props.theme.surface }]}>
            <Text style={[styles.title, { color: this.props.theme.textPrimary }]}>
              Oops! Something went wrong
            </Text>
            
            <Text style={[styles.message, { color: this.props.theme.textSecondary }]}>
              We're sorry, but something unexpected happened. Please try again.
            </Text>

            {__DEV__ && this.state.error && (
              <View style={[styles.debugInfo, { backgroundColor: this.props.theme.backgroundTertiary }]}>
                <Text style={[styles.debugTitle, { color: this.props.theme.error }]}>
                  Debug Info (Development Only):
                </Text>
                <Text style={[styles.debugText, { color: this.props.theme.textTertiary }]}>
                  {this.state.error.toString()}
                </Text>
                {this.state.errorInfo && (
                  <Text style={[styles.debugText, { color: this.props.theme.textTertiary }]}>
                    {this.state.errorInfo.componentStack}
                  </Text>
                )}
              </View>
            )}

            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: this.props.theme.primary }]}
              onPress={this.handleRetry}
            >
              <Text style={[styles.retryButtonText, { color: this.props.theme.textInverse }]}>
                Try Again
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

// =============================================================================
// STYLES
// =============================================================================

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: tokens.spacing.lg,
  },
  content: {
    padding: tokens.spacing.xl,
    borderRadius: tokens.radii.lg,
    alignItems: 'center',
    maxWidth: 400,
    width: '100%',
    ...tokens.shadows.md,
  },
  title: {
    fontSize: tokens.typography.fontSize['2xl'],
    fontWeight: tokens.typography.fontWeight.bold,
    textAlign: 'center',
    marginBottom: tokens.spacing.md,
  },
  message: {
    fontSize: tokens.typography.fontSize.md,
    textAlign: 'center',
    lineHeight: tokens.typography.lineHeight.relaxed,
    marginBottom: tokens.spacing.xl,
  },
  debugInfo: {
    padding: tokens.spacing.md,
    borderRadius: tokens.radii.sm,
    marginBottom: tokens.spacing.lg,
    width: '100%',
  },
  debugTitle: {
    fontSize: tokens.typography.fontSize.sm,
    fontWeight: tokens.typography.fontWeight.semibold,
    marginBottom: tokens.spacing.xs,
  },
  debugText: {
    fontSize: tokens.typography.fontSize.xs,
    fontFamily: 'monospace',
    marginBottom: tokens.spacing.xs,
  },
  retryButton: {
    paddingHorizontal: tokens.spacing.xl,
    paddingVertical: tokens.spacing.md,
    borderRadius: tokens.radii.sm,
    minWidth: 120,
  },
  retryButtonText: {
    fontSize: tokens.typography.fontSize.md,
    fontWeight: tokens.typography.fontWeight.semibold,
    textAlign: 'center',
  },
});

// =============================================================================
// HOC WRAPPER
// =============================================================================

/**
 * Higher-order component that wraps a component with ErrorBoundary
 */
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
) => {
  return React.forwardRef<any, P & { theme: Theme }>((props, ref) => (
    <ErrorBoundary fallback={fallback} onError={onError} theme={props.theme}>
      <Component {...props} ref={ref} />
    </ErrorBoundary>
  ));
};

export default ErrorBoundary;
