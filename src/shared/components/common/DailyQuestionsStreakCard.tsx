/**
 * Daily Questions Streak Card Component
 *
 * Displays streak information and achievements for daily questions.
 * Provides visual feedback and motivation for consistent participation.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { router } from 'expo-router';
import { Flame, Target, Trophy } from 'lucide-react-native';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useDailyQuestions } from '../../journeys/daily/useDailyQuestions';
import { tokens } from '../../utils/theme';
import { DSButton, DSCard } from './index';
import { useGlobalTheme } from './ThemeProvider';

export interface DailyQuestionsStreakCardProps {
  style?: any;
  onPress?: () => void;
  showFullDetails?: boolean;
}

export const DailyQuestionsStreakCard: React.FC<DailyQuestionsStreakCardProps> = ({
  style,
  onPress,
  showFullDetails = false
}) => {
  const { currentTheme } = useGlobalTheme();
  const { streakData, isLoading } = useDailyQuestions();

  const handleCardPress = () => {
    if (onPress) {
      onPress();
    } else {
      router.push('/daily-questions-achievements');
    }
  };

  const getStreakEmoji = (streak: number) => {
    if (streak >= 100) return '💎';
    if (streak >= 30) return '🏆';
    if (streak >= 14) return '💪';
    if (streak >= 7) return '🌟';
    if (streak >= 3) return '🔥';
    return '💬';
  };

  const getStreakMessage = (streak: number) => {
    if (streak >= 100) return 'Incredible! You\'re relationship champions!';
    if (streak >= 30) return 'One month strong! Amazing dedication!';
    if (streak >= 14) return 'Two weeks! You\'re building something special!';
    if (streak >= 7) return 'One week strong! Amazing dedication!';
    if (streak >= 3) return 'You\'re on fire! Keep it up!';
    if (streak >= 1) return 'Great start! Every day counts!';
    return 'Start your streak today!';
  };

  const getNextMilestone = (currentStreak: number) => {
    const milestones = [3, 7, 14, 30, 100];
    return milestones.find(milestone => milestone > currentStreak) || null;
  };

  const getProgressPercentage = (currentStreak: number, nextMilestone: number | null) => {
    if (!nextMilestone) return 100;
    return Math.min((currentStreak / nextMilestone) * 100, 100);
  };

  if (isLoading || !streakData) {
    return (
      <DSCard style={[styles.card, style]}>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: currentTheme.textSecondary }]}>
            Loading streak data...
          </Text>
        </View>
      </DSCard>
    );
  }

  const nextMilestone = getNextMilestone(streakData.current_streak);
  const progressPercentage = getProgressPercentage(streakData.current_streak, nextMilestone);

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <DSCard style={[styles.card, style]}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.titleRow}>
            <Text style={[styles.title, { color: currentTheme.textPrimary }]}>
              Daily Questions Streak
            </Text>
            <Text style={styles.streakEmoji}>
              {getStreakEmoji(streakData.current_streak)}
            </Text>
          </View>

          <Text style={[styles.streakMessage, { color: currentTheme.textSecondary }]}>
            {getStreakMessage(streakData.current_streak)}
          </Text>
        </View>

        {/* Streak Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <View style={[styles.statIcon, { backgroundColor: currentTheme.error }]}>
              <Flame size={16} color="white" />
            </View>
            <View style={styles.statInfo}>
              <Text style={[styles.statNumber, { color: currentTheme.textPrimary }]}>
                {streakData.current_streak}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.textSecondary }]}>
                Current Streak
              </Text>
            </View>
          </View>

          <View style={styles.statItem}>
            <View style={[styles.statIcon, { backgroundColor: currentTheme.warning }]}>
              <Trophy size={16} color="white" />
            </View>
            <View style={styles.statInfo}>
              <Text style={[styles.statNumber, { color: currentTheme.textPrimary }]}>
                {streakData.longest_streak}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.textSecondary }]}>
                Longest Streak
              </Text>
            </View>
          </View>

          <View style={styles.statItem}>
            <View style={[styles.statIcon, { backgroundColor: currentTheme.primary }]}>
              <Target size={16} color="white" />
            </View>
            <View style={styles.statInfo}>
              <Text style={[styles.statNumber, { color: currentTheme.textPrimary }]}>
                {streakData.total_questions_answered}
              </Text>
              <Text style={[styles.statLabel, { color: currentTheme.textSecondary }]}>
                Total Answered
              </Text>
            </View>
          </View>
        </View>

        {/* Progress to Next Milestone */}
        {nextMilestone && (
          <View style={styles.progressContainer}>
            <View style={styles.progressHeader}>
              <Text style={[styles.progressLabel, { color: currentTheme.textSecondary }]}>
                Next Milestone: {nextMilestone} days
              </Text>
              <Text style={[styles.progressText, { color: currentTheme.textSecondary }]}>
                {streakData.current_streak}/{nextMilestone}
              </Text>
            </View>

            <View style={[styles.progressBar, { backgroundColor: currentTheme.border }]}>
              <View
                style={[
                  styles.progressFill,
                  {
                    backgroundColor: currentTheme.primary,
                    width: `${progressPercentage}%`
                  }
                ]}
              />
            </View>
          </View>
        )}

        {/* Achievements Preview */}
        {showFullDetails && (
          <View style={styles.achievementsContainer}>
            <Text style={[styles.achievementsTitle, { color: currentTheme.textPrimary }]}>
              Recent Achievements
            </Text>

            <View style={styles.achievementsList}>
              {streakData.current_streak >= 3 && (
                <View style={styles.achievementItem}>
                  <Text style={styles.achievementEmoji}>🔥</Text>
                  <Text style={[styles.achievementText, { color: currentTheme.textSecondary }]}>
                    3 Day Streak
                  </Text>
                </View>
              )}

              {streakData.current_streak >= 7 && (
                <View style={styles.achievementItem}>
                  <Text style={styles.achievementEmoji}>🌟</Text>
                  <Text style={[styles.achievementText, { color: currentTheme.textSecondary }]}>
                    1 Week Strong
                  </Text>
                </View>
              )}

              {streakData.current_streak >= 14 && (
                <View style={styles.achievementItem}>
                  <Text style={styles.achievementEmoji}>💪</Text>
                  <Text style={[styles.achievementText, { color: currentTheme.textSecondary }]}>
                    2 Weeks Strong
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Action Button */}
        <View style={styles.actionContainer}>
          <DSButton
            title={showFullDetails ? "View All Achievements" : "View Details"}
            onPress={handleCardPress}
            variant="outline"
            size="sm"
          />
        </View>
      </DSCard>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: tokens.spacing.lg,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: tokens.spacing.lg,
  },
  loadingText: {
    fontSize: tokens.fontSizes.sm,
  },
  header: {
    marginBottom: tokens.spacing.lg,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: tokens.spacing.xs,
  },
  title: {
    fontSize: tokens.fontSizes.lg,
    fontWeight: tokens.fontWeights.semibold,
  },
  streakEmoji: {
    fontSize: 24,
  },
  streakMessage: {
    fontSize: tokens.fontSizes.sm,
    lineHeight: tokens.lineHeights.tight,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: tokens.spacing.lg,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: tokens.spacing.sm,
  },
  statInfo: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: tokens.fontSizes.lg,
    fontWeight: tokens.fontWeights.bold,
    marginBottom: tokens.spacing.xs,
  },
  statLabel: {
    fontSize: tokens.fontSizes.xs,
    textAlign: 'center',
  },
  progressContainer: {
    marginBottom: tokens.spacing.lg,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: tokens.spacing.sm,
  },
  progressLabel: {
    fontSize: tokens.fontSizes.sm,
    fontWeight: tokens.fontWeights.medium,
  },
  progressText: {
    fontSize: tokens.fontSizes.sm,
    fontWeight: tokens.fontWeights.medium,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  achievementsContainer: {
    marginBottom: tokens.spacing.lg,
  },
  achievementsTitle: {
    fontSize: tokens.fontSizes.md,
    fontWeight: tokens.fontWeights.semibold,
    marginBottom: tokens.spacing.sm,
  },
  achievementsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: tokens.spacing.sm,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingHorizontal: tokens.spacing.sm,
    paddingVertical: tokens.spacing.xs,
    borderRadius: tokens.radii.sm,
  },
  achievementEmoji: {
    fontSize: 16,
    marginRight: tokens.spacing.xs,
  },
  achievementText: {
    fontSize: tokens.fontSizes.xs,
    fontWeight: tokens.fontWeights.medium,
  },
  actionContainer: {
    alignItems: 'center',
  },
});
