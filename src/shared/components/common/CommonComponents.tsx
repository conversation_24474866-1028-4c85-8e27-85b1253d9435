import React from 'react';
import { View, Text, TouchableOpacity, TextInput, Modal, ScrollView, Dimensions, StyleSheet } from 'react-native';

import { colors } from '../../utils/colors';
import { cardStyles, buttonStyles, inputStyles, avatarStyles, textStyles } from '../../utils/sharedStyles';

const { width } = Dimensions.get('window');

// Common Card Component
interface CardProps {
  children: React.ReactNode;
  style?: any;
}

export const Card: React.FC<CardProps> = ({ children, style }) => (
  <View style={[cardStyles.container, style]}>
    {children}
  </View>
);

// Common Button Component (no gradients)
interface ButtonProps {
  title: string;
  onPress: () => void;
  backgroundColor: string;
  disabled?: boolean;
  icon?: React.ReactNode;
  style?: any;
}

export const SolidButton: React.FC<ButtonProps> = ({
  title,
  onPress,
  backgroundColor,
  disabled = false,
  icon,
  style
}) => (
  <TouchableOpacity
    style={[buttonStyles.primary, style]}
    onPress={onPress}
    disabled={disabled}
  >
    <View
      style={[buttonStyles.gradient, { backgroundColor }]}
    >
      <Text style={buttonStyles.text}>{title}</Text>
      {icon}
    </View>
  </TouchableOpacity>
);

// Common Input Component
interface InputProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  multiline?: boolean;
  numberOfLines?: number;
  style?: any;
}

export const StyledInput: React.FC<InputProps> = ({ 
  value, 
  onChangeText, 
  placeholder, 
  multiline = false, 
  numberOfLines = 1, 
  style 
}) => (
  <TextInput
    style={[
      inputStyles.container, 
      multiline && inputStyles.list, 
      style
    ]}
    placeholder={placeholder}
    value={value}
    onChangeText={onChangeText}
    multiline={multiline}
    numberOfLines={numberOfLines}
    textAlignVertical="top"
  />
);

// Common Avatar Component
interface AvatarProps {
  icon: React.ReactNode;
  size?: 'small' | 'medium';
  backgroundColor?: string;
  style?: any;
}

export const Avatar: React.FC<AvatarProps> = ({ 
  icon, 
  size = 'medium', 
  backgroundColor, 
  style 
}) => (
  <View style={[
    avatarStyles[size], 
    backgroundColor && { backgroundColor }, 
    style
  ]}>
    {icon}
  </View>
);

// Common Section Header Component
interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  style?: any;
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({ title, subtitle, style }) => (
  <View style={[cardStyles.header, style]}>
    <Text style={cardStyles.title}>{title}</Text>
    {subtitle && <Text style={cardStyles.subtitle}>{subtitle}</Text>}
  </View>
);

// Common Progress Bar Component
interface ProgressBarProps {
  current: number;
  total: number;
  backgroundColor?: string;
  fillColor?: string;
  style?: any;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({ 
  current, 
  total, 
  backgroundColor = colors.borderLight, 
  fillColor = colors.success, 
  style 
}) => {
  const progress = (current / total) * 100;
  
  return (
    <View style={[styles.progressBar, { backgroundColor }, style]}>
      <View 
        style={[
          styles.progressFill, 
          { 
            width: `${progress}%`,
            backgroundColor: fillColor
          }
        ]} 
      />
    </View>
  );
};

// Common Icon Text Component
interface IconTextProps {
  icon: React.ReactNode;
  text: string;
  style?: any;
}

export const IconText: React.FC<IconTextProps> = ({ icon, text, style }) => (
  <View style={[styles.iconText, style]}>
    {icon}
    <Text style={[textStyles.secondary, styles.iconTextLabel]}>{text}</Text>
  </View>
);

// Common Modal Component
interface ModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
}

export const CommonModal: React.FC<ModalProps> = ({ 
  visible, 
  onClose, 
  title, 
  children, 
  showCloseButton = true 
}) => (
  <Modal
    visible={visible}
    animationType="slide"
    transparent={true}
    onRequestClose={onClose}
  >
    <View style={styles.modalOverlay}>
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>{title}</Text>
          {showCloseButton && (
            <TouchableOpacity onPress={onClose}>
              <Text style={styles.closeButton}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
        <ScrollView style={styles.modalBody}>
          {children}
        </ScrollView>
      </View>
    </View>
  </Modal>
);

// NOTE: Legacy TabContainer/TabButton have been removed.
// Use the shared TopTabs component from components/shared/TopTabs instead.

// Common Empty State Component
interface EmptyStateProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  actionButton?: React.ReactNode;
}

export const EmptyState: React.FC<EmptyStateProps> = ({ 
  icon, 
  title, 
  subtitle, 
  actionButton 
}) => (
  <View style={styles.emptyState}>
    {icon}
    <Text style={styles.emptyStateText}>{title}</Text>
    {subtitle && <Text style={styles.emptyStateSubtext}>{subtitle}</Text>}
    {actionButton}
  </View>
);

// Common Stats Card Component
interface StatCardProps {
  number: string | number;
  label: string;
  icon?: React.ReactNode;
  color?: string;
}

export const StatCard: React.FC<StatCardProps> = ({ 
  number, 
  label, 
  icon, 
  color = colors.primary 
}) => (
  <View style={styles.statCard}>
    {icon && (
      <View style={[styles.statIconContainer, { backgroundColor: color }]}>
        {icon}
      </View>
    )}
    <Text style={styles.statNumber}>{number}</Text>
    <Text style={styles.statLabel}>{label}</Text>
  </View>
);

// Common Search Bar Component
interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  onFilterPress?: () => void;
  showFilter?: boolean;
}

export const SearchBar: React.FC<SearchBarProps> = ({ 
  value, 
  onChangeText, 
  placeholder = "Search...", 
  onFilterPress,
  showFilter = false 
}) => (
  <View style={styles.searchFilterBar}>
    <View style={styles.searchContainer}>
      <Text style={styles.searchIcon}>🔍</Text>
      <TextInput
        style={styles.searchInput}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        placeholderTextColor={colors.textTertiary}
      />
    </View>
    {showFilter && onFilterPress && (
      <TouchableOpacity style={styles.filterButton} onPress={onFilterPress}>
        <Text style={styles.filterIcon}>⚙️</Text>
      </TouchableOpacity>
    )}
  </View>
);

// Common Quick Action Button Component
interface QuickActionButtonProps {
  title: string;
  onPress: () => void;
  backgroundColor?: string;
  icon?: React.ReactNode;
  style?: any;
}

export const QuickActionButton: React.FC<QuickActionButtonProps> = ({ 
  title, 
  onPress, 
  backgroundColor = colors.primary,
  icon, 
  style 
}) => (
  <TouchableOpacity style={[styles.quickActionButton, style]} onPress={onPress}>
    <View style={[styles.quickActionButtonGradient, { backgroundColor }]}>
      {icon}
      <Text style={styles.quickActionButtonText}>{title}</Text>
    </View>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  progressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    marginBottom: 12,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  iconText: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconTextLabel: {
    marginLeft: 8,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: 20,
    width: width - 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
  },
  closeButton: {
    fontSize: 24,
    color: colors.textSecondary,
  },
  modalBody: {
    padding: 20,
  },
  // Legacy tab styles removed; see TopTabs for current implementation
  // Empty state styles
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: colors.textTertiary,
    textAlign: 'center',
  },
  // Stat card styles
  statCard: {
    flex: 1,
    backgroundColor: colors.backgroundSecondary,
    marginHorizontal: 4,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundPrimary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  // Search bar styles
  searchFilterBar: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  searchIcon: {
    marginRight: 8,
    fontSize: 16,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: colors.textPrimary,
  },
  filterButton: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  filterIcon: {
    fontSize: 16,
  },
  // Quick action button styles
  quickActionButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  quickActionButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    gap: 8,
  },
  quickActionButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
