/**
 * Streak Display Component
 *
 * Displays user streak information with dynamic styling and icons.
 * Integrates with the existing design system and theme provider.
 * Uses custom hook for data management and proper error handling.
 *
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React from 'react';
import { ActivityIndicator, StyleSheet, Text, View, ViewStyle } from 'react-native';
import { useStreakData } from '../../../journeys/daily/useStreakData';
import { tokens } from '../../utils/theme';
import { ThemedText } from './ThemedText';
import { useGlobalTheme } from './ThemeProvider';

export interface StreakDisplayProps {
  /** Category filter for streak calculation */
  category?: string;
  /** Whether to show the streak icon */
  showIcon?: boolean;
  /** Size variant of the component */
  size?: 'small' | 'medium' | 'large';
  /** Custom styles */
  style?: ViewStyle;
  /** Whether to show activity data */
  showActivity?: boolean;
  /** Auto-refresh interval in milliseconds */
  refreshInterval?: number;
}

export const StreakDisplay: React.FC<StreakDisplayProps> = ({
  category,
  showIcon = true,
  size = 'medium',
  style,
  showActivity = false,
  refreshInterval = 30000
}) => {
  const { currentTheme } = useGlobalTheme();
  const {
    streak,
    loading,
    error,
    refresh,
    activity,
    loadingActivity,
    activityError
  } = useStreakData({
    category,
    refreshInterval,
    autoRefresh: true
  });

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          container: styles.smallContainer,
          text: styles.smallText,
          icon: styles.smallIcon
        };
      case 'large':
        return {
          container: styles.largeContainer,
          text: styles.largeText,
          icon: styles.largeIcon
        };
      default:
        return {
          container: styles.mediumContainer,
          text: styles.mediumText,
          icon: styles.mediumIcon
        };
    }
  };

  const getStreakColor = (): string => {
    if (error) return currentTheme.error;
    if (streak === 0) return currentTheme.textSecondary;
    if (streak < 7) return currentTheme.primary;
    if (streak < 30) return currentTheme.success;
    return currentTheme.warning;
  };

  const getStreakIcon = (): string => {
    if (error) return '⚠️';
    if (streak === 0) return '💤';
    if (streak < 7) return '🔥';
    if (streak < 30) return '⚡';
    return '🏆';
  };

  const handleRefresh = (): void => {
    refresh().catch(err => {
      console.error('Error refreshing streak:', err);
    });
  };

  // Loading state
  if (loading) {
    return (
      <View style={[styles.loadingContainer, style]}>
        <ActivityIndicator size="small" color={currentTheme.primary} />
        <ThemedText variant="caption" style={styles.loadingText}>
          Loading streak...
        </ThemedText>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={[styles.errorContainer, { backgroundColor: currentTheme.backgroundSecondary }, style]}>
        <ThemedText variant="caption" style={[styles.errorText, { color: currentTheme.error }]}>
          Failed to load streak
        </ThemedText>
        <ThemedText
          variant="caption"
          style={[styles.retryText, { color: currentTheme.primary }]}
          onPress={handleRefresh}
        >
          Tap to retry
        </ThemedText>
      </View>
    );
  }

  const sizeClasses = getSizeClasses();

  return (
    <View style={[sizeClasses.container, { backgroundColor: currentTheme.backgroundSecondary }, style]}>
      {showIcon && (
        <Text style={sizeClasses.icon}>
          {getStreakIcon()}
        </Text>
      )}
      <ThemedText style={[sizeClasses.text, { color: getStreakColor() }]}>
        {streak} day{streak !== 1 ? 's' : ''}
      </ThemedText>
      {streak > 0 && (
        <ThemedText variant="caption" style={[styles.streakLabel, { color: currentTheme.textSecondary }]}>
          streak
        </ThemedText>
      )}
      {showActivity && activity.length > 0 && (
        <ThemedText variant="caption" style={[styles.activityText, { color: currentTheme.textTertiary }]}>
          {activity.length} recent activities
        </ThemedText>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    padding: tokens.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: tokens.spacing.xs,
  },
  errorContainer: {
    padding: tokens.spacing.sm,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: tokens.radii.md,
  },
  errorText: {
    textAlign: 'center',
    marginBottom: tokens.spacing.xs,
  },
  retryText: {
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
  smallContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing.sm,
    paddingVertical: tokens.spacing.xs,
    borderRadius: tokens.radii.sm,
  },
  mediumContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing.md,
    paddingVertical: tokens.spacing.sm,
    borderRadius: tokens.radii.md,
  },
  largeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: tokens.spacing.lg,
    paddingVertical: tokens.spacing.md,
    borderRadius: tokens.radii.lg,
  },
  smallText: {
    fontSize: 14,
    fontWeight: '500',
  },
  mediumText: {
    fontSize: 16,
    fontWeight: '600',
  },
  largeText: {
    fontSize: 18,
    fontWeight: '700',
  },
  smallIcon: {
    fontSize: 14,
    marginRight: tokens.spacing.xs,
  },
  mediumIcon: {
    fontSize: 18,
    marginRight: tokens.spacing.sm,
  },
  largeIcon: {
    fontSize: 24,
    marginRight: tokens.spacing.md,
  },
  streakLabel: {
    fontSize: 12,
    marginLeft: tokens.spacing.xs,
    opacity: 0.75,
  },
  activityText: {
    fontSize: 10,
    marginTop: tokens.spacing.xs,
    textAlign: 'center',
  },
});
