/**
 * Lazy Wrapper Component
 * 
 * Provides lazy loading functionality for components to improve performance.
 * Only renders children when they become visible in the viewport.
 * 
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import React, { useState, useRef, useEffect, ReactNode } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { tokens } from '../../utils/theme';
import type { Theme } from '../../utils/theme/colors';

// =============================================================================
// TYPES
// =============================================================================

interface LazyWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
  threshold?: number;
  rootMargin?: string;
  style?: any;
  theme: Theme;
  disabled?: boolean;
}

// =============================================================================
// COMPONENT
// =============================================================================

export const LazyWrapper: React.FC<LazyWrapperProps> = ({
  children,
  fallback,
  threshold = 0.1,
  rootMargin = '50px',
  style,
  theme,
  disabled = false,
}) => {
  const [isVisible, setIsVisible] = useState(disabled);
  const [hasLoaded, setHasLoaded] = useState(disabled);
  const ref = useRef<View>(null);

  useEffect(() => {
    if (disabled || hasLoaded) return;

    // For React Native, we'll use a simple timeout-based approach
    // In a real implementation, you might want to use react-native-intersection-observer
    // or implement a custom solution based on scroll position
    
    const timer = setTimeout(() => {
      setIsVisible(true);
      setHasLoaded(true);
    }, 100);

    return () => clearTimeout(timer);
  }, [disabled, hasLoaded]);

  // Default fallback component
  const defaultFallback = (
    <View style={[styles.fallback, { backgroundColor: theme.backgroundSecondary }]}>
      <ActivityIndicator size="small" color={theme.primary} />
    </View>
  );

  return (
    <View ref={ref} style={style}>
      {isVisible ? children : (fallback || defaultFallback)}
    </View>
  );
};

// =============================================================================
// INTERSECTION OBSERVER WRAPPER (Web Only)
// =============================================================================

interface IntersectionLazyWrapperProps extends LazyWrapperProps {
  onIntersect?: (isIntersecting: boolean) => void;
}

export const IntersectionLazyWrapper: React.FC<IntersectionLazyWrapperProps> = ({
  children,
  fallback,
  threshold = 0.1,
  rootMargin = '50px',
  style,
  theme,
  disabled = false,
  onIntersect,
}) => {
  const [isVisible, setIsVisible] = useState(disabled);
  const [hasLoaded, setHasLoaded] = useState(disabled);
  const ref = useRef<any>(null);

  useEffect(() => {
    if (disabled || hasLoaded || typeof window === 'undefined') return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isIntersecting = entry.isIntersecting;
        
        if (isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
          observer.disconnect();
        }
        
        onIntersect?.(isIntersecting);
      },
      {
        threshold,
        rootMargin,
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [disabled, hasLoaded, threshold, rootMargin, onIntersect]);

  // Default fallback component
  const defaultFallback = (
    <View style={[styles.fallback, { backgroundColor: theme.backgroundSecondary }]}>
      <ActivityIndicator size="small" color={theme.primary} />
    </View>
  );

  return (
    <View ref={ref} style={style}>
      {isVisible ? children : (fallback || defaultFallback)}
    </View>
  );
};

// =============================================================================
// LAZY IMAGE WRAPPER
// =============================================================================

interface LazyImageWrapperProps {
  source: { uri: string } | number;
  style?: any;
  fallback?: ReactNode;
  theme: Theme;
  onLoad?: () => void;
  onError?: () => void;
}

export const LazyImageWrapper: React.FC<LazyImageWrapperProps> = ({
  source,
  style,
  fallback,
  theme,
  onLoad,
  onError,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  const defaultFallback = (
    <View style={[styles.imageFallback, style, { backgroundColor: theme.backgroundTertiary }]}>
      <ActivityIndicator size="small" color={theme.primary} />
    </View>
  );

  const errorFallback = (
    <View style={[styles.imageFallback, style, { backgroundColor: theme.backgroundTertiary }]}>
      {/* You could add an error icon here */}
    </View>
  );

  if (hasError) {
    return errorFallback;
  }

  return (
    <LazyWrapper theme={theme} style={style}>
      <View style={style}>
        {/* In React Native, you would use Image component here */}
        {/* <Image
          source={source}
          style={style}
          onLoad={handleLoad}
          onError={handleError}
        /> */}
        {!isLoaded && (fallback || defaultFallback)}
      </View>
    </LazyWrapper>
  );
};

// =============================================================================
// HOC FOR LAZY LOADING
// =============================================================================

/**
 * Higher-order component that adds lazy loading to any component
 */
export const withLazyLoading = <P extends object>(
  Component: React.ComponentType<P>,
  options: {
    threshold?: number;
    rootMargin?: string;
    fallback?: ReactNode;
  } = {}
) => {
  return React.forwardRef<any, P & { theme: Theme }>((props, ref) => {
    const { theme, ...componentProps } = props;
    return (
      <LazyWrapper
        theme={theme}
        threshold={options.threshold}
        rootMargin={options.rootMargin}
        fallback={options.fallback}
      >
        <Component {...(componentProps as P)} ref={ref} />
      </LazyWrapper>
    );
  });
};

// =============================================================================
// STYLES
// =============================================================================

const styles = StyleSheet.create({
  fallback: {
    minHeight: 100,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: tokens.radii.md,
  },
  imageFallback: {
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: tokens.radii.sm,
  },
});

export default LazyWrapper;
