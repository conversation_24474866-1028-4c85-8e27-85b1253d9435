/**
 * UI Components Index
 *
 * Centralized export for all UI components. This replaces the scattered
 * component exports and provides a single source of truth for the design system.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

// =============================================================================
// CORE COMPONENTS
// =============================================================================

// Button Components
export {
    Button, DSButton, GhostButton, OutlineButton, PrimaryButton,
    SecondaryButton
} from './Button';

export type { ButtonProps } from './Button';

// Card Components
export {
    Card,
    ElevatedCard, FilledCard, OutlinedCard, StatCard
} from './Card';

export type { CardProps, StatCardProps } from './Card';

// Legacy aliases for backward compatibility
export { Card as DSCard, StatCard as DSProgressBar } from './Card';

// Input Components
export {
    AuthInput, FilledInput, Input,
    OutlinedInput, Textarea
} from './Input';

export type { InputProps } from './Input';

// =============================================================================
// LAYOUT COMPONENTS
// =============================================================================

// These will be created in the next step
// export { Layout, ScreenLayout, HeaderLayout, ContentLayout } from './Layout';
// export { Stack, HStack, VStack } from './Stack';
// export { Grid, GridItem } from './Grid';

// =============================================================================
// FEEDBACK COMPONENTS
// =============================================================================

// These will be created in the next step
// export { Toast, Alert, Modal } from './Feedback';
// export { Loading, Spinner, Skeleton } from './Loading';

// =============================================================================
// NAVIGATION COMPONENTS
// =============================================================================

// These will be created in the next step
// export { TabBar, Header, Navigation } from './Navigation';

// =============================================================================
// DATA DISPLAY COMPONENTS
// =============================================================================

// These will be created in the next step
// export { Avatar, Badge, Tag } from './Display';
// export { List, ListItem } from './List';
// export { Table, TableRow, TableCell } from './Table';

// =============================================================================
// FORM COMPONENTS
// =============================================================================

// These will be created in the next step
// export { Form, FormField, FormGroup } from './Form';
// export { Select, Checkbox, Radio, Switch } from './FormControls';
// export { DatePicker, TimePicker } from './Pickers';

// =============================================================================
// MEDIA COMPONENTS
// =============================================================================

// These will be created in the next step
// export { Image, Video, Audio } from './Media';
// export { Gallery, Carousel } from './Gallery';

// =============================================================================
// UTILITY COMPONENTS
// =============================================================================

// These will be created in the next step
// export { Spacer, Divider, Separator } from './Utility';
// export { Portal, Overlay } from './Portal';

// =============================================================================
// LEGACY EXPORTS (TO BE DEPRECATED)
// =============================================================================

// Re-export legacy components for backward compatibility
// These should be gradually replaced with the new components

/**
 * @deprecated Use the new UI components instead
 */
export const LegacyComponents = {
  // Will be populated as we migrate legacy components
};

// =============================================================================
// COMPONENT GROUPS
// =============================================================================

/**
 * All button components
 */
export const Buttons = {
  Button,
  PrimaryButton,
  SecondaryButton,
  OutlineButton,
  GhostButton,
};

/**
 * All card components
 */
export const Cards = {
  Card,
  ElevatedCard,
  OutlinedCard,
  FilledCard,
  StatCard,
};

/**
 * All input components
 */
export const Inputs = {
  Input,
  OutlinedInput,
  FilledInput,
  Textarea,
};

/**
 * All UI components
 */
export const UI = {
  ...Buttons,
  ...Cards,
  ...Inputs,
};

export default UI;
