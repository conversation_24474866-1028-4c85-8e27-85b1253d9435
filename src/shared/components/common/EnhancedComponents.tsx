/**
 * Enhanced Shared UI Components
 * 
 * Comprehensive set of reusable UI components for the Everlasting Us app.
 * These components consolidate common patterns found across the codebase
 * and provide consistent, well-typed interfaces.
 * 
 * <AUTHOR> Us Team
 * @version 2.0.0
 */

import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  TextInput, 
  Modal, 
  ScrollView, 
  Dimensions, 
  StyleSheet,
  ActivityIndicator 
} from 'react-native';
import { colors } from '../../utils/colors';

const { width } = Dimensions.get('window');

// ============================================================================
// ENHANCED CARD COMPONENTS
// ============================================================================

/**
 * Props for the GradientCard component
 */
interface GradientCardProps {
  /** Card content */
  children: React.ReactNode;
  /** Background color */
  backgroundColor?: string;
  /** Optional custom styles */
  style?: any;
  /** Optional onPress handler for interactive cards */
  onPress?: () => void;
  /** Whether the card is pressable */
  pressable?: boolean;
  /** Card padding */
  padding?: number;
  /** Border radius */
  borderRadius?: number;
}

/**
 * Enhanced card component with consistent styling
 */
export const GradientCard: React.FC<GradientCardProps> = ({ 
  children, 
  backgroundColor = colors.backgroundSecondary,
  style, 
  onPress, 
  pressable = false,
  padding = 20,
  borderRadius = 16
}) => {
  const CardWrapper = pressable ? TouchableOpacity : View;
  
  return (
    <CardWrapper style={[styles.gradientCard, { borderRadius }, style]} onPress={onPress}>
      <View style={[styles.gradientCardContent, { padding, borderRadius, backgroundColor }]}>
        {children}
      </View>
    </CardWrapper>
  );
};

/**
 * Props for the StatCard component
 */
interface StatCardProps {
  /** Stat number or value */
  number: string | number;
  /** Stat label */
  label: string;
  /** Optional icon */
  icon?: React.ReactNode;
  /** Icon color */
  iconColor?: string;
  /** Background color for icon container */
  iconBackgroundColor?: string;
  /** Optional onPress handler */
  onPress?: () => void;
  /** Whether the card is pressable */
  pressable?: boolean;
}

/**
 * Enhanced stat card component for displaying metrics
 */
export const StatCard: React.FC<StatCardProps> = ({ 
  number, 
  label, 
  icon, 
  iconColor = colors.primary,
  iconBackgroundColor = colors.backgroundPrimary,
  onPress,
  pressable = false
}) => {
  const CardWrapper = pressable ? TouchableOpacity : View;
  
  return (
    <CardWrapper style={styles.statCard} onPress={onPress}>
      {icon && React.isValidElement(icon) && (
        <View style={[styles.statIconContainer, { backgroundColor: iconBackgroundColor }]}>
          {React.cloneElement(icon, { color: iconColor, size: 20 })}
        </View>
      )}
      <Text style={styles.statNumber}>{number}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </CardWrapper>
  );
};

// ============================================================================
// ENHANCED BUTTON COMPONENTS
// ============================================================================

/**
 * Props for the GradientButton component
 */
interface GradientButtonProps {
  /** Button title */
  title: string;
  /** Button press handler */
  onPress: () => void;
  /** Background color */
  backgroundColor?: string;
  /** Whether button is disabled */
  disabled?: boolean;
  /** Optional icon */
  icon?: React.ReactNode;
  /** Button size */
  size?: 'small' | 'medium' | 'large';
  /** Button variant */
  variant?: 'primary' | 'secondary' | 'outline';
  /** Custom styles */
  style?: any;
  /** Loading state */
  loading?: boolean;
}

/**
 * Enhanced gradient button with multiple variants and sizes
 */
export const GradientButton: React.FC<GradientButtonProps> = ({ 
  title, 
  onPress, 
  backgroundColor = colors.primary, 
  disabled = false, 
  icon, 
  size = 'medium',
  variant = 'primary',
  style,
  loading = false
}) => {
  const getButtonSize = () => {
    switch (size) {
      case 'small': return styles.buttonSmall;
      case 'large': return styles.buttonLarge;
      default: return styles.buttonMedium;
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'small': return styles.buttonTextSmall;
      case 'large': return styles.buttonTextLarge;
      default: return styles.buttonTextMedium;
    }
  };

  return (
    <TouchableOpacity 
      style={[
        getButtonSize(), 
        { backgroundColor: disabled ? colors.borderLight : backgroundColor },
        style
      ]} 
      onPress={onPress}
      disabled={disabled || loading}
    >
      <View style={styles.gradientButtonContent}>
        {loading ? (
          <ActivityIndicator size="small" color={colors.white} />
        ) : (
          <>
            {icon}
            <Text style={getTextSize()}>{title}</Text>
          </>
        )}
      </View>
    </TouchableOpacity>
  );
};

// ============================================================================
// ENHANCED PROGRESS COMPONENTS
// ============================================================================

/**
 * Props for the ProgressBar component
 */
interface ProgressBarProps {
  /** Current progress value */
  current: number;
  /** Total/maximum value */
  total: number;
  /** Background color */
  backgroundColor?: string;
  /** Fill color */
  fillColor?: string;
  /** Progress bar height */
  height?: number;
  /** Border radius */
  borderRadius?: number;
  /** Show percentage text */
  showPercentage?: boolean;
  /** Custom styles */
  style?: any;
}

/**
 * Enhanced progress bar component
 */
export const ProgressBar: React.FC<ProgressBarProps> = ({ 
  current, 
  total, 
  backgroundColor = colors.backgroundTertiary, 
  fillColor = colors.primary, 
  height = 8,
  borderRadius = 4,
  showPercentage = false,
  style 
}) => {
  const progress = total > 0 ? (current / total) * 100 : 0;
  
  return (
    <View style={[styles.progressBarContainer, style]}>
      <View style={[styles.progressBarBg, { backgroundColor, height, borderRadius }]}>
        <View 
          style={[
            styles.progressBarFill, 
            { 
              width: `${progress}%`,
              backgroundColor: fillColor,
              height,
              borderRadius
            }
          ]} 
        />
      </View>
      {showPercentage && (
        <Text style={styles.progressText}>{Math.round(progress)}%</Text>
      )}
    </View>
  );
};

/**
 * Props for the WeekSelector component
 */
interface WeekSelectorProps {
  /** Current selected week */
  currentWeek: number;
  /** Total number of weeks */
  totalWeeks: number;
  /** Week selection handler */
  onWeekSelect: (week: number) => void;
  /** Custom styles */
  style?: any;
}

/**
 * Enhanced week selector component
 */
export const WeekSelector: React.FC<WeekSelectorProps> = ({ 
  currentWeek, 
  totalWeeks, 
  onWeekSelect, 
  style 
}) => {
  return (
    <View style={[styles.weekSelector, style]}>
      {Array.from({ length: totalWeeks }, (_, i) => i + 1).map((week) => (
        <TouchableOpacity 
          key={week}
          style={[
            styles.weekButton, 
            currentWeek === week && styles.weekButtonActive
          ]}
          onPress={() => onWeekSelect(week)}
        >
          <Text style={[
            styles.weekButtonText, 
            currentWeek === week && styles.weekButtonTextActive
          ]}>
            {week}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

// ============================================================================
// ENHANCED MODAL COMPONENTS
// ============================================================================

/**
 * Props for the EnhancedModal component
 */
interface EnhancedModalProps {
  /** Modal visibility */
  visible: boolean;
  /** Close handler */
  onClose: () => void;
  /** Modal title */
  title: string;
  /** Modal content */
  children: React.ReactNode;
  /** Show close button */
  showCloseButton?: boolean;
  /** Modal size */
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
  /** Animation type */
  animationType?: 'slide' | 'fade' | 'none';
  /** Custom styles */
  style?: any;
}

/**
 * Enhanced modal component with multiple sizes and consistent styling
 */
export const EnhancedModal: React.FC<EnhancedModalProps> = ({ 
  visible, 
  onClose, 
  title, 
  children, 
  showCloseButton = true,
  size = 'medium',
  animationType = 'slide',
  style
}) => {
  const getModalSize = () => {
    switch (size) {
      case 'small': return { width: width * 0.8, maxHeight: '60%' };
      case 'large': return { width: width * 0.95, maxHeight: '90%' };
      case 'fullscreen': return { width: width, height: '100%' };
      default: return { width: width * 0.9, maxHeight: '80%' };
    }
  };

  return (
    <Modal
      visible={visible}
      animationType={animationType}
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, getModalSize(), style]}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{title}</Text>
            {showCloseButton && (
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            )}
          </View>
          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            {children}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

// ============================================================================
// ENHANCED FORM COMPONENTS
// ============================================================================

/**
 * Props for the EnhancedInput component
 */
interface EnhancedInputProps {
  /** Input value */
  value: string;
  /** Text change handler */
  onChangeText: (text: string) => void;
  /** Placeholder text */
  placeholder: string;
  /** Input label */
  label?: string;
  /** Multiline input */
  multiline?: boolean;
  /** Number of lines for multiline */
  numberOfLines?: number;
  /** Input type */
  type?: 'text' | 'email' | 'password';
  /** Error message */
  error?: string;
  /** Disabled state */
  disabled?: boolean;
  /** Custom styles */
  style?: any;
  /** Input container styles */
  containerStyle?: any;
}

/**
 * Enhanced input component with label, error handling, and consistent styling
 */
export const EnhancedInput: React.FC<EnhancedInputProps> = ({ 
  value, 
  onChangeText, 
  placeholder, 
  label,
  multiline = false, 
  numberOfLines = 1, 
  type = 'text',
  error,
  disabled = false,
  style,
  containerStyle
}) => {
  return (
    <View style={[styles.inputContainer, containerStyle]}>
      {label && <Text style={styles.inputLabel}>{label}</Text>}
      <TextInput
        style={[
          styles.input,
          multiline && styles.inputMultiline,
          error && styles.inputError,
          disabled && styles.inputDisabled,
          style
        ]}
        placeholder={placeholder}
        value={value}
        onChangeText={onChangeText}
        multiline={multiline}
        numberOfLines={numberOfLines}
        textAlignVertical={multiline ? "top" : "center"}
        secureTextEntry={type === 'password'}
        keyboardType={type === 'email' ? 'email-address' : 'default'}
        editable={!disabled}
        placeholderTextColor={colors.textTertiary}
      />
      {error && <Text style={styles.inputErrorText}>{error}</Text>}
    </View>
  );
};

// ============================================================================
// ENHANCED ACTIVITY/GOAL COMPONENTS
// ============================================================================

/**
 * Props for the ActivityCard component
 */
interface ActivityCardProps {
  /** Activity title */
  title: string;
  /** Activity description */
  description?: string;
  /** Activity category */
  category?: string;
  /** Completion status */
  completed?: boolean;
  /** Activity icon */
  icon?: React.ReactNode;
  /** Points earned */
  points?: number;
  /** Press handler */
  onPress?: () => void;
  /** Custom styles */
  style?: any;
}

/**
 * Enhanced activity card component for displaying activities and goals
 */
export const ActivityCard: React.FC<ActivityCardProps> = ({ 
  title, 
  description, 
  category, 
  completed = false,
  icon,
  points,
  onPress,
  style
}) => {
  return (
    <TouchableOpacity 
      style={[styles.activityCard, completed && styles.activityCardCompleted, style]} 
      onPress={onPress}
    >
      <View style={styles.activityCardContent}>
        <View style={styles.activityCardHeader}>
          <View style={styles.activityCardIconContainer}>
            {icon}
          </View>
          <View style={styles.activityCardTextContainer}>
            <Text style={[styles.activityCardTitle, completed && styles.activityCardTitleCompleted]}>
              {title}
            </Text>
            {category && (
              <Text style={styles.activityCardCategory}>{category}</Text>
            )}
            {description && (
              <Text style={styles.activityCardDescription}>{description}</Text>
            )}
          </View>
          <View style={styles.activityCardRight}>
            {completed && (
              <View style={styles.completedIndicator}>
                <Text style={styles.completedCheckmark}>✓</Text>
              </View>
            )}
            {points && (
              <Text style={styles.pointsText}>+{points}</Text>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

// ============================================================================
// ENHANCED NAVIGATION COMPONENTS
// ============================================================================

/**
 * Props for the StepNavigation component
 */
interface StepNavigationProps {
  /** Current step index */
  currentStep: number;
  /** Total number of steps */
  totalSteps: number;
  /** Step titles */
  stepTitles: string[];
  /** Step icons */
  stepIcons?: React.ReactNode[];
  /** Step selection handler */
  onStepSelect?: (step: number) => void;
  /** Custom styles */
  style?: any;
}

/**
 * Enhanced step navigation component
 */
export const StepNavigation: React.FC<StepNavigationProps> = ({ 
  currentStep, 
  totalSteps, 
  stepTitles, 
  stepIcons = [],
  onStepSelect,
  style
}) => {
  return (
    <View style={[styles.stepNavigation, style]}>
      {stepTitles.map((title, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.stepButton,
            currentStep === index && styles.stepButtonActive,
            index < currentStep && styles.stepButtonCompleted
          ]}
          onPress={() => onStepSelect?.(index)}
        >
          <View style={styles.stepButtonContent}>
            {stepIcons[index] && (
              <View style={styles.stepIconContainer}>
                {stepIcons[index]}
              </View>
            )}
            <Text style={[
              styles.stepButtonText,
              currentStep === index && styles.stepButtonTextActive,
              index < currentStep && styles.stepButtonTextCompleted
            ]}>
              {title}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

// ============================================================================
// STYLES
// ============================================================================

const styles = StyleSheet.create({
  // Gradient Card Styles
  gradientCard: {
    marginBottom: 16,
    borderRadius: 16,
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  gradientCardContent: {
    borderRadius: 16,
  },

  // Stat Card Styles (updated to new brand spec)
  statCard: {
    backgroundColor: '#f8f9fa',
    flex: 1,
    marginHorizontal: 4,
    padding: 15,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: colors.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  statIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.textPrimary,
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    fontWeight: '500',
  },

  // Button Styles
  buttonSmall: {
    borderRadius: 8,
    overflow: 'hidden',
  },
  buttonMedium: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonLarge: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  gradientButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    gap: 8,
  },
  buttonTextSmall: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  buttonTextMedium: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  buttonTextLarge: {
    color: colors.white,
    fontSize: 18,
    fontWeight: '600',
  },

  // Progress Bar Styles
  progressBarContainer: {
    marginBottom: 12,
  },
  progressBarBg: {
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBarFill: {
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },

  // Week Selector Styles
  weekSelector: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  weekButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
    borderWidth: 1,
    borderColor: colors.borderMedium,
  },
  weekButtonActive: {
    backgroundColor: colors.secondary,
    borderColor: colors.secondary,
  },
  weekButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textSecondary,
  },
  weekButtonTextActive: {
    color: colors.textInverse,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.borderLight,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.textPrimary,
    flex: 1,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.backgroundTertiary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    color: colors.textSecondary,
    fontWeight: '600',
  },
  modalBody: {
    padding: 20,
  },

  // Input Styles
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 8,
  },
  input: {
    borderWidth: 2,
    borderColor: colors.borderLight,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: colors.backgroundSecondary,
    color: colors.textPrimary,
  },
  inputMultiline: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: colors.error,
  },
  inputDisabled: {
    backgroundColor: colors.backgroundTertiary,
    color: colors.textTertiary,
  },
  inputErrorText: {
    fontSize: 12,
    color: colors.error,
    marginTop: 4,
  },

  // Activity Card Styles
  activityCard: {
    backgroundColor: colors.backgroundSecondary,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.borderLight,
  },
  activityCardCompleted: {
    backgroundColor: colors.backgroundTertiary,
    borderColor: colors.success,
  },
  activityCardContent: {
    padding: 16,
  },
  activityCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityCardIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  activityCardTextContainer: {
    flex: 1,
  },
  activityCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.textPrimary,
    marginBottom: 2,
  },
  activityCardTitleCompleted: {
    textDecorationLine: 'line-through',
    color: colors.textSecondary,
  },
  activityCardCategory: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  activityCardDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  activityCardRight: {
    alignItems: 'flex-end',
  },
  completedIndicator: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.success,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  completedCheckmark: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '700',
  },
  pointsText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
  },

  // Step Navigation Styles
  stepNavigation: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  stepButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 12,
    alignItems: 'center',
    backgroundColor: colors.backgroundTertiary,
    marginHorizontal: 2,
  },
  stepButtonActive: {
    backgroundColor: colors.primary,
  },
  stepButtonCompleted: {
    backgroundColor: colors.success,
  },
  stepButtonContent: {
    alignItems: 'center',
  },
  stepIconContainer: {
    marginBottom: 4,
  },
  stepButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.textSecondary,
    textAlign: 'center',
  },
  stepButtonTextActive: {
    color: colors.white,
  },
  stepButtonTextCompleted: {
    color: colors.white,
  },
});
