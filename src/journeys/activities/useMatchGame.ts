/**
 * Match Game Hook
 * Custom hook that integrates all match game services for easy app usage
 */

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../services/AuthContext';
import { useUserProfile } from './useUserProfile';
import { 
  MatchGameQuestion, 
  MatchGameGameState, 
  MatchGamePlayer,
  MatchGameSession,
  MatchGameResult,
  MatchGameStats,
  UseMatchGameQuestionsOptions,
  UseMatchGameSessionOptions,
  MatchGameError
} from '../types/matchGame.types';
import { matchGameQuestionsService } from '../services/matchGameQuestions.service';
import { matchGameAnswersService } from '../services/matchGameAnswers.service';
import { matchGameSessionsService } from '../services/matchGameSessions.service';
import { matchGameResultsService } from '../services/matchGameResults.service';
import { 
  getRandomQuestionsFromArray, 
  getBalancedQuestionsFromArray,
  calculateGameScore,
  getScoreDescription,
  isGameComplete,
  getNextQuestionIndex
} from '../utils/matchGameHelpers';
import { DEFAULT_QUESTION_COUNT } from '../constants/matchGameConstants';
import { logger } from '../../shared/utils/logger';

export const useMatchGame = () => {
  const { user } = useAuth();
  const { profile, getPartnerNames } = useUserProfile();
  
  // State management
  const [gameState, setGameState] = useState<MatchGameGameState>({
    session_id: null,
    current_question_index: 0,
    questions: [],
    player1: {
      user_id: user?.id || '',
      name: getPartnerNames()[0] || 'Player 1',
      score: 0,
      correct_answers: 0,
      total_answers: 0
    },
    player2: {
      user_id: '',
      name: getPartnerNames()[1] || 'Player 2',
      score: 0,
      correct_answers: 0,
      total_answers: 0
    },
    phase: 'setup',
    is_loading: false,
    error: null
  });

  const [userStats, setUserStats] = useState<MatchGameStats | null>(null);
  const [categories, setCategories] = useState<string[]>([]);

  // Initialize partner info when profile changes
  useEffect(() => {
    if (profile?.partner_id) {
      setGameState(prev => ({
        ...prev,
        player2: {
          ...prev.player2,
          user_id: profile.partner_id
        }
      }));
    }
  }, [profile?.partner_id]);

  /**
   * Load questions with filtering options
   */
  const loadQuestions = useCallback(async (options: UseMatchGameQuestionsOptions = {}) => {
    if (!user?.id) return;

    setGameState(prev => ({ ...prev, is_loading: true, error: null }));

    try {
      let questions: MatchGameQuestion[] = [];

      if (options.balanced) {
        questions = await matchGameQuestionsService.getBalancedQuestions({
          count: options.count || DEFAULT_QUESTION_COUNT,
          category: options.category,
          exclude_answered_by_user: user.id
        });
      } else {
        questions = await matchGameQuestionsService.getRandomQuestions({
          count: options.count || DEFAULT_QUESTION_COUNT,
          category: options.category,
          difficulty: options.difficulty,
          exclude_answered_by_user: user.id
        });
      }

      setGameState(prev => ({
        ...prev,
        questions,
        current_question_index: 0,
        phase: questions.length > 0 ? 'answering' : 'setup',
        is_loading: false
      }));

      return questions;
    } catch (error) {
      logger.error('Error loading questions:', error);
      setGameState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load questions',
        is_loading: false
      }));
      return [];
    }
  }, [user?.id]);

  /**
   * Create a new game session
   */
  const createSession = useCallback(async (options: UseMatchGameSessionOptions = {}) => {
    if (!user?.id || !profile?.couple_id) {
      throw new Error('User or couple information missing');
    }

    setGameState(prev => ({ ...prev, is_loading: true, error: null }));

    try {
      const session = await matchGameSessionsService.createSession({
        couple_id: profile.couple_id,
        session_type: options.session_type || 'weekly',
        question_count: options.session_id ? undefined : DEFAULT_QUESTION_COUNT
      });

      setGameState(prev => ({
        ...prev,
        session_id: session.id,
        is_loading: false
      }));

      return session;
    } catch (error) {
      logger.error('Error creating session:', error);
      setGameState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to create session',
        is_loading: false
      }));
      throw error;
    }
  }, [user?.id, profile?.couple_id]);

  /**
   * Save user answer
   */
  const saveAnswer = useCallback(async (questionId: string, answer: string) => {
    if (!user?.id) return;

    try {
      await matchGameAnswersService.saveUserAnswer({
        user_id: user.id,
        question_id: questionId,
        answer_text: answer
      });

      // Update local state
      setGameState(prev => ({
        ...prev,
        player1: {
          ...prev.player1,
          total_answers: prev.player1.total_answers + 1
        }
      }));
    } catch (error) {
      logger.error('Error saving answer:', error);
      throw error;
    }
  }, [user?.id]);

  /**
   * Save game result
   */
  const saveResult = useCallback(async (
    questionId: string,
    partner1Answer: string,
    partner2Answer: string,
    partner1Guess: string,
    partner2Guess: string
  ) => {
    if (!gameState.session_id || !user?.id || !profile?.partner_id) {
      throw new Error('Session or user information missing');
    }

    try {
      const result = await matchGameResultsService.saveResult({
        session_id: gameState.session_id,
        question_id: questionId,
        partner1_user_id: user.id,
        partner2_user_id: profile.partner_id,
        partner1_answer: partner1Answer,
        partner2_answer: partner2Answer,
        partner1_guess: partner1Guess,
        partner2_guess: partner2Guess
      });

      // Update local scores
      setGameState(prev => {
        const newPlayer1 = { ...prev.player1 };
        const newPlayer2 = { ...prev.player2 };

        if (result.partner1_correct) {
          newPlayer1.correct_answers += 1;
          newPlayer1.score += 10;
        }
        if (result.partner2_correct) {
          newPlayer2.correct_answers += 1;
          newPlayer2.score += 10;
        }

        return {
          ...prev,
          player1: newPlayer1,
          player2: newPlayer2
        };
      });

      return result;
    } catch (error) {
      logger.error('Error saving result:', error);
      throw error;
    }
  }, [gameState.session_id, user?.id, profile?.partner_id]);

  /**
   * Move to next question
   */
  const nextQuestion = useCallback(() => {
    setGameState(prev => {
      const nextIndex = getNextQuestionIndex(prev.current_question_index, prev.questions.length);
      
      if (nextIndex === null) {
        // Game completed
        return {
          ...prev,
          phase: 'completed'
        };
      }

      return {
        ...prev,
        current_question_index: nextIndex,
        phase: 'answering'
      };
    });
  }, []);

  /**
   * Complete the current game session
   */
  const completeSession = useCallback(async () => {
    if (!gameState.session_id) return;

    try {
      await matchGameSessionsService.completeSession(gameState.session_id);
      
      // Reload user stats
      if (user?.id) {
        const stats = await matchGameResultsService.getUserStats(user.id);
        setUserStats(stats);
      }
    } catch (error) {
      logger.error('Error completing session:', error);
      throw error;
    }
  }, [gameState.session_id, user?.id]);

  /**
   * Load user statistics
   */
  const loadUserStats = useCallback(async () => {
    if (!user?.id) return;

    try {
      const stats = await matchGameResultsService.getUserStats(user.id);
      setUserStats(stats);
    } catch (error) {
      logger.error('Error loading user stats:', error);
    }
  }, [user?.id]);

  /**
   * Load available categories
   */
  const loadCategories = useCallback(async () => {
    try {
      const categoryList = await matchGameQuestionsService.getAllCategories();
      setCategories(categoryList);
    } catch (error) {
      logger.error('Error loading categories:', error);
    }
  }, []);

  /**
   * Reset game state
   */
  const resetGame = useCallback(() => {
    setGameState({
      session_id: null,
      current_question_index: 0,
      questions: [],
      player1: {
        user_id: user?.id || '',
        name: getPartnerNames()[0] || 'Player 1',
        score: 0,
        correct_answers: 0,
        total_answers: 0
      },
      player2: {
        user_id: profile?.partner_id || '',
        name: getPartnerNames()[1] || 'Player 2',
        score: 0,
        correct_answers: 0,
        total_answers: 0
      },
      phase: 'setup',
      is_loading: false,
      error: null
    });
  }, [user?.id, profile?.partner_id, getPartnerNames]);

  /**
   * Get current question
   */
  const getCurrentQuestion = useCallback((): MatchGameQuestion | null => {
    return gameState.questions[gameState.current_question_index] || null;
  }, [gameState.questions, gameState.current_question_index]);

  /**
   * Check if game is completed
   */
  const isGameFinished = useCallback((): boolean => {
    return isGameComplete(gameState.current_question_index, gameState.questions.length);
  }, [gameState.current_question_index, gameState.questions.length]);

  /**
   * Get game score
   */
  const getGameScore = useCallback(() => {
    const totalQuestions = gameState.questions.length;
    const totalCorrect = gameState.player1.correct_answers + gameState.player2.correct_answers;
    return calculateGameScore(totalCorrect, totalQuestions * 2); // Each question has 2 answers
  }, [gameState.player1.correct_answers, gameState.player2.correct_answers, gameState.questions.length]);

  /**
   * Get score description
   */
  const getScoreDescriptionText = useCallback(() => {
    return getScoreDescription(getGameScore());
  }, [getGameScore]);

  // Load initial data
  useEffect(() => {
    if (user?.id) {
      loadUserStats();
      loadCategories();
    }
  }, [user?.id, loadUserStats, loadCategories]);

  return {
    // State
    gameState,
    userStats,
    categories,
    
    // Actions
    loadQuestions,
    createSession,
    saveAnswer,
    saveResult,
    nextQuestion,
    completeSession,
    resetGame,
    
    // Utilities
    getCurrentQuestion,
    isGameFinished,
    getGameScore,
    getScoreDescription: getScoreDescriptionText,
    
    // Computed values
    isLoading: gameState.is_loading,
    error: gameState.error,
    currentPhase: gameState.phase,
    progress: gameState.questions.length > 0 
      ? Math.round(((gameState.current_question_index + 1) / gameState.questions.length) * 100)
      : 0
  };
};

// Hook for managing match game questions specifically
export const useMatchGameQuestions = (options: UseMatchGameQuestionsOptions = {}) => {
  const [questions, setQuestions] = useState<MatchGameQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const loadQuestions = useCallback(async () => {
    if (!user?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      let loadedQuestions: MatchGameQuestion[] = [];

      if (options.balanced) {
        loadedQuestions = await matchGameQuestionsService.getBalancedQuestions({
          count: options.count || DEFAULT_QUESTION_COUNT,
          category: options.category,
          exclude_answered_by_user: user.id
        });
      } else {
        loadedQuestions = await matchGameQuestionsService.getRandomQuestions({
          count: options.count || DEFAULT_QUESTION_COUNT,
          category: options.category,
          difficulty: options.difficulty,
          exclude_answered_by_user: user.id
        });
      }

      setQuestions(loadedQuestions);
    } catch (err) {
      logger.error('Error loading questions:', err);
      setError(err instanceof Error ? err.message : 'Failed to load questions');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, options]);

  useEffect(() => {
    if (options.refetchOnMount !== false) {
      loadQuestions();
    }
  }, [loadQuestions, options.refetchOnMount]);

  return {
    questions,
    isLoading,
    error,
    loadQuestions,
    refetch: loadQuestions
  };
};

// Hook for managing match game sessions
export const useMatchGameSession = (options: UseMatchGameSessionOptions = {}) => {
  const [session, setSession] = useState<MatchGameSession | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { profile } = useUserProfile();

  const createSession = useCallback(async () => {
    if (!user?.id || !profile?.couple_id) {
      setError('User or couple information missing');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const newSession = await matchGameSessionsService.createSession({
        couple_id: profile.couple_id,
        session_type: options.session_type || 'weekly'
      });

      setSession(newSession);
    } catch (err) {
      logger.error('Error creating session:', err);
      setError(err instanceof Error ? err.message : 'Failed to create session');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, profile?.couple_id, options.session_type]);

  const loadSession = useCallback(async (sessionId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const sessionData = await matchGameSessionsService.getSession(sessionId);
      setSession(sessionData);
    } catch (err) {
      logger.error('Error loading session:', err);
      setError(err instanceof Error ? err.message : 'Failed to load session');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (options.session_id) {
      loadSession(options.session_id);
    } else if (options.auto_create) {
      createSession();
    }
  }, [options.session_id, options.auto_create, loadSession, createSession]);

  return {
    session,
    isLoading,
    error,
    createSession,
    loadSession,
    refetch: options.session_id ? () => loadSession(options.session_id!) : createSession
  };
};
