/**
 * Activities Journey - Index
 *
 * Centralized exports for all activities and games functionality:
 * - Date night ideas and planning
 * - Match games and couple activities
 * - Meal planning and recipes
 * - Favorites and content management
 *
 * <AUTHOR> Us Team
 */

// Hooks
export { useContentFavorites } from './useContentFavorites';
export { useDateNightFavorites } from './useDateNightFavorites';
export { useDateNightIdeasSupabase } from './useDateNightIdeasSupabase';
export { useDateNightPool } from './useDateNightPool';
export { useFavorites } from './useFavorites';
export { useMatchGame } from './useMatchGame';
export { useMealIdeasSupabase } from './useMealIdeasSupabase';
export { useWeeklyDateNightIntegration } from './useWeeklyDateNightIntegration';

// Services
export { default as dateNightIdeasService } from './dateNightIdeasService';
export { default as favoritesService } from './favoritesService';
export { mealIdeasService } from './mealIdeasService';

// Match game services
export * from './match-game/';

// Date night API and hooks (consolidated)
export * from './dateNightApi';
export * from './dateNightHooks';

// Components
export { default as Activities } from './Activities';
export { default as DateNight } from './DateNight';
export { default as Filters } from './Filters';
export { default as IdeaCard } from './IdeaCard';
export { default as Meals } from './Meals';

// Utils
export * from './matchGameHelpers';
export * from './surpriseHelpers';

// Types
export type {
    ActivityCategory, DateNightIdea, FavoriteItem,
    GameSession, MatchGameQuestion, MealIdea
} from './activities.types';
