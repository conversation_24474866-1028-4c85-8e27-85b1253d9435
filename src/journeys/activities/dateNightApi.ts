import { securityMiddleware } from '../../middleware/securityMiddleware';
import { supabase } from '../../shared/services/supabase/client';
import { logger } from '../../shared/utils/logger';
import {
    DateNightFilters,
    DateNightIdeaGlobal,
    DateNightIdeaUser,
    DateNightIdeaUserInsert,
    DateNightIdeaUserUpdate,
    IdeaSource,
    PaginatedResponse
} from '../../types/supabase.types';

/**
 * Date Night Ideas API
 * Handles all CRUD operations for both global and user date night ideas
 */
export class DateNightApi {
  /**
   * List global date night ideas with filters and pagination
   */
  async listGlobalIdeas(
    params: DateNightFilters & {
      page?: number;
      limit?: number;
      source?: IdeaSource
    } = {}
  ): Promise<PaginatedResponse<DateNightIdeaGlobal>> {
    try {
      const {
        category,
        cost,
        indoor_outdoor,
        difficulty,
        week_number,
        search,
        page = 1,
        limit = 20,
        source
      } = params;

      let query = supabase
        .from('date_night_ideas_global')
        .select('*', { count: 'exact' });

      // Apply filters
      if (category) {
        query = query.eq('category', category);
      }
      if (cost) {
        query = query.eq('cost', cost);
      }
      if (indoor_outdoor) {
        query = query.eq('indoor_outdoor', indoor_outdoor);
      }
      if (difficulty) {
        query = query.eq('difficulty', difficulty);
      }
      if (week_number) {
        query = query.eq('week_number', week_number);
      }
      if (source) {
        query = query.eq('source', source);
      }
      if (search) {
        // Use centralized security validation
        const context = securityMiddleware.createSecurityContext();
        const securityResult = securityMiddleware.validateQueryParams({ search }, context);

        if (securityResult.isValid && securityResult.sanitizedParams.search) {
          query = query.or(`title.ilike.%${securityResult.sanitizedParams.search}%,description.ilike.%${securityResult.sanitizedParams.search}%,category.ilike.%${securityResult.sanitizedParams.search}%`);
        } else {
          logger.warn('Invalid search parameter rejected:', securityResult.errors);
          // Continue without search filter rather than failing completely
        }
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to).order('title');

      const { data, error, count } = await query;

      if (error) {
        logger.error('Error fetching global date night ideas:', error);
        throw new Error(`Failed to fetch ideas: ${error.message}`);
      }

      return {
        data: data || [],
        count: count || 0,
        hasMore: (count || 0) > page * limit
      };
    } catch (error) {
      logger.error('Error in listGlobalIdeas:', error);
      throw error;
    }
  }

  /**
   * List user's date night ideas with filters and pagination
   */
  async listUserIdeas(
    userId: string,
    params: DateNightFilters & { page?: number; limit?: number } = {}
  ): Promise<PaginatedResponse<DateNightIdeaUser>> {
    try {
      const {
        category,
        cost,
        indoor_outdoor,
        difficulty,
        search,
        page = 1,
        limit = 20
      } = params;

      let query = supabase
        .from('date_night_ideas_user')
        .select('*', { count: 'exact' })
        .eq('user_id', userId);

      // Apply filters
      if (category) {
        query = query.eq('category', category);
      }
      if (cost) {
        query = query.eq('cost', cost);
      }
      if (indoor_outdoor) {
        query = query.eq('indoor_outdoor', indoor_outdoor);
      }
      if (difficulty) {
        query = query.eq('difficulty', difficulty);
      }
      if (search) {
        // Use centralized security validation
        const context = securityMiddleware.createSecurityContext();
        const securityResult = securityMiddleware.validateQueryParams({ search }, context);

        if (securityResult.isValid && securityResult.sanitizedParams.search) {
          query = query.or(`title.ilike.%${securityResult.sanitizedParams.search}%,description.ilike.%${securityResult.sanitizedParams.search}%,category.ilike.%${securityResult.sanitizedParams.search}%`);
        } else {
          logger.warn('Invalid search parameter rejected:', securityResult.errors);
          // Continue without search filter rather than failing completely
        }
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to).order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        logger.error('Error fetching user date night ideas:', error);
        throw new Error(`Failed to fetch user ideas: ${error.message}`);
      }

      return {
        data: data || [],
        count: count || 0,
        hasMore: (count || 0) > page * limit
      };
    } catch (error) {
      logger.error('Error in listUserIdeas:', error);
      throw error;
    }
  }

  /**
   * Get a single global date night idea by ID
   */
  async getGlobalIdea(id: string): Promise<DateNightIdeaGlobal | null> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        logger.error('Error fetching global date night idea:', error);
        throw new Error(`Failed to fetch idea: ${error.message}`);
      }

      return data;
    } catch (error) {
      logger.error('Error in getGlobalIdea:', error);
      throw error;
    }
  }

  /**
   * Get a single user date night idea by ID
   */
  async getUserIdea(id: string, userId: string): Promise<DateNightIdeaUser | null> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_user')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        logger.error('Error fetching user date night idea:', error);
        throw new Error(`Failed to fetch user idea: ${error.message}`);
      }

      return data;
    } catch (error) {
      logger.error('Error in getUserIdea:', error);
      throw error;
    }
  }

  /**
   * Create a new user date night idea
   */
  async createUserIdea(
    userId: string,
    payload: Omit<DateNightIdeaUserInsert, 'user_id'>
  ): Promise<DateNightIdeaUser> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_user')
        .insert({
          ...payload,
          user_id: userId
        })
        .select()
        .single();

      if (error) {
        logger.error('Error creating user date night idea:', error);
        throw new Error(`Failed to create idea: ${error.message}`);
      }

      logger.info('User date night idea created successfully');
      return data;
    } catch (error) {
      logger.error('Error in createUserIdea:', error);
      throw error;
    }
  }

  /**
   * Update a user date night idea
   */
  async updateUserIdea(
    id: string,
    userId: string,
    updates: DateNightIdeaUserUpdate
  ): Promise<DateNightIdeaUser> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_user')
        .update(updates)
        .eq('id', id)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        logger.error('Error updating user date night idea:', error);
        throw new Error(`Failed to update idea: ${error.message}`);
      }

      logger.info('User date night idea updated successfully');
      return data;
    } catch (error) {
      logger.error('Error in updateUserIdea:', error);
      throw error;
    }
  }

  /**
   * Delete a user date night idea
   */
  async deleteUserIdea(id: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('date_night_ideas_user')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

      if (error) {
        logger.error('Error deleting user date night idea:', error);
        throw new Error(`Failed to delete idea: ${error.message}`);
      }

      logger.info('User date night idea deleted successfully');
    } catch (error) {
      logger.error('Error in deleteUserIdea:', error);
      throw error;
    }
  }

  /**
   * Get random global date night ideas
   */
  async getRandomGlobalIdeas(count: number = 5): Promise<DateNightIdeaGlobal[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .limit(count);

      if (error) {
        logger.error('Error fetching random global ideas:', error);
        throw new Error(`Failed to fetch random ideas: ${error.message}`);
      }

      // Shuffle the results
      const shuffled = (data || []).sort(() => 0.5 - Math.random());
      return shuffled.slice(0, count);
    } catch (error) {
      logger.error('Error in getRandomGlobalIdeas:', error);
      throw error;
    }
  }

  /**
   * Get available categories from global ideas
   */
  async getCategories(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('category')
        .not('category', 'is', null);

      if (error) {
        logger.error('Error fetching categories:', error);
        throw new Error(`Failed to fetch categories: ${error.message}`);
      }

      const categories = Array.from(new Set(data?.map(item => item.category).filter(Boolean))) as string[];
      return categories.sort();
    } catch (error) {
      logger.error('Error in getCategories:', error);
      throw error;
    }
  }

  /**
   * Get weekly date night ideas
   */
  async getWeeklyIdeas(): Promise<DateNightIdeaGlobal[]> {
    try {
      const { data, error } = await supabase
        .from('date_night_ideas_global')
        .select('*')
        .eq('source', 'weekly')
        .order('week_number');

      if (error) {
        logger.error('Error fetching weekly ideas:', error);
        throw new Error(`Failed to fetch weekly ideas: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      logger.error('Error in getWeeklyIdeas:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dateNightApi = new DateNightApi();
export default dateNightApi;
