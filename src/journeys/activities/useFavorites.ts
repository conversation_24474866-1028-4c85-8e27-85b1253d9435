import { useState, useCallback, useRef, useEffect } from 'react';
import { Alert } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { favoritesService, FavoriteContentType, FavoriteItem } from '../favoritesService';
import { favoritesQueue } from '../favoritesQueue';
import { useAuth } from '../useAuth';
import { useFavoritesContext } from '../FavoritesContext';
import { logger } from '../../shared/utils/logger';

export interface UseFavoritesOptions {
  /** Content type for favorites */
  contentType: FavoriteContentType;
  /** Initial favorite item IDs */
  initialFavoriteIds?: string[];
  /** Debounce delay in milliseconds */
  debounceMs?: number;
  /** Enable automatic loading of user favorites */
  autoLoad?: boolean;
  /** Callback when favorite status changes */
  onFavoriteChange?: (itemId: string, isFavorited: boolean) => void;
  /** Callback when operation completes successfully */
  onSuccess?: (itemId: string, isFavorited: boolean) => void;
  /** Callback when operation fails */
  onError?: (itemId: string, error: Error) => void;
}

export interface UseFavoritesReturn {
  /** Map of item IDs to their favorite status */
  favorites: Record<string, boolean>;
  /** Loading states for individual items */
  loadingStates: Record<string, boolean>;
  /** Overall loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** All favorite items for this content type */
  favoriteItems: FavoriteItem[];
  /** Toggle favorite status for an item */
  toggleFavorite: (itemId: string, metadata?: Record<string, any>) => Promise<void>;
  /** Check if an item is favorited */
  isFavorited: (itemId: string) => boolean;
  /** Batch check favorite status for multiple items */
  checkMultipleFavorites: (itemIds: string[]) => Promise<void>;
  /** Refresh favorites from server */
  refresh: () => Promise<void>;
  /** Clear all local state */
  clear: () => void;
  /** Update metadata for a favorite */
  updateMetadata: (itemId: string, metadata: Record<string, any>) => Promise<void>;
}

/**
 * Core hook for managing favorites with optimistic updates, error handling, and state synchronization
 */
export const useFavorites = (options: UseFavoritesOptions): UseFavoritesReturn => {
  const {
    contentType,
    initialFavoriteIds = [],
    debounceMs = 300,
    autoLoad = true,
    onFavoriteChange,
    onSuccess,
    onError,
  } = options;

  const { user } = useAuth();
  const { notifyFavoriteChange, subscribeTo } = useFavoritesContext();

  // State management
  const [favorites, setFavorites] = useState<Record<string, boolean>>(() => {
    return initialFavoriteIds.reduce((acc: any, id: any) => {
      acc[id] = true;
      return acc;
    }, {} as Record<string, boolean>);
  });
  
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [favoriteItems, setFavoriteItems] = useState<FavoriteItem[]>([]);

  // Refs for managing debounced operations
  const debounceTimeouts = useRef<Record<string, NodeJS.Timeout>>({});
  const pendingOperations = useRef<Record<string, boolean>>({});

  // Load user favorites on mount
  useEffect(() => {
    if (autoLoad && user?.id) {
      loadUserFavorites();
    }
  }, [user?.id, contentType, autoLoad]);

  // Subscribe to favorite changes from other components
  useEffect(() => {
    const unsubscribe = subscribeTo(contentType, (itemId, isFavorited) => {
      updateFavoriteState(itemId, isFavorited);
    });
    return unsubscribe;
  }, [contentType, subscribeTo, updateFavoriteState]);

  const loadUserFavorites = useCallback(async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const userFavorites = await favoritesService.getUserFavorites(user.id, contentType);
      
      const favoritesMap = userFavorites.reduce((acc: any, item: any) => {
        acc[item.item_id] = true;
        return acc;
      }, {} as Record<string, boolean>);
      
      setFavorites(favoritesMap);
      setFavoriteItems(userFavorites);
      
      logger.info(`Loaded ${userFavorites.length} favorites for ${contentType}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load favorites';
      setError(errorMessage);
      logger.error('Error loading user favorites:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, contentType]);

  const setItemLoading = useCallback((itemId: string, loading: boolean) => {
    setLoadingStates(prev => ({
      ...prev,
      [itemId]: loading,
    }));
  }, []);

  const updateFavoriteState = useCallback((itemId: string, isFavorited: boolean) => {
    setFavorites(prev => ({
      ...prev,
      [itemId]: isFavorited,
    }));
    onFavoriteChange?.(itemId, isFavorited);
    // Notify other components of the change
    notifyFavoriteChange(contentType, itemId, isFavorited);
  }, [onFavoriteChange, notifyFavoriteChange, contentType]);

  const toggleFavorite = useCallback(async (itemId: string, metadata?: Record<string, any>) => {
    if (!user?.id) {
      const error = new Error('User not authenticated');
      onError?.(itemId, error);
      return;
    }

    // Clear any existing timeout for this item
    if (debounceTimeouts.current[itemId]) {
      clearTimeout(debounceTimeouts.current[itemId]);
    }

    const currentState = favorites[itemId] || false;
    const newState = !currentState;

    // Optimistic update
    updateFavoriteState(itemId, newState);
    setItemLoading(itemId, true);
    pendingOperations.current[itemId] = newState;

    // Debounced API call
    debounceTimeouts.current[itemId] = setTimeout(async () => {
      try {
        // Check network connectivity
        const netInfo = await NetInfo.fetch();

        if (netInfo.isConnected) {
          // Online: make direct API call
          await favoritesService.toggleFavorite(user.id, itemId, contentType, newState, metadata);
        } else {
          // Offline: queue the operation
          await favoritesQueue.enqueue({
            userId: user.id,
            itemId,
            contentType,
            operation: newState ? 'add' : 'remove',
            metadata,
          });
          logger.info(`Queued favorite operation for offline processing: ${itemId}`);
        }
        
        // Update favorite items list
        if (newState) {
          const newFavoriteItem: FavoriteItem = {
            id: `temp-${itemId}`, // This would be replaced by actual DB ID
            user_id: user.id,
            item_id: itemId,
            type: contentType,
            metadata,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          };
          setFavoriteItems(prev => [newFavoriteItem, ...prev]);
        } else {
          setFavoriteItems(prev => prev.filter(item => item.item_id !== itemId));
        }
        
        onSuccess?.(itemId, newState);
        logger.info(`Successfully ${newState ? 'favorited' : 'unfavorited'} ${contentType} ${itemId}`);
      } catch (err) {
        // Rollback optimistic update
        updateFavoriteState(itemId, currentState);
        
        const error = err instanceof Error ? err : new Error('Toggle failed');
        onError?.(itemId, error);
        logger.error(`Failed to toggle favorite for ${itemId}:`, error);
        
        Alert.alert(
          'Error',
          `Failed to ${newState ? 'add to' : 'remove from'} favorites. Please try again.`
        );
      } finally {
        setItemLoading(itemId, false);
        delete pendingOperations.current[itemId];
        delete debounceTimeouts.current[itemId];
      }
    }, debounceMs);
  }, [user?.id, favorites, contentType, debounceMs, updateFavoriteState, onSuccess, onError]);

  const isFavorited = useCallback((itemId: string): boolean => {
    return favorites[itemId] || false;
  }, [favorites]);

  const checkMultipleFavorites = useCallback(async (itemIds: string[]) => {
    if (!user?.id || itemIds.length === 0) return;

    try {
      const favoritesMap = await favoritesService.areFavorited(user.id, itemIds, contentType);
      setFavorites(prev => ({ ...prev, ...favoritesMap }));
    } catch (err) {
      logger.error('Error checking multiple favorites:', err);
    }
  }, [user?.id, contentType]);

  const refresh = useCallback(async () => {
    await loadUserFavorites();
  }, [loadUserFavorites]);

  const clear = useCallback(() => {
    setFavorites({});
    setLoadingStates({});
    setError(null);
    setFavoriteItems([]);
    
    // Clear any pending timeouts
    Object.values(debounceTimeouts.current).forEach(timeout => clearTimeout(timeout));
    debounceTimeouts.current = {};
    pendingOperations.current = {};
  }, []);

  const updateMetadata = useCallback(async (itemId: string, metadata: Record<string, any>) => {
    if (!user?.id) return;

    try {
      await favoritesService.updateFavoriteMetadata(user.id, itemId, contentType, metadata);
      
      // Update local state
      setFavoriteItems(prev => 
        prev.map(item => 
          item.item_id === itemId 
            ? { ...item, metadata: { ...item.metadata, ...metadata } }
            : item
        )
      );
    } catch (err) {
      logger.error('Error updating favorite metadata:', err);
      throw err;
    }
  }, [user?.id, contentType]);

  return {
    favorites,
    loadingStates,
    isLoading,
    error,
    favoriteItems,
    toggleFavorite,
    isFavorited,
    checkMultipleFavorites,
    refresh,
    clear,
    updateMetadata,
  };
};
