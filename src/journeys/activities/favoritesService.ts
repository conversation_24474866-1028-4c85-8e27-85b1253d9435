import { supabase } from '../../shared/services/supabase/client';
import { logger } from '../../shared/utils/logger';

// Supported content types for favorites
export type FavoriteContentType = 'date_night' | 'meal' | 'memory' | 'couple_profile' | 'playlist_song';

export interface FavoriteItem {
  id: string;
  type: FavoriteContentType;
  user_id: string;
  item_id: string;
  metadata?: Record<string, any>; // For storing additional context like tags, notes, etc.
  created_at: string;
  updated_at: string;
}

export interface CreateFavoriteData {
  type: FavoriteContentType;
  user_id: string;
  item_id: string;
  metadata?: Record<string, any>;
}

class FavoritesService {
  /**
   * Add an item to favorites
   */
  async addFavorite(data: CreateFavoriteData): Promise<FavoriteItem> {
    try {
      const { data: favorite, error } = await supabase
        .from('favorites')
        .insert([data])
        .select()
        .single();

      if (error) {
        logger.error('Error adding favorite:', error);
        throw new Error(`Failed to add favorite: ${error.message}`);
      }

      return favorite;
    } catch (error) {
      logger.error('Error in addFavorite:', error);
      throw error;
    }
  }

  /**
   * Remove an item from favorites
   */
  async removeFavorite(userId: string, itemId: string, type: FavoriteContentType): Promise<void> {
    try {
      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('user_id', userId)
        .eq('item_id', itemId)
        .eq('type', type);

      if (error) {
        logger.error('Error removing favorite:', error);
        throw new Error(`Failed to remove favorite: ${error.message}`);
      }
    } catch (error) {
      logger.error('Error in removeFavorite:', error);
      throw error;
    }
  }

  /**
   * Check if an item is favorited by a user
   */
  async isFavorited(userId: string, itemId: string, type: FavoriteContentType): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('favorites')
        .select('id')
        .eq('user_id', userId)
        .eq('item_id', itemId)
        .eq('type', type)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        logger.error('Error checking favorite status:', error);
        throw new Error(`Failed to check favorite status: ${error.message}`);
      }

      return !!data;
    } catch (error) {
      logger.error('Error in isFavorited:', error);
      throw error;
    }
  }

  /**
   * Get all favorites for a user by type
   */
  async getUserFavorites(userId: string, type: FavoriteContentType): Promise<FavoriteItem[]> {
    try {
      const { data, error } = await supabase
        .from('favorites')
        .select('*')
        .eq('user_id', userId)
        .eq('type', type)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error getting user favorites:', error);
        throw new Error(`Failed to get favorites: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      logger.error('Error in getUserFavorites:', error);
      throw error;
    }
  }

  /**
   * Toggle favorite status for an item
   */
  async toggleFavorite(
    userId: string,
    itemId: string,
    type: FavoriteContentType,
    isFavorited: boolean,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      if (!userId || !itemId || !type) {
        throw new Error('Missing required parameters for toggleFavorite');
      }

      if (isFavorited) {
        await this.addFavorite({ user_id: userId, item_id: itemId, type, metadata });
      } else {
        await this.removeFavorite(userId, itemId, type);
      }
    } catch (error) {
      logger.error('Error in toggleFavorite:', error);
      throw error;
    }
  }

  /**
   * Get favorite item IDs for a user by type
   */
  async getFavoriteItemIds(userId: string, type: FavoriteContentType): Promise<string[]> {
    try {
      const favorites = await this.getUserFavorites(userId, type);
      return favorites.map(fav => fav.item_id);
    } catch (error) {
      logger.error('Error in getFavoriteItemIds:', error);
      throw error;
    }
  }

  /**
   * Get all favorites for a user across all types
   */
  async getAllUserFavorites(userId: string): Promise<FavoriteItem[]> {
    try {
      const { data, error } = await supabase
        .from('favorites')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error getting all user favorites:', error);
        throw new Error(`Failed to get all favorites: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      logger.error('Error in getAllUserFavorites:', error);
      throw error;
    }
  }

  /**
   * Batch check if multiple items are favorited
   */
  async areFavorited(userId: string, itemIds: string[], type: FavoriteContentType): Promise<Record<string, boolean>> {
    try {
      const { data, error } = await supabase
        .from('favorites')
        .select('item_id')
        .eq('user_id', userId)
        .eq('type', type)
        .in('item_id', itemIds);

      if (error) {
        logger.error('Error checking multiple favorite status:', error);
        throw new Error(`Failed to check favorite status: ${error.message}`);
      }

      const favoritedIds = new Set(data?.map(item => item.item_id) || []);
      return itemIds.reduce((acc, itemId) => {
        acc[itemId] = favoritedIds.has(itemId);
        return acc;
      }, {} as Record<string, boolean>);
    } catch (error) {
      logger.error('Error in areFavorited:', error);
      throw error;
    }
  }

  /**
   * Update favorite metadata
   */
  async updateFavoriteMetadata(
    userId: string,
    itemId: string,
    type: FavoriteContentType,
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('favorites')
        .update({ metadata, updated_at: new Date().toISOString() })
        .eq('user_id', userId)
        .eq('item_id', itemId)
        .eq('type', type);

      if (error) {
        logger.error('Error updating favorite metadata:', error);
        throw new Error(`Failed to update favorite metadata: ${error.message}`);
      }
    } catch (error) {
      logger.error('Error in updateFavoriteMetadata:', error);
      throw error;
    }
  }
}

export const favoritesService = new FavoritesService();
export default favoritesService;
