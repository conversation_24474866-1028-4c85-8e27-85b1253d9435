/**
 * Match Game Answers Service
 * Handles all database operations related to user answers
 */

import { supabase } from '../../../shared/services/supabase/client';
import { logger } from '../../../shared/utils/logger';
import {
    MatchGameAnswersService,
    MatchGameError,
    MatchGameUserAnswer,
    SaveAnswerRequest
} from '../types/matchGame.types';

export class MatchGameAnswersServiceImpl implements MatchGameAnswersService {

  /**
   * Save a user's answer to a question
   */
  async saveUserAnswer(answer: SaveAnswerRequest): Promise<MatchGameUserAnswer> {
    try {
      const { data, error } = await supabase
        .from('match_game_user_answers')
        .insert({
          user_id: answer.user_id,
          question_id: answer.question_id,
          answer_text: answer.answer_text
        })
        .select()
        .single();

      if (error) {
        logger.error('Error saving user answer:', error);
        throw new MatchGameError({
          code: 'SAVE_ANSWER_ERROR',
          message: 'Failed to save user answer',
          details: error
        });
      }

      return data;
    } catch (error) {
      logger.error('Error in saveUserAnswer:', error);
      throw error;
    }
  }

  /**
   * Get a specific user's answer to a question
   */
  async getUserAnswer(user_id: string, question_id: string): Promise<MatchGameUserAnswer | null> {
    try {
      const { data, error } = await supabase
        .from('match_game_user_answers')
        .select('*')
        .eq('user_id', user_id)
        .eq('question_id', question_id)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No rows returned
          return null;
        }
        logger.error('Error fetching user answer:', error);
        throw new MatchGameError({
          code: 'FETCH_USER_ANSWER_ERROR',
          message: `Failed to fetch answer for user: ${user_id}, question: ${question_id}`,
          details: error
        });
      }

      return data;
    } catch (error) {
      logger.error('Error in getUserAnswer:', error);
      throw error;
    }
  }

  /**
   * Get all answers for a specific user
   */
  async getUserAnswers(user_id: string): Promise<MatchGameUserAnswer[]> {
    try {
      const { data, error } = await supabase
        .from('match_game_user_answers')
        .select('*')
        .eq('user_id', user_id)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error fetching user answers:', error);
        throw new MatchGameError({
          code: 'FETCH_USER_ANSWERS_ERROR',
          message: `Failed to fetch answers for user: ${user_id}`,
          details: error
        });
      }

      return data || [];
    } catch (error) {
      logger.error('Error in getUserAnswers:', error);
      throw error;
    }
  }

  /**
   * Update an existing user answer
   */
  async updateUserAnswer(answer_id: string, answer_text: string): Promise<MatchGameUserAnswer> {
    try {
      const { data, error } = await supabase
        .from('match_game_user_answers')
        .update({ answer_text })
        .eq('id', answer_id)
        .select()
        .single();

      if (error) {
        logger.error('Error updating user answer:', error);
        throw new MatchGameError({
          code: 'UPDATE_ANSWER_ERROR',
          message: `Failed to update answer: ${answer_id}`,
          details: error
        });
      }

      return data;
    } catch (error) {
      logger.error('Error in updateUserAnswer:', error);
      throw error;
    }
  }

  /**
   * Delete a user answer
   */
  async deleteUserAnswer(answer_id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('match_game_user_answers')
        .delete()
        .eq('id', answer_id);

      if (error) {
        logger.error('Error deleting user answer:', error);
        throw new MatchGameError({
          code: 'DELETE_ANSWER_ERROR',
          message: `Failed to delete answer: ${answer_id}`,
          details: error
        });
      }
    } catch (error) {
      logger.error('Error in deleteUserAnswer:', error);
      throw error;
    }
  }

  /**
   * Get list of question IDs that a user has answered
   */
  async getUserAnsweredQuestions(user_id: string): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('match_game_user_answers')
        .select('question_id')
        .eq('user_id', user_id);

      if (error) {
        logger.error('Error fetching user answered questions:', error);
        throw new MatchGameError({
          code: 'FETCH_ANSWERED_QUESTIONS_ERROR',
          message: `Failed to fetch answered questions for user: ${user_id}`,
          details: error
        });
      }

      return data?.map(answer => answer.question_id) || [];
    } catch (error) {
      logger.error('Error in getUserAnsweredQuestions:', error);
      throw error;
    }
  }

  /**
   * Get user's answer statistics
   */
  async getUserAnswerStats(user_id: string): Promise<{
    total_answers: number;
    answers_by_category: Record<string, number>;
    answers_by_difficulty: Record<string, number>;
    first_answer_date: string | null;
    last_answer_date: string | null;
  }> {
    try {
      // Get user answers with question details
      const { data, error } = await supabase
        .from('match_game_user_answers')
        .select(`
          created_at,
          match_game_questions!inner(
            category,
            difficulty
          )
        `)
        .eq('user_id', user_id)
        .order('created_at', { ascending: true });

      if (error) {
        logger.error('Error fetching user answer stats:', error);
        throw new MatchGameError({
          code: 'FETCH_USER_ANSWER_STATS_ERROR',
          message: `Failed to fetch answer stats for user: ${user_id}`,
          details: error
        });
      }

      const stats = {
        total_answers: data?.length || 0,
        answers_by_category: {} as Record<string, number>,
        answers_by_difficulty: {} as Record<string, number>,
        first_answer_date: data?.[0]?.created_at || null,
        last_answer_date: data?.[data.length - 1]?.created_at || null
      };

      // Count by category and difficulty
      data?.forEach(answer => {
        const question = answer.match_game_questions;
        if (question) {
          stats.answers_by_category[question.category] =
            (stats.answers_by_category[question.category] || 0) + 1;
          stats.answers_by_difficulty[question.difficulty] =
            (stats.answers_by_difficulty[question.difficulty] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      logger.error('Error in getUserAnswerStats:', error);
      throw error;
    }
  }

  /**
   * Get answers for multiple users (for couple matching)
   */
  async getUsersAnswersForQuestions(
    user_ids: string[],
    question_ids: string[]
  ): Promise<Record<string, Record<string, string>>> {
    try {
      const { data, error } = await supabase
        .from('match_game_user_answers')
        .select('user_id, question_id, answer_text')
        .in('user_id', user_ids)
        .in('question_id', question_ids);

      if (error) {
        logger.error('Error fetching users answers for questions:', error);
        throw new MatchGameError({
          code: 'FETCH_USERS_ANSWERS_ERROR',
          message: 'Failed to fetch answers for multiple users',
          details: error
        });
      }

      // Structure data as user_id -> question_id -> answer_text
      const result: Record<string, Record<string, string>> = {};

      data?.forEach(answer => {
        if (!result[answer.user_id]) {
          result[answer.user_id] = {};
        }
        result[answer.user_id][answer.question_id] = answer.answer_text;
      });

      return result;
    } catch (error) {
      logger.error('Error in getUsersAnswersForQuestions:', error);
      throw error;
    }
  }

  /**
   * Upsert user answer (insert or update if exists)
   */
  async upsertUserAnswer(answer: SaveAnswerRequest): Promise<MatchGameUserAnswer> {
    try {
      const { data, error } = await supabase
        .from('match_game_user_answers')
        .upsert({
          user_id: answer.user_id,
          question_id: answer.question_id,
          answer_text: answer.answer_text
        }, {
          onConflict: 'user_id,question_id'
        })
        .select()
        .single();

      if (error) {
        logger.error('Error upserting user answer:', error);
        throw new MatchGameError({
          code: 'UPSERT_ANSWER_ERROR',
          message: 'Failed to upsert user answer',
          details: error
        });
      }

      return data;
    } catch (error) {
      logger.error('Error in upsertUserAnswer:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const matchGameAnswersService = new MatchGameAnswersServiceImpl();
