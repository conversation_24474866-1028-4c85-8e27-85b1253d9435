import { useState, useEffect, useCallback } from 'react';
import { useDateNightFavorites as useBaseDateNightFavorites } from '../useContentFavorites';
import { dateNightIdeasService, DateNightIdea } from '../services/dateNightIdeasService';
import { useAuth } from '../hooks/useAuth';
import { logger } from '../../shared/utils/logger';

export interface UseDateNightFavoritesReturn {
  // Date night ideas with favorite status
  ideas: DateNightIdea[];
  isLoading: boolean;
  error: string | null;
  
  // Favorites functionality
  toggleFavorite: (compositeId: string, metadata?: Record<string, any>) => Promise<void>;
  isFavorited: (compositeId: string) => boolean;
  favoriteLoadingStates: Record<string, boolean>;
  
  // Data loading
  loadIdeas: () => Promise<void>;
  loadIdeasByCategory: (category: string) => Promise<void>;
  refresh: () => Promise<void>;
  
  // Statistics
  totalIdeas: number;
  favoriteIdeas: DateNightIdea[];
  favoriteCount: number;
}

/**
 * Enhanced hook for managing date night ideas with favorites support
 * Handles both global and user-generated ideas with composite ID system
 */
export const useDateNightFavorites = (): UseDateNightFavoritesReturn => {
  const { user } = useAuth();
  const baseFavorites = useBaseDateNightFavorites({
    autoLoad: true,
    onSuccess: (itemId, isFavorited) => {
      logger.info(`Date night idea ${itemId} ${isFavorited ? 'favorited' : 'unfavorited'}`);
    },
    onError: (itemId, error) => {
      logger.error(`Failed to toggle favorite for date night idea ${itemId}:`, error);
    },
  });

  // Local state for date night ideas
  const [ideas, setIdeas] = useState<DateNightIdea[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load ideas on mount
  useEffect(() => {
    loadIdeas();
  }, [user?.id]);

  // Update ideas with favorite status when favorites change
  useEffect(() => {
    updateIdeasWithFavoriteStatus();
  }, [ideas, baseFavorites.favorites]);

  const loadIdeas = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const allIdeas = await dateNightIdeasService.getAllIdeas(user?.id);
      setIdeas(allIdeas);
      
      // Check favorite status for all ideas
      const compositeIds = allIdeas.map(idea => idea.composite_id);
      if (compositeIds.length > 0) {
        await baseFavorites.checkMultipleFavorites(compositeIds);
      }
      
      logger.info(`Loaded ${allIdeas.length} date night ideas`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load date night ideas';
      setError(errorMessage);
      logger.error('Error loading date night ideas:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, baseFavorites.checkMultipleFavorites]);

  const loadIdeasByCategory = useCallback(async (category: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const allIdeas = await dateNightIdeasService.getAllIdeas(user?.id);
      const categoryIdeas = allIdeas.filter(idea => idea.category === category);
      setIdeas(categoryIdeas);
      
      // Check favorite status for category ideas
      const compositeIds = categoryIdeas.map(idea => idea.composite_id);
      if (compositeIds.length > 0) {
        await baseFavorites.checkMultipleFavorites(compositeIds);
      }
      
      logger.info(`Loaded ${categoryIdeas.length} date night ideas for category: ${category}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load date night ideas';
      setError(errorMessage);
      logger.error('Error loading date night ideas by category:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, baseFavorites.checkMultipleFavorites]);

  const updateIdeasWithFavoriteStatus = useCallback(() => {
    setIdeas(prevIdeas => 
      prevIdeas.map(idea => ({
        ...idea,
        is_favorited: baseFavorites.isFavorited(idea.composite_id),
      }))
    );
  }, [baseFavorites.isFavorited]);

  const toggleFavorite = useCallback(async (compositeId: string, metadata?: Record<string, any>) => {
    try {
      // Find the idea to get additional metadata
      const idea = ideas.find(i => i.composite_id === compositeId);
      
      const favoriteMetadata = {
        ...metadata,
        title: idea?.title,
        category: idea?.category,
        source: idea?.source,
        cost_level: idea?.costLevel,
        difficulty: idea?.difficulty,
        estimated_duration: idea?.estimatedDuration,
      };

      await baseFavorites.toggleFavorite(compositeId, favoriteMetadata);
      
      // Update local state immediately for better UX
      updateIdeasWithFavoriteStatus();
    } catch (error) {
      logger.error('Error toggling date night idea favorite:', error);
      throw error;
    }
  }, [ideas, baseFavorites.toggleFavorite, updateIdeasWithFavoriteStatus]);

  const isFavorited = useCallback((compositeId: string): boolean => {
    return baseFavorites.isFavorited(compositeId);
  }, [baseFavorites.isFavorited]);

  const refresh = useCallback(async () => {
    await Promise.all([
      loadIdeas(),
      baseFavorites.refresh(),
    ]);
  }, [loadIdeas, baseFavorites.refresh]);

  // Computed values
  const totalIdeas = ideas.length;
  const favoriteIdeas = ideas.filter(idea => idea.is_favorited);
  const favoriteCount = favoriteIdeas.length;

  return {
    // Date night ideas with favorite status
    ideas,
    isLoading: isLoading || baseFavorites.isLoading,
    error: error || baseFavorites.error,
    
    // Favorites functionality
    toggleFavorite,
    isFavorited,
    favoriteLoadingStates: baseFavorites.loadingStates,
    
    // Data loading
    loadIdeas,
    loadIdeasByCategory,
    refresh,
    
    // Statistics
    totalIdeas,
    favoriteIdeas,
    favoriteCount,
  };
};

/**
 * Hook specifically for getting favorited date night ideas
 */
export const useFavoriteDateNightIdeas = () => {
  const { favoriteIdeas, favoriteCount, isLoading, error, refresh } = useDateNightFavorites();
  
  return {
    favoriteIdeas,
    favoriteCount,
    isLoading,
    error,
    refresh,
  };
};

/**
 * Hook for managing date night ideas by category with favorites
 */
export const useDateNightIdeasByCategory = (category: string) => {
  const hook = useDateNightFavorites();
  
  useEffect(() => {
    if (category) {
      hook.loadIdeasByCategory(category);
    }
  }, [category]);
  
  return {
    ...hook,
    category,
  };
};
