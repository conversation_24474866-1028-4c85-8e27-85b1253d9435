import { dateNightIdeasService, DateNightIdea } from '../services/dateNightIdeasService';
import { mealIdeasService, MealIdea } from '../services/mealIdeasService';
import { SurpriseItem } from '../components/shared/SurpriseMe';
import { logger } from './logger';

/**
 * Convert DateNightIdea to SurpriseItem format
 */
export const convertDateNightToSurpriseItem = (idea: DateNightIdea): SurpriseItem => ({
  id: idea.id,
  title: idea.title,
  description: idea.description,
  emoji: idea.emoji,
  category: idea.category,
  difficulty: idea.difficulty,
  estimatedDuration: idea.estimatedDuration,
  costLevel: idea.costLevel,
  indoorOutdoor: idea.indoorOutdoor,
  source: idea.source,
  weekNumber: idea.weekNumber,
});

/**
 * Convert MealIdea to SurpriseItem format
 */
export const convertMealToSurpriseItem = (idea: MealIdea): SurpriseItem => ({
  id: idea.id,
  title: idea.title,
  description: idea.description,
  emoji: idea.emoji,
  category: idea.category,
  difficulty: idea.difficulty,
  prepTime: idea.prepTime,
  cookTime: idea.cookTime,
  servings: idea.servings,
  ingredients: idea.ingredients,
  instructions: idea.instructions,
  tags: idea.tags,
  source: idea.source,
  weekNumber: idea.weekNumber,
});

/**
 * Get random date night ideas for SurpriseMe component
 */
export const getRandomDateNightIdeas = async (count: number = 3): Promise<SurpriseItem[]> => {
  try {
    // Get all available date night ideas
    const allIdeas = await dateNightIdeasService.getAllIdeas();
    console.log(`Found ${allIdeas.length} total date night ideas in database`);
    
    // Shuffle the array and take the requested count
    const shuffled = allIdeas.sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, count);
    
    console.log(`Randomly selected ${selected.length} date night ideas:`, selected.map(i => i.title));
    
    return (selected || []).map(convertDateNightToSurpriseItem);
  } catch (error) {
    logger.error('Error getting random date night ideas:', error);
    return [];
  }
};

/**
 * Get random meal ideas for SurpriseMe component
 */
export const getRandomMealIdeas = async (userId: string, count: number = 3): Promise<SurpriseItem[]> => {
  try {
    // Get combined meal ideas (global + user's)
    const allMeals = await mealIdeasService.getCombinedMealIdeas(userId);
    
    // Shuffle and take random selection
    const shuffled = allMeals.sort(() => 0.5 - Math.random());
    const selected = shuffled.slice(0, count);
    
    return (selected || []).map(convertMealToSurpriseItem);
  } catch (error) {
    logger.error('Error getting random meal ideas:', error);
    return [];
  }
};

/**
 * Get combined random suggestions (both date night and meal ideas)
 */
export const getRandomCombinedIdeas = async (userId: string, count: number = 3): Promise<SurpriseItem[]> => {
  try {
    // Get all available ideas from both categories
    const [allDateNightIdeas, allMealIdeas] = await Promise.all([
      dateNightIdeasService.getAllIdeas(),
      mealIdeasService.getCombinedMealIdeas(userId)
    ]);
    
    // Convert to SurpriseItem format
    const dateNightItems = (allDateNightIdeas || []).map(convertDateNightToSurpriseItem);
    const mealItems = (allMealIdeas || []).map(convertMealToSurpriseItem);
    
    // Combine and shuffle
    const combined = [...dateNightItems, ...mealItems];
    const shuffled = combined.sort(() => 0.5 - Math.random());
    
    return shuffled.slice(0, count);
  } catch (error) {
    logger.error('Error getting random combined ideas:', error);
    return [];
  }
};

/**
 * Get random ideas by category
 */
export const getRandomIdeasByCategory = async (
  type: 'date-night' | 'meal',
  category: string,
  userId?: string,
  count: number = 3
): Promise<SurpriseItem[]> => {
  try {
    if (type === 'date-night') {
      const ideas = await dateNightIdeasService.getIdeasByCategory(category);
      const shuffled = ideas.sort(() => 0.5 - Math.random());
      return (shuffled || []).slice(0, count).map(convertDateNightToSurpriseItem);
    } else if (type === 'meal' && userId) {
      const ideas = await mealIdeasService.getCombinedMealIdeas(userId, category);
      const shuffled = ideas.sort(() => 0.5 - Math.random());
      return (shuffled || []).slice(0, count).map(convertMealToSurpriseItem);
    }
    
    return [];
  } catch (error) {
    logger.error('Error getting random ideas by category:', error);
    return [];
  }
};

/**
 * Get random ideas by difficulty
 */
export const getRandomIdeasByDifficulty = async (
  type: 'date-night' | 'meal',
  difficulty: 'easy' | 'medium' | 'hard',
  userId?: string,
  count: number = 3
): Promise<SurpriseItem[]> => {
  try {
    if (type === 'date-night') {
      const ideas = await dateNightIdeasService.getIdeasByDifficulty(difficulty);
      const shuffled = ideas.sort(() => 0.5 - Math.random());
      return (shuffled || []).slice(0, count).map(convertDateNightToSurpriseItem);
    } else if (type === 'meal' && userId) {
      const allMeals = await mealIdeasService.getCombinedMealIdeas(userId);
      const filtered = allMeals.filter(meal => meal.difficulty === difficulty);
      const shuffled = filtered.sort(() => 0.5 - Math.random());
      return (shuffled || []).slice(0, count).map(convertMealToSurpriseItem);
    }
    
    return [];
  } catch (error) {
    logger.error('Error getting random ideas by difficulty:', error);
    return [];
  }
};

/**
 * Smart random selection that avoids recently shown items
 */
export const getSmartRandomIdeas = async (
  type: 'date-night' | 'meal' | 'combined',
  userId?: string,
  count: number = 3,
  excludeIds: string[] = []
): Promise<SurpriseItem[]> => {
  try {
    let allIdeas: SurpriseItem[] = [];
    
    if (type === 'date-night') {
      // Get ALL date night ideas from the database
      const ideas = await dateNightIdeasService.getAllIdeas();
      allIdeas = (ideas || []).map(convertDateNightToSurpriseItem);
    } else if (type === 'meal' && userId) {
      // Get ALL meal ideas (global + user's)
      const ideas = await mealIdeasService.getCombinedMealIdeas(userId);
      allIdeas = (ideas || []).map(convertMealToSurpriseItem);
    } else if (type === 'combined' && userId) {
      // Get ALL ideas from both categories
      const [dateNightIdeas, mealIdeas] = await Promise.all([
        dateNightIdeasService.getAllIdeas(),
        mealIdeasService.getCombinedMealIdeas(userId)
      ]);
      allIdeas = [
        ...(dateNightIdeas || []).map(convertDateNightToSurpriseItem),
        ...(mealIdeas || []).map(convertMealToSurpriseItem)
      ];
    }
    
    // Filter out excluded items (recently shown)
    const availableIdeas = allIdeas.filter(idea => !excludeIds.includes(idea.id));
    
    // If we don't have enough available ideas, use all ideas
    const ideasToUse = availableIdeas.length >= count ? availableIdeas : allIdeas;
    
    // Shuffle and select
    const shuffled = ideasToUse.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  } catch (error) {
    logger.error('Error getting smart random ideas:', error);
    return [];
  }
};
