import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { userPreferencesService, UserPreferences } from '../services/userPreferencesService';
import { logger } from '../../shared/utils/logger';

export interface UseUserPreferencesReturn {
  preferences: UserPreferences | null;
  isLoading: boolean;
  error: string | null;
  
  // Hidden ideas functionality
  hiddenIdeas: string[];
  hideIdea: (compositeId: string) => Promise<void>;
  unhideIdea: (compositeId: string) => Promise<void>;
  isIdeaHidden: (compositeId: string) => boolean;
  
  // Preferences management
  updatePreferences: (updates: Partial<UserPreferences>) => Promise<void>;
  setFavoriteCategories: (categories: string[]) => Promise<void>;
  setPreferredDifficulty: (difficulty: 'easy' | 'medium' | 'hard') => Promise<void>;
  setPreferredCost: (cost: 'free' | 'low' | 'medium' | 'high') => Promise<void>;
  setPreferredDuration: (minMinutes: number, maxMinutes: number) => Promise<void>;
  
  // Utility functions
  filterHiddenIdeas: <T extends { composite_id: string }>(ideas: T[]) => T[];
  refresh: () => Promise<void>;
}

/**
 * Hook for managing user preferences including hidden date night ideas
 */
export const useUserPreferences = (): UseUserPreferencesReturn => {
  const { user } = useAuth();
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user preferences
  const loadPreferences = useCallback(async () => {
    if (!user?.id) {
      setPreferences(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const prefs = await userPreferencesService.getUserPreferences(user.id);
      setPreferences(prefs);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load preferences';
      setError(errorMessage);
      logger.error('Error loading user preferences:', err);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // Load preferences on mount and when user changes
  useEffect(() => {
    loadPreferences();
  }, [loadPreferences]);

  // Hide an idea
  const hideIdea = useCallback(async (compositeId: string) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      await userPreferencesService.hideDateNightIdea(user.id, compositeId);
      
      // Update local state
      setPreferences(prev => {
        if (!prev) return prev;
        
        const updatedHiddenIdeas = [...prev.hidden_date_night_ideas];
        if (!updatedHiddenIdeas.includes(compositeId)) {
          updatedHiddenIdeas.push(compositeId);
        }
        
        return {
          ...prev,
          hidden_date_night_ideas: updatedHiddenIdeas
        };
      });
    } catch (err) {
      logger.error('Error hiding idea:', err);
      throw err;
    }
  }, [user?.id]);

  // Unhide an idea
  const unhideIdea = useCallback(async (compositeId: string) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      await userPreferencesService.unhideDateNightIdea(user.id, compositeId);
      
      // Update local state
      setPreferences(prev => {
        if (!prev) return prev;
        
        return {
          ...prev,
          hidden_date_night_ideas: prev.hidden_date_night_ideas.filter(id => id !== compositeId)
        };
      });
    } catch (err) {
      logger.error('Error unhiding idea:', err);
      throw err;
    }
  }, [user?.id]);

  // Check if an idea is hidden
  const isIdeaHidden = useCallback((compositeId: string): boolean => {
    return preferences?.hidden_date_night_ideas.includes(compositeId) || false;
  }, [preferences?.hidden_date_night_ideas]);

  // Update preferences
  const updatePreferences = useCallback(async (updates: Partial<UserPreferences>) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      await userPreferencesService.updatePreferences(user.id, updates);
      await loadPreferences(); // Reload to get updated data
    } catch (err) {
      logger.error('Error updating preferences:', err);
      throw err;
    }
  }, [user?.id, loadPreferences]);

  // Set favorite categories
  const setFavoriteCategories = useCallback(async (categories: string[]) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      await userPreferencesService.setFavoriteCategories(user.id, categories);
      setPreferences(prev => prev ? { ...prev, favorite_categories: categories } : prev);
    } catch (err) {
      logger.error('Error setting favorite categories:', err);
      throw err;
    }
  }, [user?.id]);

  // Set preferred difficulty
  const setPreferredDifficulty = useCallback(async (difficulty: 'easy' | 'medium' | 'hard') => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      await userPreferencesService.setPreferredDifficulty(user.id, difficulty);
      setPreferences(prev => prev ? { ...prev, preferred_difficulty: difficulty } : prev);
    } catch (err) {
      logger.error('Error setting preferred difficulty:', err);
      throw err;
    }
  }, [user?.id]);

  // Set preferred cost
  const setPreferredCost = useCallback(async (cost: 'free' | 'low' | 'medium' | 'high') => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      await userPreferencesService.setPreferredCost(user.id, cost);
      setPreferences(prev => prev ? { ...prev, preferred_cost: cost } : prev);
    } catch (err) {
      logger.error('Error setting preferred cost:', err);
      throw err;
    }
  }, [user?.id]);

  // Set preferred duration
  const setPreferredDuration = useCallback(async (minMinutes: number, maxMinutes: number) => {
    if (!user?.id) {
      throw new Error('User not authenticated');
    }

    try {
      await userPreferencesService.setPreferredDuration(user.id, minMinutes, maxMinutes);
      setPreferences(prev => prev ? { 
        ...prev, 
        preferred_duration_min: minMinutes,
        preferred_duration_max: maxMinutes 
      } : prev);
    } catch (err) {
      logger.error('Error setting preferred duration:', err);
      throw err;
    }
  }, [user?.id]);

  // Filter hidden ideas from a list
  const filterHiddenIdeas = useCallback(<T extends { composite_id: string }>(ideas: T[]): T[] => {
    if (!preferences?.hidden_date_night_ideas.length) {
      return ideas;
    }
    
    return ideas.filter(idea => !preferences.hidden_date_night_ideas.includes(idea.composite_id));
  }, [preferences?.hidden_date_night_ideas]);

  // Refresh preferences
  const refresh = useCallback(async () => {
    await loadPreferences();
  }, [loadPreferences]);

  return {
    preferences,
    isLoading,
    error,
    
    // Hidden ideas functionality
    hiddenIdeas: preferences?.hidden_date_night_ideas || [],
    hideIdea,
    unhideIdea,
    isIdeaHidden,
    
    // Preferences management
    updatePreferences,
    setFavoriteCategories,
    setPreferredDifficulty,
    setPreferredCost,
    setPreferredDuration,
    
    // Utility functions
    filterHiddenIdeas,
    refresh,
  };
};
