/**
 * Planning Journey - Index
 * 
 * Centralized exports for all planning and goal-setting functionality:
 * - User preferences and settings
 * - Goal setting and tracking
 * - Activity scheduling and planning
 * - Configuration management
 * 
 * <AUTHOR> Us Team
 */

// Hooks
export { useUserPreferences } from './useUserPreferences';
export { useAppSettings } from './useAppSettings';

// Services
export { default as userPreferencesService } from './userPreferencesService';

// Utils
export * from './config';

// Types
export type {
  UserPreferences,
  AppSettings,
  PlanningGoal,
  ScheduleItem,
  ConfigOption
} from './types';
