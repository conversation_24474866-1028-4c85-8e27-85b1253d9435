import { APP_CONSTANTS } from './constants';

/**
 * Secure configuration management
 */

export interface AppConfig {
  isProduction: boolean;
  isDevelopment: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error' | 'none';
  maxInputLength: number;
  maxImageSize: number;
  allowedImageTypes: string[];
  rateLimitAttempts: number;
  rateLimitWindowMs: number;
  secureStorageEnabled: boolean;
}

class ConfigManager {
  private config: AppConfig;

  constructor() {
    this.config = {
      isProduction: process.env.NODE_ENV === 'production',
      isDevelopment: process.env.NODE_ENV === 'development',
      logLevel: this.getLogLevel(),
      maxInputLength: APP_CONSTANTS.MAX_INPUT_LENGTH,
      maxImageSize: APP_CONSTANTS.MAX_IMAGE_SIZE,
      allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      rateLimitAttempts: APP_CONSTANTS.RATE_LIMIT_ATTEMPTS,
      rateLimitWindowMs: APP_CONSTANTS.RATE_LIMIT_WINDOW_MS,
      secureStorageEnabled: true,
    };
  }

  private getLogLevel(): 'debug' | 'info' | 'warn' | 'error' | 'none' {
    const envLevel = process.env.LOG_LEVEL;
    if (envLevel && ['debug', 'info', 'warn', 'error', 'none'].includes(envLevel)) {
      return envLevel as any;
    }
    return this.config.isProduction ? 'error' : 'debug';
  }

  get<K extends keyof AppConfig>(key: K): AppConfig[K] {
    return this.config[key];
  }

  getAll(): AppConfig {
    return { ...this.config };
  }

  update<K extends keyof AppConfig>(key: K, value: AppConfig[K]): void {
    this.config[key] = value;
  }

  isFeatureEnabled(feature: string): boolean {
    // Feature flags for security controls
    const featureFlags: Record<string, boolean> = {
      inputValidation: true,
      rateLimiting: true,
      secureStorage: this.config.secureStorageEnabled,
      externalImageLoading: false, // Disabled for security
      consoleLogging: !this.config.isProduction,
    };

    return featureFlags[feature] ?? false;
  }

  getSecurityHeaders(): Record<string, string> {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': this.getCSP(),
    };
  }

  private getCSP(): string {
    const baseCSP = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: blob:",
      "font-src 'self'",
      "connect-src 'self'",
      "media-src 'self'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
    ];

    return baseCSP.join('; ');
  }

  validateConfig(): boolean {
    try {
      // Validate critical security settings
      if (this.config.maxInputLength > 10000) {
        console.error('Config validation failed: maxInputLength too high');
        return false;
      }

      if (this.config.rateLimitAttempts > 100) {
        console.error('Config validation failed: rateLimitAttempts too high');
        return false;
      }

      if (this.config.maxImageSize > 10 * 1024 * 1024) { // 10MB
        console.error('Config validation failed: maxImageSize too high');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Config validation error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const config = new ConfigManager();

// Validate configuration on load
if (!config.validateConfig()) {
  console.error('Invalid configuration detected. Using fallback settings.');
}
