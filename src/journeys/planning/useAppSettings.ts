import { useSettings } from '../services/SettingsContext';
import { StyleSheet } from 'react-native';
import { lightTheme, darkTheme } from '../../utils/colors';

/**
 * Hook to provide app settings and derived styles/configurations
 * This demonstrates how settings can be applied throughout the app
 */
export const useAppSettings = () => {
  const { settings, updateSetting, resetSettings, isLoading, currentTheme, isDarkMode, systemColorScheme } = useSettings();

  // Derive font sizes based on settings
  const getFontSizes = () => {
    const baseSize = {
      small: { title: 18, body: 14, caption: 12 },
      medium: { title: 20, body: 16, caption: 14 },
      large: { title: 24, body: 18, caption: 16 },
    };
    return baseSize[settings.fontSize];
  };

  // Get theme colors from the current theme
  const getThemeColors = () => {
    return {
      background: currentTheme.background,
      surface: currentTheme.backgroundSecondary,
      text: currentTheme.textPrimary,
      textSecondary: currentTheme.textSecondary,
      border: currentTheme.borderLight,
      primary: currentTheme.primary,
      secondary: currentTheme.secondary,
      accent: currentTheme.accent1,
      success: currentTheme.success,
      warning: currentTheme.warning,
      error: currentTheme.error,
      info: currentTheme.info,
    };
  };

  // Create dynamic styles based on settings
  const createDynamicStyles = () => {
    const fontSizes = getFontSizes();
    const themeColors = getThemeColors();

    return StyleSheet.create({
      container: {
        backgroundColor: themeColors.background,
        flex: 1,
      },
      surface: {
        backgroundColor: themeColors.surface,
        borderColor: themeColors.border,
      },
      title: {
        fontSize: fontSizes.title,
        color: themeColors.text,
        fontWeight: '600',
      },
      body: {
        fontSize: fontSizes.body,
        color: themeColors.text,
        lineHeight: fontSizes.body * 1.4,
      },
      caption: {
        fontSize: fontSizes.caption,
        color: themeColors.textSecondary,
      },
      card: {
        backgroundColor: themeColors.surface,
        borderRadius: 12,
        padding: 16,
        marginVertical: 8,
        borderWidth: 1,
        borderColor: themeColors.border,
      },
      // Button styles
      button: {
        backgroundColor: themeColors.primary,
        borderRadius: 8,
        padding: 12,
      },
      buttonSecondary: {
        backgroundColor: themeColors.secondary,
        borderRadius: 8,
        padding: 12,
      },
      buttonText: {
        color: themeColors.background,
        fontSize: fontSizes.body,
        fontWeight: '600',
        textAlign: 'center',
      },
      // Input styles
      input: {
        backgroundColor: themeColors.surface,
        borderColor: themeColors.border,
        borderWidth: 1,
        borderRadius: 8,
        padding: 12,
        color: themeColors.text,
        fontSize: fontSizes.body,
      },
    });
  };

  // Check if a feature is enabled
  const isFeatureEnabled = (feature: keyof typeof settings): boolean => {
    return Boolean(settings[feature]);
  };

  // Get accessibility props based on settings
  const getAccessibilityProps = (label: string, hint?: string) => {
    if (!settings.accessibility) {
      return {};
    }
    
    return {
      accessible: true,
      accessibilityLabel: label,
      accessibilityHint: hint,
      accessibilityRole: 'button' as const,
    };
  };

  // Get notification preferences
  const getNotificationConfig = () => {
    return {
      enabled: settings.notifications,
      sound: settings.soundEnabled,
      vibration: settings.vibrationEnabled,
    };
  };

  // Apply settings to component props
  const applySettingsToProps = (baseProps: any) => {
    const dynamicStyles = createDynamicStyles();
    
    return {
      ...baseProps,
      style: [dynamicStyles.container, baseProps.style],
      // Add other setting-based modifications here
    };
  };

  return {
    // Settings state
    settings,
    isLoading,
    
    // Settings actions
    updateSetting,
    resetSettings,
    
    // Derived values
    fontSizes: getFontSizes(),
    themeColors: getThemeColors(),
    dynamicStyles: createDynamicStyles(),
    
    // Utility functions
    isFeatureEnabled,
    getAccessibilityProps,
    getNotificationConfig,
    applySettingsToProps,
    
    // Convenience getters
    isDarkMode,
    isAccessibilityEnabled: settings.accessibility,
    currentLanguage: settings.language,
    currentFontSize: settings.fontSize,
    currentTheme,
    systemColorScheme,
  };
};

export default useAppSettings;
