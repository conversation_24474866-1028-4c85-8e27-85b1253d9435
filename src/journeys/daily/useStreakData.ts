/**
 * useStreakData Hook
 *
 * Custom hook for managing streak data with proper error handling and caching.
 * Follows established patterns in the codebase for data management.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { streakEventService, StreakCalculationResult, UserActivity } from '../services/streakEventService';
import { useAuth } from './useAuth';
import { logger } from '../../shared/utils/logger';

export interface UseStreakDataOptions {
  category?: string;
  refreshInterval?: number; // in milliseconds
  autoRefresh?: boolean;
}

export interface UseStreakDataReturn {
  streak: number;
  lastActivityDate?: string;
  totalEvents: number;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  activity: UserActivity[];
  loadingActivity: boolean;
  activityError: string | null;
  refreshActivity: () => Promise<void>;
}

export const useStreakData = (options: UseStreakDataOptions = {}): UseStreakDataReturn => {
  const { category, refreshInterval = 30000, autoRefresh = true } = options;
  const { user } = useAuth();
  
  // Streak data state
  const [streakData, setStreakData] = useState<StreakCalculationResult>({
    streak: 0,
    totalEvents: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Activity data state
  const [activity, setActivity] = useState<UserActivity[]>([]);
  const [loadingActivity, setLoadingActivity] = useState(false);
  const [activityError, setActivityError] = useState<string | null>(null);

  // Memoized cache key for dependency tracking
  const cacheKey = useMemo(() => {
    return `${user?.id || 'anonymous'}-${category || 'overall'}`;
  }, [user?.id, category]);

  /**
   * Load streak data
   */
  const loadStreakData = useCallback(async (): Promise<void> => {
    if (!user?.id) {
      setError('User not authenticated');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const result = await streakEventService.getUserStreak(user.id, category);
      setStreakData(result);
      
      logger.debug('Streak data loaded:', { userId: user.id, category, result });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load streak data';
      setError(errorMessage);
      logger.error('Error loading streak data:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.id, category]);

  /**
   * Load activity data
   */
  const loadActivity = useCallback(async (): Promise<void> => {
    if (!user?.id) {
      setActivityError('User not authenticated');
      return;
    }

    try {
      setLoadingActivity(true);
      setActivityError(null);
      
      const result = await streakEventService.getUserActivity(user.id, 30);
      setActivity(result);
      
      logger.debug('Activity data loaded:', { userId: user.id, count: result.length });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load activity data';
      setActivityError(errorMessage);
      logger.error('Error loading activity data:', err);
    } finally {
      setLoadingActivity(false);
    }
  }, [user?.id]);

  /**
   * Refresh streak data
   */
  const refresh = useCallback(async (): Promise<void> => {
    await loadStreakData();
  }, [loadStreakData]);

  /**
   * Refresh activity data
   */
  const refreshActivity = useCallback(async (): Promise<void> => {
    await loadActivity();
  }, [loadActivity]);

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadStreakData();
  }, [loadStreakData]);

  // Load activity data on mount
  useEffect(() => {
    loadActivity();
  }, [loadActivity]);

  // Auto-refresh setup
  useEffect(() => {
    if (!autoRefresh || !user?.id) return;

    const interval = setInterval(() => {
      loadStreakData();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, loadStreakData, user?.id]);

  // Reset state when user changes
  useEffect(() => {
    if (!user?.id) {
      setStreakData({ streak: 0, totalEvents: 0 });
      setActivity([]);
      setError(null);
      setActivityError(null);
      setLoading(false);
      setLoadingActivity(false);
    }
  }, [user?.id]);

  return {
    streak: streakData.streak,
    lastActivityDate: streakData.lastActivityDate,
    totalEvents: streakData.totalEvents,
    loading,
    error,
    refresh,
    activity,
    loadingActivity,
    activityError,
    refreshActivity,
  };
};

export default useStreakData;
