import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../../shared/utils/logger';

export interface CustomCrest {
  icons: string[];
  colors: string[];
  symbols: string[];
  designNotes: string;
  timestamp: number;
}

export interface LiveShowDate {
  when: string;
  where: string;
  typeOfShow: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface SharedValues {
  playerOneValues: string[];
  playerTwoValues: string[];
  sharedValues: string[];
  valuesStatement: string;
  timestamp: number;
}

export interface WeekNineData {
  customCrest: CustomCrest;
  liveShowDate: LiveShowDate;
  chatPrompts: ChatPrompt[];
  sharedValues: SharedValues;
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_NINE_STORAGE_KEY = 'week_nine_data';

export const useWeekNineData = () => {
  const [data, setData] = useState<WeekNineData>({
    customCrest: {
      icons: ['', '', ''],
      colors: ['', '', ''],
      symbols: ['', '', ''],
      designNotes: '',
      timestamp: Date.now(),
    },
    liveShowDate: {
      when: '',
      where: '',
      typeOfShow: '',
      completed: false,
      timestamp: Date.now(),
    },
    chatPrompts: [
      { prompt: 'If we could create a new tradition just for us, what would it be?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'What\'s something you wish we did more often as a couple?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    sharedValues: {
      playerOneValues: ['', '', '', '', ''],
      playerTwoValues: ['', '', '', '', ''],
      sharedValues: ['', '', '', '', ''],
      valuesStatement: '',
      timestamp: Date.now(),
    },
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekNineData>(WEEK_NINE_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Nine data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekNineData) => {
    try {
      await secureStorage.setItem(WEEK_NINE_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Nine data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateCustomCrest = async (updates: Partial<CustomCrest>) => {
    const newData = { ...data };
    newData.customCrest = { ...newData.customCrest, ...updates };
    await saveData(newData);
  };

  const updateLiveShowDate = async (updates: Partial<LiveShowDate>) => {
    const newData = { ...data };
    newData.liveShowDate = { ...newData.liveShowDate, ...updates };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    await saveData(newData);
  };

  const updateSharedValues = async (updates: Partial<SharedValues>) => {
    const newData = { ...data };
    newData.sharedValues = { ...newData.sharedValues, ...updates };
    await saveData(newData);
  };

  const updateCompletedSections = async (newSections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = newSections;
    if (newSections.every(section => section)) {
      newData.completedAt = Date.now();
    }
    await saveData(newData);
  };

  const resetData = async () => {
    const initialData: WeekNineData = {
      customCrest: {
        icons: ['', '', ''],
        colors: ['', '', ''],
        symbols: ['', '', ''],
        designNotes: '',
        timestamp: Date.now(),
      },
      liveShowDate: {
        when: '',
        where: '',
        typeOfShow: '',
        completed: false,
        timestamp: Date.now(),
      },
      chatPrompts: [
        { prompt: 'If we could create a new tradition just for us, what would it be?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'What\'s something you wish we did more often as a couple?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
      sharedValues: {
        playerOneValues: ['', '', '', '', ''],
        playerTwoValues: ['', '', '', '', ''],
        sharedValues: ['', '', '', '', ''],
        valuesStatement: '',
        timestamp: Date.now(),
      },
      completedSections: [false, false, false, false],
    };
    await saveData(initialData);
  };

  return {
    data,
    isLoading,
    updateCustomCrest,
    updateLiveShowDate,
    updateChatPrompt,
    updateSharedValues,
    updateCompletedSections,
    resetData,
  };
};
