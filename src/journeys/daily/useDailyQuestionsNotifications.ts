/**
 * Daily Questions Notifications Hook
 *
 * Custom hook for managing daily questions notifications.
 * Provides notification settings and management functionality.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { useState, useEffect, useCallback } from 'react';
import { dailyQuestionsNotificationService, NotificationSettings, NotificationData } from '../services/dailyQuestionsNotificationService';
import { useAuth } from './useAuth';
import { logger } from '../../shared/utils/logger';

export interface UseDailyQuestionsNotificationsReturn {
  // Settings
  settings: NotificationSettings | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  updateSettings: (settings: Partial<NotificationSettings>) => Promise<boolean>;
  sendPartnerResponseNotification: (coupleId: string, questionText: string) => Promise<boolean>;
  sendStreakMilestoneNotification: (coupleId: string, streak: number) => Promise<boolean>;
  
  // Notifications
  pendingNotifications: NotificationData[];
  markNotificationAsSent: (notificationId: string) => Promise<boolean>;
  clearOldNotifications: () => Promise<void>;
}

export function useDailyQuestionsNotifications(): UseDailyQuestionsNotificationsReturn {
  const { user } = useAuth();
  const [settings, setSettings] = useState<NotificationSettings | null>(null);
  const [pendingNotifications, setPendingNotifications] = useState<NotificationData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load settings and notifications
  const loadData = useCallback(async () => {
    if (!user?.id) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const [userSettings, notifications] = await Promise.allSettled([
        dailyQuestionsNotificationService.getNotificationSettings(user.id),
        dailyQuestionsNotificationService.getPendingNotifications(user.id)
      ]);

      setSettings(userSettings.status === 'fulfilled' ? userSettings.value : null);
      setPendingNotifications(notifications.status === 'fulfilled' ? notifications.value : []);

    } catch (err) {
      logger.error('Error loading notification data:', err);
      setError('Failed to load notification settings');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const updateSettings = useCallback(async (newSettings: Partial<NotificationSettings>): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      setError(null);
      const success = await dailyQuestionsNotificationService.updateNotificationSettings(user.id, newSettings);
      
      if (success) {
        setSettings((prev: NotificationSettings | null) => prev ? { ...prev, ...newSettings } : null);
      }
      
      return success;
    } catch (err) {
      logger.error('Error updating notification settings:', err);
      setError('Failed to update notification settings');
      return false;
    }
  }, [user?.id]);

  const sendPartnerResponseNotification = useCallback(async (coupleId: string, questionText: string): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      const success = await dailyQuestionsNotificationService.sendPartnerResponseNotification(user.id, coupleId, questionText);
      
      if (success) {
        // Reload notifications to show the new one
        const notifications = await dailyQuestionsNotificationService.getPendingNotifications(user.id);
        setPendingNotifications(notifications);
      }
      
      return success;
    } catch (err) {
      logger.error('Error sending partner response notification:', err);
      return false;
    }
  }, [user?.id]);

  const sendStreakMilestoneNotification = useCallback(async (coupleId: string, streak: number): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      const success = await dailyQuestionsNotificationService.sendStreakMilestoneNotification(user.id, coupleId, streak);
      
      if (success) {
        // Reload notifications to show the new one
        const notifications = await dailyQuestionsNotificationService.getPendingNotifications(user.id);
        setPendingNotifications(notifications);
      }
      
      return success;
    } catch (err) {
      logger.error('Error sending streak milestone notification:', err);
      return false;
    }
  }, [user?.id]);

  const markNotificationAsSent = useCallback(async (notificationId: string): Promise<boolean> => {
    try {
      const success = await dailyQuestionsNotificationService.markNotificationAsSent(notificationId);
      
      if (success) {
        // Remove from pending notifications
        setPendingNotifications(prev => prev.filter(n => n.id !== notificationId));
      }
      
      return success;
    } catch (err) {
      logger.error('Error marking notification as sent:', err);
      return false;
    }
  }, []);

  const clearOldNotifications = useCallback(async (): Promise<void> => {
    try {
      await dailyQuestionsNotificationService.clearOldNotifications();
      
      // Reload notifications
      if (user?.id) {
        const notifications = await dailyQuestionsNotificationService.getPendingNotifications(user.id);
        setPendingNotifications(notifications);
      }
    } catch (err) {
      logger.error('Error clearing old notifications:', err);
    }
  }, [user?.id]);

  return {
    settings,
    isLoading,
    error,
    updateSettings,
    sendPartnerResponseNotification,
    sendStreakMilestoneNotification,
    pendingNotifications,
    markNotificationAsSent,
    clearOldNotifications,
  };
}