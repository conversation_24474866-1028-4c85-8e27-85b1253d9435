/**
 * Daily Questions Notification Service
 *
 * Handles notifications for daily questions including morning reminders,
 * evening prompts, and partner response notifications.
 * Uses local storage and database for notification management.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { logger } from '../../shared/utils/logger';
import { logEvent } from '../services/analytics/eventLogger';
import { secureStorage } from '../utils/secureStorage';

export interface NotificationSettings {
  morning_reminder: boolean;
  evening_reminder: boolean;
  partner_response_notifications: boolean;
  streak_milestone_notifications: boolean;
  morning_time: string; // HH:MM format
  evening_time: string; // HH:MM format
}

export interface NotificationData {
  id: string;
  user_id: string;
  couple_id: string;
  type: 'daily_question_morning' | 'daily_question_evening' | 'partner_response' | 'streak_milestone';
  title: string;
  body: string;
  data?: Record<string, any>;
  scheduled_for: string;
  sent_at?: string;
  created_at: string;
}

class DailyQuestionsNotificationService {
  private readonly DEFAULT_MORNING_TIME = '09:00';
  private readonly DEFAULT_EVENING_TIME = '19:00';

  /**
   * Get user's notification settings
   */
  async getNotificationSettings(userId: string): Promise<NotificationSettings | null> {
    try {
      // For now, use local storage until database tables are properly set up
      const settings = await secureStorage.getItem<NotificationSettings>('daily_questions_notification_settings');
      return settings || this.getDefaultSettings();
    } catch (error) {
      logger.error('Error getting notification settings:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * Update user's notification settings
   */
  async updateNotificationSettings(
    userId: string,
    settings: Partial<NotificationSettings>
  ): Promise<boolean> {
    try {
      const currentSettings = await this.getNotificationSettings(userId);
      const updatedSettings = { ...currentSettings, ...settings };

      // Store settings locally for now
      await secureStorage.setItem('daily_questions_notification_settings', updatedSettings);

      return true;
    } catch (error) {
      logger.error('Error updating notification settings:', error);
      return false;
    }
  }

  /**
   * Send partner response notification (stored locally for now)
   */
  async sendPartnerResponseNotification(
    userId: string,
    coupleId: string,
    questionText: string
  ): Promise<boolean> {
    try {
      const notification: NotificationData = {
        id: crypto.randomUUID(),
        user_id: userId,
        couple_id: coupleId,
        type: 'partner_response',
        title: 'Your partner answered! 💕',
        body: `Your partner has answered today's question: "${questionText.substring(0, 50)}..."`,
        data: {
          question_text: questionText,
          action: 'view_response'
        },
        scheduled_for: new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      // Store notification locally
      const existingNotifications = await secureStorage.getItem<NotificationData[]>('daily_questions_notifications') || [];
      existingNotifications.push(notification);
      await secureStorage.setItem('daily_questions_notifications', existingNotifications);

      // Log analytics event
      await logEvent('partner_response_notification_sent', {
        user_id: userId,
        couple_id: coupleId
      });

      logger.info('Partner response notification sent');
      return true;
    } catch (error) {
      logger.error('Error sending partner response notification:', error);
      return false;
    }
  }

  /**
   * Send streak milestone notification
   */
  async sendStreakMilestoneNotification(
    userId: string,
    coupleId: string,
    streak: number
  ): Promise<boolean> {
    try {
      const notification: NotificationData = {
        id: crypto.randomUUID(),
        user_id: userId,
        couple_id: coupleId,
        type: 'streak_milestone',
        title: `🔥 ${streak}-Day Streak!`,
        body: `Amazing! You've answered daily questions for ${streak} days in a row. Keep it up!`,
        data: {
          streak_count: streak,
          action: 'view_achievements'
        },
        scheduled_for: new Date().toISOString(),
        created_at: new Date().toISOString()
      };

      // Store notification locally
      const existingNotifications = await secureStorage.getItem<NotificationData[]>('daily_questions_notifications') || [];
      existingNotifications.push(notification);
      await secureStorage.setItem('daily_questions_notifications', existingNotifications);

      // Log analytics event
      await logEvent('streak_milestone_notification_sent', {
        user_id: userId,
        couple_id: coupleId,
        streak_count: streak
      });

      logger.info(`Streak milestone notification sent for ${streak} days`);
      return true;
    } catch (error) {
      logger.error('Error sending streak milestone notification:', error);
      return false;
    }
  }

  /**
   * Get pending notifications for a user
   */
  async getPendingNotifications(userId: string): Promise<NotificationData[]> {
    try {
      const notifications = await secureStorage.getItem<NotificationData[]>('daily_questions_notifications') || [];
      return notifications.filter(n => n.user_id === userId && !n.sent_at);
    } catch (error) {
      logger.error('Error getting pending notifications:', error);
      return [];
    }
  }

  /**
   * Mark notification as sent
   */
  async markNotificationAsSent(notificationId: string): Promise<boolean> {
    try {
      const notifications = await secureStorage.getItem<NotificationData[]>('daily_questions_notifications') || [];
      const updatedNotifications = notifications.map(n =>
        n.id === notificationId ? { ...n, sent_at: new Date().toISOString() } : n
      );
      await secureStorage.setItem('daily_questions_notifications', updatedNotifications);
      return true;
    } catch (error) {
      logger.error('Error marking notification as sent:', error);
      return false;
    }
  }

  /**
   * Clear old notifications (keep only last 50)
   */
  async clearOldNotifications(): Promise<void> {
    try {
      const notifications = await secureStorage.getItem<NotificationData[]>('daily_questions_notifications') || [];
      if (notifications.length > 50) {
        const recentNotifications = notifications.slice(-50);
        await secureStorage.setItem('daily_questions_notifications', recentNotifications);
      }
    } catch (error) {
      logger.error('Error clearing old notifications:', error);
    }
  }

  /**
   * Get default notification settings
   */
  private getDefaultSettings(): NotificationSettings {
    return {
      morning_reminder: true,
      evening_reminder: true,
      partner_response_notifications: true,
      streak_milestone_notifications: true,
      morning_time: this.DEFAULT_MORNING_TIME,
      evening_time: this.DEFAULT_EVENING_TIME
    };
  }
}

export const dailyQuestionsNotificationService = new DailyQuestionsNotificationService();
