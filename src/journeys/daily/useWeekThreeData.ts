import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../../shared/utils/logger';

export interface WouldYouRatherQuestion {
  id: string;
  question: string;
  category: 'petPeeve' | 'fear';
  playerOneAnswer: string;
  playerTwoAnswer: string;
  playerOneReason: string;
  playerTwoReason: string;
  timestamp: number;
}

export interface AlphabetDateNight {
  letter: string;
  when: string;
  where: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface CuriosityQuestion {
  question: string;
  playerOneResponse: string;
  playerTwoResponse: string;
  followUpUsed: string;
  timestamp: number;
}

export interface WeekThreeData {
  wouldYouRather: WouldYouRatherQuestion[];
  alphabetDateNight: AlphabetDateNight;
  chatPrompts: ChatPrompt[];
  curiosityQuestions: CuriosityQuestion[];
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_THREE_STORAGE_KEY = 'week_three_data';

const DEFAULT_WYR_QUESTIONS: Omit<WouldYouRatherQuestion, 'id' | 'playerOneAnswer' | 'playerTwoAnswer' | 'playerOneReason' | 'playerTwoReason' | 'timestamp'>[] = [
  // Pet Peeve Edition
  {
    question: 'Chewing loudly for an hour vs. everything in wrong place',
    category: 'petPeeve'
  },
  {
    question: 'Stuck in traffic vs. messy kitchen every day',
    category: 'petPeeve'
  },
  {
    question: 'Dirty socks everywhere vs. off-key humming all day',
    category: 'petPeeve'
  },
  {
    question: 'Constant interrupter vs. finger tapper',
    category: 'petPeeve'
  },
  // Fear Edition
  {
    question: 'Lose phone for a day vs. message wrong person',
    category: 'fear'
  },
  {
    question: 'Karaoke in front of strangers vs. joke with no laughs',
    category: 'fear'
  },
  {
    question: 'Rollercoaster ride vs. speaking to 100 people',
    category: 'fear'
  },
  {
    question: 'Swim across a calm lake at night vs. hike woods alone in the day',
    category: 'fear'
  }
];

const DEFAULT_CHAT_PROMPTS = [
  'What would your younger self not believe about your life today?',
  'What are you ready to let go of?'
];

const DEFAULT_CURIOSITY_QUESTIONS = [
  'What\'s been on your mind this week?',
  'What\'s something you\'re excited about?',
  'When do you feel most at ease?'
];

const FOLLOW_UP_PROMPTS = [
  'What made that stand out?',
  'What do you think that means for you?',
  'Can you tell me more?'
];

export const useWeekThreeData = () => {
  const [data, setData] = useState<WeekThreeData>({
    wouldYouRather: DEFAULT_WYR_QUESTIONS.map((q, index) => ({
      id: `wyr-${index}`,
      ...q,
      playerOneAnswer: '',
      playerTwoAnswer: '',
      playerOneReason: '',
      playerTwoReason: '',
      timestamp: Date.now()
    })),
    alphabetDateNight: { 
      letter: '', 
      when: '', 
      where: '', 
      completed: false, 
      timestamp: Date.now() 
    },
    chatPrompts: DEFAULT_CHAT_PROMPTS.map((prompt, index) => ({
      prompt,
      playerOneAnswer: '',
      playerTwoAnswer: '',
      timestamp: Date.now()
    })),
    curiosityQuestions: DEFAULT_CURIOSITY_QUESTIONS.map((question, index) => ({
      question,
      playerOneResponse: '',
      playerTwoResponse: '',
      followUpUsed: '',
      timestamp: Date.now()
    })),
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const stored = await secureStorage.getItem<WeekThreeData>(WEEK_THREE_STORAGE_KEY);
      if (stored) {
        setData(stored);
      }
    } catch (error) {
      logger.error('Error loading week three data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekThreeData) => {
    try {
      await secureStorage.setItem(WEEK_THREE_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving week three data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateWouldYouRather = (questionId: string, updates: Partial<WouldYouRatherQuestion>) => {
    const newData = {
      ...data,
      wouldYouRather: data.wouldYouRather.map(q => 
        q.id === questionId ? { ...q, ...updates } : q
      )
    };
    saveData(newData);
  };

  const updateAlphabetDateNight = (updates: Partial<AlphabetDateNight>) => {
    const newData = {
      ...data,
      alphabetDateNight: { ...data.alphabetDateNight, ...updates }
    };
    saveData(newData);
  };

  const updateChatPrompt = (promptIndex: number, updates: Partial<ChatPrompt>) => {
    const newData = {
      ...data,
      chatPrompts: data.chatPrompts.map((p, index) => 
        index === promptIndex ? { ...p, ...updates } : p
      )
    };
    saveData(newData);
  };

  const updateCuriosityQuestion = (questionIndex: number, updates: Partial<CuriosityQuestion>) => {
    const newData = {
      ...data,
      curiosityQuestions: data.curiosityQuestions.map((q, index) => 
        index === questionIndex ? { ...q, ...updates } : q
      )
    };
    saveData(newData);
  };

  const updateCompletedSections = (sections: boolean[]) => {
    const newData = {
      ...data,
      completedSections: sections,
      completedAt: sections.every(s => s) ? Date.now() : undefined
    };
    saveData(newData);
  };

  const getFollowUpPrompts = () => FOLLOW_UP_PROMPTS;

  return {
    data,
    isLoading,
    updateWouldYouRather,
    updateAlphabetDateNight,
    updateChatPrompt,
    updateCuriosityQuestion,
    updateCompletedSections,
    getFollowUpPrompts
  };
};
