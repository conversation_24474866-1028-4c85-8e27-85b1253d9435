import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../../shared/utils/logger';

export interface SuperheroDuoChart {
  playerOneSuperpower: string;
  playerOneWeakness: string;
  playerTwoSuperpower: string;
  playerTwoWeakness: string;
  combinedPower: string;
  villain: string;
  timestamp: number;
}

export interface ThriftShopShowdown {
  when: string;
  where: string;
  budget: string;
  timeLimit: string;
  giftDescription: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface TurningTowardPractice {
  bidDescription: string;
  response: string;
  whatChanged: string;
  timestamp: number;
}

export interface WeekSevenData {
  superheroDuoChart: SuperheroDuoChart;
  thriftShopShowdown: ThriftShopShowdown;
  chatPrompts: ChatPrompt[];
  turningTowardPractice: TurningTowardPractice[];
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_SEVEN_STORAGE_KEY = 'week_seven_data';

export const useWeekSevenData = () => {
  const [data, setData] = useState<WeekSevenData>({
    superheroDuoChart: {
      playerOneSuperpower: '',
      playerOneWeakness: '',
      playerTwoSuperpower: '',
      playerTwoWeakness: '',
      combinedPower: '',
      villain: '',
      timestamp: Date.now(),
    },
    thriftShopShowdown: {
      when: '',
      where: '',
      budget: '',
      timeLimit: '',
      giftDescription: '',
      completed: false,
      timestamp: Date.now(),
    },
    chatPrompts: [
      { prompt: 'If we opened a business together, what would it be?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'What\'s a moment you think shaped us as a couple?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    turningTowardPractice: [
      { bidDescription: '', response: '', whatChanged: '', timestamp: Date.now() },
      { bidDescription: '', response: '', whatChanged: '', timestamp: Date.now() },
      { bidDescription: '', response: '', whatChanged: '', timestamp: Date.now() },
    ],
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekSevenData>(WEEK_SEVEN_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Seven data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekSevenData) => {
    try {
      await secureStorage.setItem(WEEK_SEVEN_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Seven data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateSuperheroDuoChart = async (updates: Partial<SuperheroDuoChart>) => {
    const newData = { ...data };
    newData.superheroDuoChart = { ...newData.superheroDuoChart, ...updates };
    await saveData(newData);
  };

  const updateThriftShopShowdown = async (updates: Partial<ThriftShopShowdown>) => {
    const newData = { ...data };
    newData.thriftShopShowdown = { ...newData.thriftShopShowdown, ...updates };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    await saveData(newData);
  };

  const updateTurningTowardPractice = async (index: number, updates: Partial<TurningTowardPractice>) => {
    const newData = { ...data };
    newData.turningTowardPractice[index] = { ...newData.turningTowardPractice[index], ...updates };
    await saveData(newData);
  };

  const updateCompletedSections = async (newSections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = newSections;
    if (newSections.every(section => section)) {
      newData.completedAt = Date.now();
    }
    await saveData(newData);
  };

  const resetData = async () => {
    const initialData: WeekSevenData = {
      superheroDuoChart: {
        playerOneSuperpower: '',
        playerOneWeakness: '',
        playerTwoSuperpower: '',
        playerTwoWeakness: '',
        combinedPower: '',
        villain: '',
        timestamp: Date.now(),
      },
      thriftShopShowdown: {
        when: '',
        where: '',
        budget: '',
        timeLimit: '',
        giftDescription: '',
        completed: false,
        timestamp: Date.now(),
      },
      chatPrompts: [
        { prompt: 'If we opened a business together, what would it be?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'What\'s a moment you think shaped us as a couple?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
      turningTowardPractice: [
        { bidDescription: '', response: '', whatChanged: '', timestamp: Date.now() },
        { bidDescription: '', response: '', whatChanged: '', timestamp: Date.now() },
        { bidDescription: '', response: '', whatChanged: '', timestamp: Date.now() },
      ],
      completedSections: [false, false, false, false],
    };
    await saveData(initialData);
  };

  return {
    data,
    isLoading,
    updateSuperheroDuoChart,
    updateThriftShopShowdown,
    updateChatPrompt,
    updateTurningTowardPractice,
    updateCompletedSections,
    resetData,
  };
};
