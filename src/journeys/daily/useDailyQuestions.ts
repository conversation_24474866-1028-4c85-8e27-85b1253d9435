/**
 * Daily Questions Hook
 *
 * Custom hook for managing daily questions state and operations.
 * Provides reactive data and methods for the daily questions feature.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { useCallback, useEffect, useState } from 'react';
import { logger } from '../../shared/utils/logger';
import { dailyQuestionsService } from '../services/dailyQuestionsService';
import type {
    CoupleResponseStatus,
    DailyQuestion,
    QuestionHistory,
    StreakData
} from '../types';
import { useAuth } from './useAuth';
import { useUserProfile } from './useUserProfile';

export interface UseDailyQuestionsReturn {
  // Current question data
  todaysQuestion: DailyQuestion | null;
  responseStatus: CoupleResponseStatus[];
  isLoading: boolean;
  error: string | null;

  // User's data
  userResponse: string | null;
  partnerResponse: string | null;
  hasUserAnswered: boolean;
  hasPartnerAnswered: boolean;

  // Streak data
  streakData: StreakData | null;

  // Actions
  submitResponse: (responseText: string) => Promise<boolean>;
  updateResponse: (responseText: string) => Promise<boolean>;
  addReaction: (responseId: string, reactionType: 'heart' | 'laugh' | 'surprise' | 'love') => Promise<boolean>;
  addComment: (responseId: string, commentText: string) => Promise<boolean>;
  skipQuestion: () => Promise<boolean>;
  refreshData: () => Promise<void>;

  // History
  questionHistory: QuestionHistory[];
  loadMoreHistory: () => Promise<void>;
  isHistoryLoading: boolean;
}

export function useDailyQuestions(): UseDailyQuestionsReturn {
  const { user } = useAuth();
  const { profile } = useUserProfile();

  // State
  const [todaysQuestion, setTodaysQuestion] = useState<DailyQuestion | null>(null);
  const [responseStatus, setResponseStatus] = useState<CoupleResponseStatus[]>([]);
  const [streakData, setStreakData] = useState<StreakData | null>(null);
  const [questionHistory, setQuestionHistory] = useState<QuestionHistory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [historyOffset, setHistoryOffset] = useState(0);

  // Derived state
  const userResponse = responseStatus.find(r => r.user_id === user?.id)?.response_text || null;
  const partnerResponse = responseStatus.find(r => r.user_id !== user?.id)?.response_text || null;
  const hasUserAnswered = responseStatus.find(r => r.user_id === user?.id)?.has_answered || false;
  const hasPartnerAnswered = responseStatus.find(r => r.user_id !== user?.id)?.has_answered || false;

  // Load initial data
  const loadData = useCallback(async () => {
    if (!user || !profile?.coupleId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Load today's question and response status in parallel
      const [question, status, streak] = await Promise.allSettled([
        dailyQuestionsService.getTodaysQuestion(profile.coupleId),
        dailyQuestionsService.getCoupleResponseStatus(profile.coupleId),
        dailyQuestionsService.getStreakData(user.id)
      ]);

      // Handle results with proper error handling
      setTodaysQuestion(question.status === 'fulfilled' ? question.value : null);
      setResponseStatus(status.status === 'fulfilled' ? status.value : []);
      setStreakData(streak.status === 'fulfilled' ? streak.value : null);

      // Load initial history
      const history = await dailyQuestionsService.getQuestionHistory(
        profile.coupleId,
        user.id,
        10,
        0
      );
      setQuestionHistory(history);
      setHistoryOffset(10);

    } catch (err) {
      logger.error('Error loading daily questions data:', err);
      setError('Failed to load daily questions');
    } finally {
      setIsLoading(false);
    }
  }, [user, profile?.coupleId]);

  // Load more history
  const loadMoreHistory = useCallback(async () => {
    if (!user || !profile?.coupleId || isHistoryLoading) return;

    try {
      setIsHistoryLoading(true);

      const moreHistory = await dailyQuestionsService.getQuestionHistory(
        profile.coupleId,
        user.id,
        10,
        historyOffset
      );

      setQuestionHistory(prev => [...prev, ...moreHistory]);
      setHistoryOffset(prev => prev + 10);

    } catch (err) {
      logger.error('Error loading more history:', err);
    } finally {
      setIsHistoryLoading(false);
    }
  }, [user, profile?.coupleId, historyOffset, isHistoryLoading]);

  // Submit response
  const submitResponse = useCallback(async (responseText: string): Promise<boolean> => {
    if (!user || !profile?.coupleId || !todaysQuestion) return false;

    try {
      const response = await dailyQuestionsService.submitResponse(
        user.id,
        profile.coupleId,
        todaysQuestion.question_id,
        responseText
      );

      if (response) {
        // Refresh data to show updated response
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error submitting response:', err);
      return false;
    }
  }, [user, profile?.coupleId, todaysQuestion, loadData]);

  // Update response
  const updateResponse = useCallback(async (responseText: string): Promise<boolean> => {
    if (!user || !responseStatus) return false;

    const userResponseData = responseStatus.find(r => r.user_id === user.id);
    if (!userResponseData || !userResponseData.has_answered) return false;

    try {
      // Find the response ID from the current data
      const responseId = questionHistory
        .find(h => h.user_response?.user_id === user.id)
        ?.user_response?.id;

      if (!responseId) return false;

      const response = await dailyQuestionsService.updateResponse(
        responseId,
        responseText,
        user.id
      );

      if (response) {
        // Refresh data to show updated response
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error updating response:', err);
      return false;
    }
  }, [user, responseStatus, questionHistory, loadData]);

  // Add reaction
  const addReaction = useCallback(async (
    responseId: string,
    reactionType: 'heart' | 'laugh' | 'surprise' | 'love'
  ): Promise<boolean> => {
    if (!user) return false;

    try {
      const reaction = await dailyQuestionsService.addReaction(
        responseId,
        user.id,
        reactionType
      );

      if (reaction) {
        // Refresh data to show updated reactions
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error adding reaction:', err);
      return false;
    }
  }, [user, loadData]);

  // Add comment
  const addComment = useCallback(async (
    responseId: string,
    commentText: string
  ): Promise<boolean> => {
    if (!user) return false;

    try {
      const comment = await dailyQuestionsService.addComment(
        responseId,
        user.id,
        commentText
      );

      if (comment) {
        // Refresh data to show updated comments
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error adding comment:', err);
      return false;
    }
  }, [user, loadData]);

  // Skip question
  const skipQuestion = useCallback(async (): Promise<boolean> => {
    if (!user || !profile?.coupleId) return false;

    try {
      const success = await dailyQuestionsService.skipTodaysQuestion(
        user.id,
        profile.coupleId
      );

      if (success) {
        // Refresh data to potentially show a new question
        await loadData();
        return true;
      }

      return false;
    } catch (err) {
      logger.error('Error skipping question:', err);
      return false;
    }
  }, [user, profile?.coupleId, loadData]);

  // Refresh data
  const refreshData = useCallback(async () => {
    await loadData();
  }, [loadData]);

  // Load data on mount and when dependencies change
  useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    // Current question data
    todaysQuestion,
    responseStatus,
    isLoading,
    error,

    // User's data
    userResponse,
    partnerResponse,
    hasUserAnswered,
    hasPartnerAnswered,

    // Streak data
    streakData,

    // Actions
    submitResponse,
    updateResponse,
    addReaction,
    addComment,
    skipQuestion,
    refreshData,

    // History
    questionHistory,
    loadMoreHistory,
    isHistoryLoading,
  };
}

export { useDailyQuestions };
