/**
 * Daily Journey - Index
 * 
 * Centralized exports for all daily engagement functionality:
 * - Daily questions and responses
 * - Streak tracking and achievements
 * - Daily challenges and activities
 * - Weekly progress data
 * 
 * <AUTHOR> Us Team
 */

// Hooks
export { useDailyQuestions } from './useDailyQuestions';
export { useDailyQuestionsNotifications } from './useDailyQuestionsNotifications';
export { useStreakData } from './useStreakData';
export { useDailyChallenges } from './useDailyChallenges';

// Weekly data hooks
export { useWeekOneData } from './useWeekOneData';
export { useWeekTwoData } from './useWeekTwoData';
export { useWeekThreeData } from './useWeekThreeData';
export { useWeekFourData } from './useWeekFourData';
export { useWeekFiveData } from './useWeekFiveData';
export { useWeekSixData } from './useWeekSixData';
export { useWeekSevenData } from './useWeekSevenData';
export { useWeekEightData } from './useWeekEightData';
export { useWeekNineData } from './useWeekNineData';
export { useWeekTenData } from './useWeekTenData';
export { useWeekElevenData } from './useWeekElevenData';
export { useWeekTwelveData } from './useWeekTwelveData';
export { useGenericWeekData } from './useGenericWeekData';

// Services
export { default as dailyQuestionsService } from './dailyQuestionsService';
export { default as dailyQuestionsNotificationService } from './dailyQuestionsNotificationService';
export { default as streakEventService } from './streakEventService';

// Types
export type {
  DailyQuestion,
  QuestionResponse,
  StreakData,
  CoupleResponseStatus,
  WeeklyData,
  DailyChallenge
} from './daily.types';
