import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { sanitizeText } from '../utils/validation';
import { logger } from '../../shared/utils/logger';

export interface LoveLanguageQuiz {
  questions: {
    question: string;
    playerOneAnswer: string;
    playerTwoAnswer: string;
  }[];
  results: {
    words: number;
    touch: number;
    gifts: number;
    qualityTime: number;
    acts: number;
  };
  timestamp: number;
}

export interface MiniOlympics {
  when: string;
  where: string;
  gamesPlayed: string[];
  winner: string;
  completed: boolean;
  timestamp: number;
}

export interface ChatPrompt {
  prompt: string;
  playerOneAnswer: string;
  playerTwoAnswer: string;
  timestamp: number;
}

export interface LoveLanguagePractice {
  partnerLoveLanguage: string;
  actionOne: string;
  actionTwo: string;
  timestamp: number;
}

export interface WeekElevenData {
  loveLanguageQuiz: LoveLanguageQuiz;
  miniOlympics: MiniOlympics;
  chatPrompts: ChatPrompt[];
  loveLanguagePractice: LoveLanguagePractice[];
  completedSections: boolean[];
  completedAt?: number;
}

const WEEK_ELEVEN_STORAGE_KEY = 'week_eleven_data';

export const useWeekElevenData = () => {
  const [data, setData] = useState<WeekElevenData>({
    loveLanguageQuiz: {
      questions: [
        { question: 'I feel most loved when my partner...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'When I\'m stressed, I appreciate when my partner...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'I show love by...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'I feel valued when my partner...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'My ideal date involves...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'I feel connected when we...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'I appreciate small gestures like...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'I feel supported when...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'I express affection through...', playerOneAnswer: '', playerTwoAnswer: '' },
        { question: 'I feel most secure when...', playerOneAnswer: '', playerTwoAnswer: '' },
      ],
      results: {
        words: 0,
        touch: 0,
        gifts: 0,
        qualityTime: 0,
        acts: 0,
      },
      timestamp: Date.now(),
    },
    miniOlympics: {
      when: '',
      where: '',
      gamesPlayed: [],
      winner: '',
      completed: false,
      timestamp: Date.now(),
    },
    chatPrompts: [
      { prompt: 'Who has had the biggest positive impact on you?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      { prompt: 'What in your life are you most grateful for?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
    ],
    loveLanguagePractice: [
      { partnerLoveLanguage: '', actionOne: '', actionTwo: '', timestamp: Date.now() },
      { partnerLoveLanguage: '', actionOne: '', actionTwo: '', timestamp: Date.now() },
    ],
    completedSections: [false, false, false, false],
  });

  const [isLoading, setIsLoading] = useState(true);

  // Load data from storage on mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const storedData = await secureStorage.getItem<WeekElevenData>(WEEK_ELEVEN_STORAGE_KEY);
      if (storedData) {
        setData(storedData);
      }
    } catch (error) {
      logger.error('Error loading Week Eleven data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveData = async (newData: WeekElevenData) => {
    try {
      await secureStorage.setItem(WEEK_ELEVEN_STORAGE_KEY, newData);
      setData(newData);
    } catch (error) {
      logger.error('Error saving Week Eleven data:', error);
      throw new Error('Failed to save data securely');
    }
  };

  const updateLoveLanguageQuiz = async (updates: Partial<LoveLanguageQuiz>) => {
    const newData = { ...data };
    newData.loveLanguageQuiz = { ...newData.loveLanguageQuiz, ...updates };
    await saveData(newData);
  };

  const updateMiniOlympics = async (updates: Partial<MiniOlympics>) => {
    const newData = { ...data };
    newData.miniOlympics = { ...newData.miniOlympics, ...updates };
    await saveData(newData);
  };

  const updateChatPrompt = async (index: number, updates: Partial<ChatPrompt>) => {
    const newData = { ...data };
    newData.chatPrompts[index] = { ...newData.chatPrompts[index], ...updates };
    await saveData(newData);
  };

  const updateLoveLanguagePractice = async (index: number, updates: Partial<LoveLanguagePractice>) => {
    const newData = { ...data };
    newData.loveLanguagePractice[index] = { ...newData.loveLanguagePractice[index], ...updates };
    await saveData(newData);
  };

  const updateCompletedSections = async (newSections: boolean[]) => {
    const newData = { ...data };
    newData.completedSections = newSections;
    if (newSections.every(section => section)) {
      newData.completedAt = Date.now();
    }
    await saveData(newData);
  };

  const resetData = async () => {
    const initialData: WeekElevenData = {
      loveLanguageQuiz: {
        questions: [
          { question: 'I feel most loved when my partner...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'When I\'m stressed, I appreciate when my partner...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'I show love by...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'I feel valued when my partner...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'My ideal date involves...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'I feel connected when we...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'I appreciate small gestures like...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'I feel supported when...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'I express affection through...', playerOneAnswer: '', playerTwoAnswer: '' },
          { question: 'I feel most secure when...', playerOneAnswer: '', playerTwoAnswer: '' },
        ],
        results: {
          words: 0,
          touch: 0,
          gifts: 0,
          qualityTime: 0,
          acts: 0,
        },
        timestamp: Date.now(),
      },
      miniOlympics: {
        when: '',
        where: '',
        gamesPlayed: [],
        winner: '',
        completed: false,
        timestamp: Date.now(),
      },
      chatPrompts: [
        { prompt: 'Who has had the biggest positive impact on you?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
        { prompt: 'What in your life are you most grateful for?', playerOneAnswer: '', playerTwoAnswer: '', timestamp: Date.now() },
      ],
      loveLanguagePractice: [
        { partnerLoveLanguage: '', actionOne: '', actionTwo: '', timestamp: Date.now() },
        { partnerLoveLanguage: '', actionOne: '', actionTwo: '', timestamp: Date.now() },
      ],
      completedSections: [false, false, false, false],
    };
    await saveData(initialData);
  };

  return {
    data,
    isLoading,
    updateLoveLanguageQuiz,
    updateMiniOlympics,
    updateChatPrompt,
    updateLoveLanguagePractice,
    updateCompletedSections,
    resetData,
  };
};
