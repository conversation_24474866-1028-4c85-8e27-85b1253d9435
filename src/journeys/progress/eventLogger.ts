import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../supabase';
import { logger } from '../../shared/utils/logger';

// Keys for local queue
const QUEUE_KEY = 'event_queue_v1';

type EventPayload = {
  name: string;
  metadata?: Record<string, any>;
  // Optional client-generated id to dedupe if needed
  clientId?: string;
  // Timestamp in ms (client clock); server sets created_at
  ts?: number;
};

async function loadQueue(): Promise<EventPayload[]> {
  try {
    const raw = await AsyncStorage.getItem(QUEUE_KEY);
    if (!raw) return [];
    return JSON.parse(raw) as EventPayload[];
  } catch (e) {
    logger.error('eventLogger.loadQueue error', e);
    return [];
  }
}

async function saveQueue(queue: EventPayload[]) {
  try {
    await AsyncStorage.setItem(QUEUE_KEY, JSON.stringify(queue));
  } catch (e) {
    logger.error('eventLogger.saveQueue error', e);
  }
}

export async function logEvent(name: string, metadata: Record<string, any> = {}) {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      // Queue for backfill when the user authenticates
      const q = await loadQueue();
      q.unshift({ name, metadata, ts: Date.now() });
      await saveQueue(q);
      logger.info('Queued guest event', { name });
      return { queued: true } as const;
    }

    // Attempt insert; treat duplicate unique index as success
    const { error } = await supabase.from('user_events').insert({
      user_id: user.id,
      event_name: name,
      metadata,
    });

    if (error) {
      // Unique violation code from Postgres (23505) should be treated as success
      const message = (error as any)?.message || '';
      const code = (error as any)?.code || '';
      if (code === '23505' || /duplicate key/i.test(message)) {
        logger.info('Event already exists (idempotent)', { name, userId: user.id });
        return { ok: true, duplicate: true } as const;
      }
      logger.error('Failed to log event', { name, error });
      // Fallback to queue so it can retry later
      const q = await loadQueue();
      q.unshift({ name, metadata, ts: Date.now() });
      await saveQueue(q);
      return { queued: true, error } as const;
    }

    logger.info('Event logged', { name, userId: user.id });
    return { ok: true } as const;
  } catch (e) {
    logger.error('Unexpected error in logEvent', e);
    const q = await loadQueue();
    q.unshift({ name, metadata, ts: Date.now() });
    await saveQueue(q);
    return { queued: true, error: e } as const;
  }
}

// Flush queued guest/offline events when user becomes authenticated
export async function flushQueuedEvents() {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return { flushed: 0 };

    const queue = await loadQueue();
    if (!queue.length) return { flushed: 0 };

    let flushed = 0;
    const remaining: EventPayload[] = [];

    for (const evt of queue.reverse()) {
      const { error } = await supabase.from('user_events').insert({
        user_id: user.id,
        event_name: evt.name,
        metadata: evt.metadata ?? {},
      });

      if (error) {
        const message = (error as any)?.message || '';
        const code = (error as any)?.code || '';
        if (code === '23505' || /duplicate key/i.test(message)) {
          flushed += 1; // already recorded
        } else {
          // keep it for another retry
          remaining.push(evt);
          logger.warn('Failed to flush event; will retry later', { name: evt.name, error });
        }
      } else {
        flushed += 1;
      }
    }

    await saveQueue(remaining.reverse());
    logger.info('Flushed queued events', { flushed, remaining: remaining.length });
    return { flushed, remaining: remaining.length };
  } catch (e) {
    logger.error('flushQueuedEvents error', e);
    return { flushed: 0, error: e };
  }
}

// Hook to be called on auth state change
export function attachAuthFlushListener() {
  try {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event: any) => {
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        await flushQueuedEvents();
      }
    });
    return () => subscription.unsubscribe();
  } catch (e) {
    logger.error('attachAuthFlushListener error', e);
    return () => {};
  }
}

