/**
 * Progress Journey - Index
 * 
 * Centralized exports for all progress tracking and analytics functionality:
 * - Points system and achievements
 * - User engagement tracking
 * - Performance monitoring
 * - Analytics and reporting
 * 
 * <AUTHOR> Us Team
 */

// Hooks
export { usePointsSystemSupabase } from './usePointsSystemSupabase';
export { useEngagementSystem } from './useEngagementSystem';
export { useUserEvents } from './useUserEvents';
export { usePerformance } from './usePerformance';
export { useHomeScreen } from './useHomeScreen';

// Services
export { default as pointsSystemService } from './pointsSystemService';
export { default as performanceOptimizationService } from './performanceOptimizationService';

// Analytics services (consolidated)
export * from './eventLogger';

// Utils
export * from './userEventUtils';

// Types
export type {
  PointsData,
  Achievement,
  UserEvent,
  EngagementMetrics,
  PerformanceData,
  AnalyticsEvent
} from './progress.types';
