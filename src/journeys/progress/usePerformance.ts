/**
 * Performance Optimization Hook
 *
 * Provides utilities for performance optimization including memoization,
 * debouncing, throttling, and lazy loading helpers.
 *
 * @version 3.0.0
 * <AUTHOR> Us Team
 */

import { useCallback, useEffect, useRef, useState } from 'react';

declare const __DEV__: boolean;

// =============================================================================
// DEBOUNCE HOOK
// =============================================================================

/**
 * Debounce a value to prevent excessive updates
 */
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// =============================================================================
// THROTTLE HOOK
// =============================================================================

/**
 * Throttle a function to limit execution frequency
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        func(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [func, delay]
  );
};

// =============================================================================
// MEMOIZATION HELPERS
// =============================================================================

/**
 * Memoize expensive calculations with custom equality check
 */
export const useMemoWithComparator = <T>(
  factory: () => T,
  deps: React.DependencyList,
  compareFn?: (prev: React.DependencyList, next: React.DependencyList) => boolean
): T => {
  const ref = useRef<{
    deps: React.DependencyList;
    value: T;
  } | undefined>(undefined);

  const defaultCompare = (prev: React.DependencyList, next: React.DependencyList) => {
    if (prev.length !== next.length) return false;
    return prev.every((item, index) => Object.is(item, next[index]));
  };

  const compare = compareFn || defaultCompare;

  if (!ref.current || !compare(ref.current.deps, deps)) {
    ref.current = {
      deps,
      value: factory(),
    };
  }

  return ref.current.value;
};

/**
 * Memoize async operations
 */
export const useAsyncMemo = <T>(
  factory: () => Promise<T>,
  deps: React.DependencyList,
  initialValue: T
): { value: T; loading: boolean; error: Error | null } => {
  const [state, setState] = useState<{
    value: T;
    loading: boolean;
    error: Error | null;
  }>({
    value: initialValue,
    loading: false,
    error: null,
  });

  const memoizedFactory = useCallback(factory, deps);

  useEffect(() => {
    let cancelled = false;

    setState(prev => ({ ...prev, loading: true, error: null }));

    memoizedFactory()
      .then(value => {
        if (!cancelled) {
          setState({ value, loading: false, error: null });
        }
      })
      .catch(error => {
        if (!cancelled) {
          setState(prev => ({ ...prev, loading: false, error }));
        }
      });

    return () => {
      cancelled = true;
    };
  }, [memoizedFactory]);

  return state;
};

// =============================================================================
// LAZY LOADING HELPERS
// =============================================================================

/**
 * Lazy load data when component becomes visible
 */
export const useLazyLoad = <T>(
  loader: () => Promise<T>,
  initialValue: T,
  threshold: number = 0.1
): {
  value: T;
  loading: boolean;
  error: Error | null;
  ref: React.RefObject<any>;
} => {
  const [state, setState] = useState<{
    value: T;
    loading: boolean;
    error: Error | null;
    hasLoaded: boolean;
  }>({
    value: initialValue,
    loading: false,
    error: null,
    hasLoaded: false,
  });

  const ref = useRef<any>(null);

  useEffect(() => {
    if (state.hasLoaded) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setState(prev => ({ ...prev, loading: true }));

          loader()
            .then(value => {
              setState({
                value,
                loading: false,
                error: null,
                hasLoaded: true,
              });
            })
            .catch(error => {
              setState(prev => ({
                ...prev,
                loading: false,
                error,
                hasLoaded: true,
              }));
            });

          observer.disconnect();
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [loader, threshold, state.hasLoaded]);

  return {
    value: state.value,
    loading: state.loading,
    error: state.error,
    ref,
  };
};

// =============================================================================
// PERFORMANCE MONITORING
// =============================================================================

/**
 * Monitor component render performance
 */
export const useRenderPerformance = (componentName: string, enabled: boolean = __DEV__) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    if (!enabled) return;

    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    lastRenderTime.current = now;

    if (renderCount.current > 1) {
      console.log(
        `🔄 ${componentName} rendered ${renderCount.current} times. ` +
        `Time since last render: ${timeSinceLastRender}ms`
      );

      // Warn about frequent re-renders
      if (timeSinceLastRender < 16) { // Less than one frame (60fps)
        console.warn(
          `⚠️ ${componentName} is re-rendering very frequently (${timeSinceLastRender}ms). ` +
          'Consider optimizing with React.memo, useMemo, or useCallback.'
        );
      }
    }
  });

  return {
    renderCount: renderCount.current,
    reset: () => {
      renderCount.current = 0;
      lastRenderTime.current = Date.now();
    },
  };
};

/**
 * Track expensive operations
 */
export const usePerformanceTracker = () => {
  const track = useCallback(<T>(name: string, operation: () => T): T => {
    const start = performance.now();
    const result = operation();
    const end = performance.now();

    if (__DEV__) {
      console.log(`⏱️ ${name} took ${(end - start).toFixed(2)}ms`);

      // Warn about slow operations
      if (end - start > 100) {
        console.warn(`🐌 ${name} is slow (${(end - start).toFixed(2)}ms). Consider optimization.`);
      }
    }

    return result;
  }, []);

  const trackAsync = useCallback(async <T>(name: string, operation: () => Promise<T>): Promise<T> => {
    const start = performance.now();
    const result = await operation();
    const end = performance.now();

    if (__DEV__) {
      console.log(`⏱️ ${name} took ${(end - start).toFixed(2)}ms`);

      // Warn about slow async operations
      if (end - start > 500) {
        console.warn(`🐌 ${name} is slow (${(end - start).toFixed(2)}ms). Consider optimization.`);
      }
    }

    return result;
  }, []);

  return { track, trackAsync };
};

export default {
  useDebounce,
  useThrottle,
  useMemoWithComparator,
  useAsyncMemo,
  useLazyLoad,
  useRenderPerformance,
  usePerformanceTracker,
};
