import { useCallback, useEffect, useState } from 'react';
import { usePointsSystemSupabase } from './usePointsSystemSupabase';
// import { useDailyChallenges } from './useDailyChallenges';
// import { useMilestones } from './useMilestones';
import { colors } from '../../utils/colors';
import { getStartOfToday } from '../utils/dateUtils';

export interface EngagementEvent {
  id: string;
  type: 'activity_completed' | 'module_completed' | 'challenge_completed' | 'milestone_unlocked' | 'streak_achieved';
  title: string;
  description: string;
  points: number;
  bonusPoints: number;
  timestamp: number;
  celebrationData?: {
    icon: string;
    color: string[];
    message: string;
  };
}

export interface EngagementStats {
  totalEvents: number;
  todayEvents: number;
  weeklyEvents: number;
  monthlyEvents: number;
  averagePointsPerDay: number;
  engagementScore: number; // 0-100
  motivationLevel: 'low' | 'medium' | 'high' | 'excellent';
}

export const useEngagementSystem = () => {
  const { totalPoints, level, achievements } = usePointsSystemSupabase();
  const { dailyChallenges, challengeStreak, getTodaysChallenges, getTodaysCompletedChallenges } = useDailyChallenges();
  const { milestones, checkMilestones, getUnlockedMilestones, getNextMilestone, getProgressTowardsNext } = useMilestones();

  const [engagementEvents, setEngagementEvents] = useState<EngagementEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load engagement data on mount
  useEffect(() => {
    loadEngagementData();
  }, []);

  // Check for new achievements whenever points data changes
  useEffect(() => {
    if (totalPoints > 0) {
      checkForNewAchievements();
    }
  }, [totalPoints]);

  const loadEngagementData = async () => {
    try {
      // Check milestones based on current progress
      const progress = {
        totalPoints: totalPoints,
        currentStreak: 0, // TODO: Implement streak tracking in Supabase
        longestStreak: 0, // TODO: Implement streak tracking in Supabase
        completedModules: 0, // TODO: Implement module completion tracking
        completedActivities: 0, // TODO: Implement activity completion tracking
        totalModules: 12, // Fixed number of modules
        totalActivities: 48 // Fixed number of activities (12 modules * 4 activities each)
      };

      await checkMilestones(progress);
      setIsLoading(false);
    } catch (error) {
      console.error('Error loading engagement data:', error);
      setIsLoading(false);
    }
  };

  const checkForNewAchievements = useCallback(async () => {
    try {
      const progress = {
        totalPoints: totalPoints,
        currentStreak: 0, // TODO: Implement streak tracking in Supabase
        longestStreak: 0, // TODO: Implement streak tracking in Supabase
        completedModules: 0, // TODO: Implement module completion tracking
        completedActivities: 0, // TODO: Implement activity completion tracking
        totalModules: 12, // Fixed number of modules
        totalActivities: 48 // Fixed number of activities (12 modules * 4 activities each)
      };

      const newlyUnlocked = await checkMilestones(progress);

      // Create engagement events for new milestones
      newlyUnlocked.forEach((milestone: any) => {
        const event: EngagementEvent = {
          id: `milestone-${milestone.id}`,
          type: 'milestone_unlocked',
          title: milestone.title,
          description: milestone.description,
          points: milestone.points,
          bonusPoints: milestone.bonusPoints,
          timestamp: Date.now(),
          celebrationData: {
            icon: milestone.icon,
            color: milestone.color,
            message: milestone.celebrationMessage
          }
        };

        addEngagementEvent(event);
      });
    } catch (error) {
      console.error('Error checking for new achievements:', error);
    }
  }, [totalPoints, level, achievements]);

  const addEngagementEvent = (event: EngagementEvent) => {
    setEngagementEvents(prev => [event, ...prev.slice(0, 99)]); // Keep last 100 events
  };

  // Get engagement statistics
  const getEngagementStats = (): EngagementStats => {
    const now = Date.now();
    const startOfToday = getStartOfToday();

    const weekAgo = new Date(now - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(now - 30 * 24 * 60 * 60 * 1000);

    const todayEvents = engagementEvents.filter(event =>
      event.timestamp >= startOfToday
    );

    const weeklyEvents = engagementEvents.filter(event =>
      event.timestamp >= weekAgo.getTime()
    );

    const monthlyEvents = engagementEvents.filter(event =>
      event.timestamp >= monthAgo.getTime()
    );

    const totalPoints = engagementEvents.reduce((sum: any, event: any) => sum + event.points + event.bonusPoints, 0);
    const averagePointsPerDay = todayEvents.length > 0 ? totalPoints / todayEvents.length : 0;

    // Calculate engagement score (0-100)
    let engagementScore = 0;

    // Base score from daily activity
    if (todayEvents.length > 0) engagementScore += 30;
    if (weeklyEvents.length >= 5) engagementScore += 25;
    if (monthlyEvents.length >= 20) engagementScore += 25;

    // Bonus for streaks and milestones
    // TODO: Implement streak-based engagement scoring
    // if (currentStreak >= 7) engagementScore += 10;
    // if (currentStreak >= 14) engagementScore += 10;

    // Determine motivation level
    let motivationLevel: 'low' | 'medium' | 'high' | 'excellent' = 'low';
    if (engagementScore >= 80) motivationLevel = 'excellent';
    else if (engagementScore >= 60) motivationLevel = 'high';
    else if (engagementScore >= 40) motivationLevel = 'medium';

    return {
      totalEvents: engagementEvents.length,
      todayEvents: todayEvents.length,
      weeklyEvents: weeklyEvents.length,
      monthlyEvents: weeklyEvents.length,
      averagePointsPerDay,
      engagementScore: Math.min(engagementScore, 100),
      motivationLevel
    };
  };

  // Get personalized recommendations
  const getRecommendations = () => {
    const stats = getEngagementStats();
    const nextMilestone = getNextMilestone({
      totalPoints: totalPoints || 0,
      currentStreak: 0, // Simplified - can be enhanced later
      longestStreak: 0, // Simplified - can be enhanced later
      completedModules: 0, // Simplified - can be enhanced later
      completedActivities: 0, // Simplified - can be enhanced later
      totalModules: 12, // Static for now
      totalActivities: 48 // Static for now
    });

    const recommendations = [];

    // Low engagement recommendations
    if (stats.engagementScore < 40) {
      recommendations.push({
        type: 'motivation',
        title: 'Start Small',
        description: 'Try completing just one activity today to build momentum',
        priority: 'high'
      });
    }

    // Streak recommendations
    // TODO: Implement streak-based recommendations
    // if (currentStreak > 0 && currentStreak < 7) {
    //   recommendations.push({
    //     type: 'streak',
    //     title: 'Maintain Your Streak',
    //     description: `You're on a ${currentStreak}-day streak! Keep it going!`,
    //     priority: 'high'
    //   });
    // }

    // Milestone recommendations
    if (nextMilestone) {
      const progress = getProgressTowardsNext({
        totalPoints: totalPoints,
        currentStreak: 0, // TODO: Implement streak tracking in Supabase
        longestStreak: 0, // TODO: Implement streak tracking in Supabase
        completedModules: 0, // TODO: Implement module completion tracking
        completedActivities: 0, // TODO: Implement activity completion tracking
        totalModules: 12, // Fixed number of modules
        totalActivities: 48 // Fixed number of activities (12 modules * 4 activities each)
      });

      recommendations.push({
        type: 'milestone',
        title: `Unlock ${nextMilestone.title}`,
        description: `You're ${progress.remaining} points away from your next milestone!`,
        priority: 'medium'
      });
    }

    // Daily challenge recommendations
    const todaysChallenges = getTodaysChallenges();
    if (todaysChallenges.length > 0) {
      recommendations.push({
        type: 'challenge',
        title: 'Daily Challenge Available',
        description: `Complete today's challenge for bonus points!`,
        priority: 'medium'
      });
    }

    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority as keyof typeof priorityOrder] - priorityOrder[a.priority as keyof typeof priorityOrder];
    });
  };

  // Get celebration data for achievements
  const getCelebrationData = (event: EngagementEvent) => {
    if (event.celebrationData) {
      return event.celebrationData;
    }

    // Default celebration data based on event type
    switch (event.type) {
      case 'activity_completed':
        return {
          icon: '🎯',
          color: [colors.success, colors.secondary],
          message: 'Great job completing this activity!'
        };
      case 'module_completed':
        return {
          icon: '🏆',
          color: [colors.warning, colors.accentPink],
          message: 'Module completed! You\'re making amazing progress!'
        };
      case 'challenge_completed':
        return {
          icon: '🔥',
          color: [colors.deepCoral, colors.primaryDark],
          message: 'Daily challenge completed! You\'re on fire!'
        };
      case 'streak_achieved':
        return {
          icon: '🔥',
          color: [colors.deepCoral, colors.primaryDark],
          message: 'Streak milestone reached! Keep the momentum going!'
        };
      default:
        return {
          icon: '🎉',
          color: [colors.deepCoral, colors.primaryLight],
          message: 'Congratulations on your achievement!'
        };
    }
  };

  // Reset all engagement data (for testing)
  const resetEngagementData = async () => {
    setEngagementEvents([]);
    // Note: This would also need to reset points, challenges, and milestones
    // through their respective hooks
  };

  return {
    engagementEvents,
    engagementStats: getEngagementStats(),
    recommendations: getRecommendations(),
    isLoading,
    getCelebrationData,
    resetEngagementData,
    // Re-export key functions from other hooks
    totalPoints,
    level,
    achievements,
    dailyChallenges,
    challengeStreak,
    milestones: getUnlockedMilestones(),
    nextMilestone: getNextMilestone({
      totalPoints: totalPoints,
      currentStreak: 0, // TODO: Implement streak tracking in Supabase
      longestStreak: 0, // TODO: Implement streak tracking in Supabase
      completedModules: 0, // TODO: Implement module completion tracking
      completedActivities: 0, // TODO: Implement activity completion tracking
      totalModules: 12, // Fixed number of modules
      totalActivities: 48 // Fixed number of activities (12 modules * 4 activities each)
    })
  };
};
