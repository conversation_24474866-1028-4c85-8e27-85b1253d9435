import { useState, useEffect, useCallback } from 'react';
import { pointsSystemService, Achievement, PointsSystemData } from '../../shared/services/features/pointsSystemService';
import { useAuth } from '../onboarding/useAuth';
import { logger } from '../../shared/utils/logger';

export interface PointsSystemState {
  data: PointsSystemData | null;
  isLoading: boolean;
  error: string | null;
}

export const usePointsSystemSupabase = () => {
  const [state, setState] = useState<PointsSystemState>({
    data: null,
    isLoading: true,
    error: null,
  });

  const { user } = useAuth();

  // Load user's points data on mount
  useEffect(() => {
    if (user) {
      loadUserPoints();
    } else {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [user]);

  const loadUserPoints = async () => {
    if (!user) return;

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const data = await pointsSystemService.getUserPoints(user.id);
      setState({
        data,
        isLoading: false,
        error: null,
      });

      logger.info('User points loaded successfully');
    } catch (error) {
      logger.error('Error loading user points:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to load points data',
      }));
    }
  };

  const addPoints = useCallback(async (points: number, activityType?: string): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot add points');
      return false;
    }

    try {
      const success = await pointsSystemService.addPoints(user.id, points, activityType);
      
      if (success) {
        // Reload data to get updated points
        await loadUserPoints();
      }
      
      return success;
    } catch (error) {
      logger.error('Error adding points:', error);
      return false;
    }
  }, [user]);

  const addAchievement = useCallback(async (achievement: Achievement): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot add achievement');
      return false;
    }

    try {
      const success = await pointsSystemService.addAchievement(user.id, achievement);
      
      if (success) {
        // Reload data to get updated achievements
        await loadUserPoints();
      }
      
      return success;
    } catch (error) {
      logger.error('Error adding achievement:', error);
      return false;
    }
  }, [user]);

  const getUserAchievements = useCallback(async (): Promise<Achievement[]> => {
    if (!user) {
      logger.warn('No user logged in, cannot get achievements');
      return [];
    }

    try {
      return await pointsSystemService.getUserAchievements(user.id);
    } catch (error) {
      logger.error('Error getting user achievements:', error);
      return [];
    }
  }, [user]);

  const getUserLevel = useCallback(async () => {
    if (!user) {
      logger.warn('No user logged in, cannot get level');
      return null;
    }

    try {
      return await pointsSystemService.getUserLevel(user.id);
    } catch (error) {
      logger.error('Error getting user level:', error);
      return null;
    }
  }, [user]);

  const getLeaderboard = useCallback(async (limit: number = 10) => {
    try {
      return await pointsSystemService.getLeaderboard(limit);
    } catch (error) {
      logger.error('Error getting leaderboard:', error);
      return [];
    }
  }, []);

  const resetUserPoints = useCallback(async (): Promise<boolean> => {
    if (!user) {
      logger.warn('No user logged in, cannot reset points');
      return false;
    }

    try {
      const success = await pointsSystemService.resetUserPoints(user.id);
      
      if (success) {
        // Reload data to get reset points
        await loadUserPoints();
      }
      
      return success;
    } catch (error) {
      logger.error('Error resetting user points:', error);
      return false;
    }
  }, [user]);

  // Computed values
  const totalPoints = state.data?.totalPoints || 0;
  const level = state.data?.level || 1;
  const achievements = state.data?.achievements || [];
  const lastActivity = state.data?.lastActivity;

  const pointsToNextLevel = useCallback(() => {
    const currentLevelPoints = (level - 1) * 100;
    const nextLevelPoints = level * 100;
    return Math.max(0, nextLevelPoints - totalPoints);
  }, [level, totalPoints]);

  const levelProgress = useCallback(() => {
    const currentLevelPoints = (level - 1) * 100;
    const nextLevelPoints = level * 100;
    const progress = (totalPoints - currentLevelPoints) / (nextLevelPoints - currentLevelPoints);
    return Math.min(1, Math.max(0, progress));
  }, [level, totalPoints]);

  const hasAchievement = useCallback((achievementId: string): boolean => {
    return achievements.some(achievement => achievement.id === achievementId);
  }, [achievements]);

  const getAchievementsByType = useCallback((type: Achievement['type']): Achievement[] => {
    return achievements.filter(achievement => achievement.type === type);
  }, [achievements]);

  const refreshData = useCallback(() => {
    loadUserPoints();
  }, []);

  return {
    // State
    ...state,
    
    // Computed values
    totalPoints,
    level,
    achievements,
    lastActivity,
    pointsToNextLevel: pointsToNextLevel(),
    levelProgress: levelProgress(),
    
    // Actions
    addPoints,
    addAchievement,
    getUserAchievements,
    getUserLevel,
    getLeaderboard,
    resetUserPoints,
    refreshData,
    
    // Helper functions
    hasAchievement,
    getAchievementsByType,
  };
};
