/**
 * Performance Optimization Service
 * Enterprise-grade performance optimization for 4M+ users
 */

import { logger } from '../../shared/utils/logger';
import { supabase } from '../services/supabase/client';
import { validateUUID } from '../utils/validation';

// Performance monitoring interfaces
export interface QueryMetrics {
  queryId: string;
  duration: number;
  timestamp: number;
  userId?: string;
  queryType: string;
  resultCount: number;
  cacheHit: boolean;
}

export interface PerformanceMetrics {
  averageQueryTime: number;
  slowQueries: QueryMetrics[];
  cacheHitRate: number;
  totalQueries: number;
  memoryUsage: number;
  activeConnections: number;
}

// Batch operation interfaces
export interface BatchOperation<T> {
  operation: 'insert' | 'update' | 'delete';
  table: string;
  data: T[];
  batchSize?: number;
}

export interface OptimizedQuery {
  query: string;
  params: unknown[];
  cacheKey?: string;
  cacheTTL?: number;
}

// Enhanced error handling interfaces
export interface PerformanceError {
  code: string;
  message: string;
  context?: Record<string, unknown>;
  timestamp: number;
}

export interface BatchOperationResult {
  success: boolean;
  processedCount: number;
  errors: PerformanceError[];
  duration: number;
}

class PerformanceOptimizationService {
  private static instance: PerformanceOptimizationService;
  private queryMetrics: QueryMetrics[] = [];
  private performanceThresholds = {
    slowQueryMs: 1000,
    maxCacheSize: 1000,
    batchSize: 100,
    connectionPoolSize: 20
  };

  private constructor() {}

  public static getInstance(): PerformanceOptimizationService {
    if (!PerformanceOptimizationService.instance) {
      PerformanceOptimizationService.instance = new PerformanceOptimizationService();
    }
    return PerformanceOptimizationService.instance;
  }

  /**
   * Execute optimized batch operations to prevent N+1 queries
   */
  async executeBatchOperation<T extends Record<string, unknown>>(operation: BatchOperation<T>): Promise<BatchOperationResult> {
    const startTime = Date.now();
    const queryId = `batch_${operation.operation}_${operation.table}_${Date.now()}`;

    // Input validation
    if (!operation.data || operation.data.length === 0) {
      logger.warn('No data provided for batch operation');
      return {
        success: false,
        processedCount: 0,
        errors: [{
          code: 'INVALID_INPUT',
          message: 'No data provided for batch operation',
          context: { operation: operation.operation, table: operation.table },
          timestamp: Date.now()
        }],
        duration: Date.now() - startTime
      };
    }

    if (!operation.table || typeof operation.table !== 'string') {
      logger.error('Invalid table name provided');
      return {
        success: false,
        processedCount: 0,
        errors: [{
          code: 'INVALID_TABLE',
          message: 'Invalid table name provided',
          context: { table: operation.table },
          timestamp: Date.now()
        }],
        duration: Date.now() - startTime
      };
    }

    try {
      const batchSize = operation.batchSize || this.performanceThresholds.batchSize;
      const batches = this.chunkArray(operation.data, batchSize);

      logger.info(`Executing batch ${operation.operation} on ${operation.table}: ${operation.data.length} items in ${batches.length} batches`);

      await Promise.all(
        batches.map(async (batch, index) => {
          try {
            switch (operation.operation) {
              case 'insert':
                const insertResult = await (supabase as any).from(operation.table).insert(batch);
                if (insertResult.error) {
                  throw new Error(`Insert failed: ${insertResult.error.message}`);
                }
                return insertResult;
              case 'update':
                // For updates, we need to handle each item individually but in batches
                const updateResults = await Promise.all(
                  batch.map(async item => {
                    if (!item.id) {
                      throw new Error('Update operation requires items to have an id field');
                    }
                    const result = await (supabase as any).from(operation.table).update(item).eq('id', item.id);
                    if (result.error) {
                      throw new Error(`Update failed for item ${item.id}: ${result.error.message}`);
                    }
                    return result;
                  })
                );
                return updateResults;
              case 'delete':
                const ids = batch.map(item => {
                  if (!item.id) {
                    throw new Error('Delete operation requires items to have an id field');
                  }
                  return item.id;
                });
                const deleteResult = await (supabase as any).from(operation.table).delete().in('id', ids);
                if (deleteResult.error) {
                  throw new Error(`Delete failed: ${deleteResult.error.message}`);
                }
                return deleteResult;
              default:
                throw new Error(`Unsupported batch operation: ${operation.operation}`);
            }
          } catch (error) {
            logger.error(`Batch ${index + 1} failed:`, error);
            throw error;
          }
        })
      );

      const duration = Date.now() - startTime;
      this.recordQueryMetrics({
        queryId,
        duration,
        timestamp: Date.now(),
        queryType: `batch_${operation.operation}`,
        resultCount: operation.data.length,
        cacheHit: false
      });

      logger.info(`Batch operation completed in ${duration}ms`);
      return {
        success: true,
        processedCount: operation.data.length,
        errors: [],
        duration
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Batch operation failed after ${duration}ms:`, error);

      this.recordQueryMetrics({
        queryId,
        duration,
        timestamp: Date.now(),
        queryType: `batch_${operation.operation}_error`,
        resultCount: 0,
        cacheHit: false
      });

      return {
        success: false,
        processedCount: 0,
        errors: [{
          code: 'BATCH_OPERATION_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          context: { operation: operation.operation, table: operation.table },
          timestamp: Date.now()
        }],
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Optimized bulk data fetching with joins to prevent N+1
   */
  async fetchRelatedData(
    mainTable: string,
    mainIds: string[],
    relations: Array<{
      table: string;
      foreignKey: string;
      select?: string;
      limit?: number;
    }>
  ): Promise<any> {
    const startTime = Date.now();
    const queryId = `bulk_fetch_${mainTable}_${Date.now()}`;

    try {
      // Validate all IDs
      const validIds = mainIds.filter(id => validateUUID(id).isValid);
      if (validIds.length === 0) {
        throw new Error('No valid IDs provided');
      }

      // Build optimized queries with joins
      const queries = relations.map(relation => {
        let query = (supabase as any)
          .from(relation.table)
          .select(relation.select || '*')
          .in(relation.foreignKey, validIds);

        if (relation.limit) {
          query = query.limit(relation.limit);
        }

        return query;
      });

      // Execute all queries in parallel
      const results = await Promise.all(queries);

      // Group results by foreign key for easy lookup
      const groupedResults = results.map((result, index) => {
        const relation = relations[index];
        const grouped = (result.data || []).reduce((acc: any, item: any) => {
          const key = item[relation.foreignKey];
          if (!acc[key]) acc[key] = [];
          acc[key].push(item);
          return acc;
        }, {});

        return { relation: relation.table, data: grouped };
      });

      const duration = Date.now() - startTime;
      this.recordQueryMetrics({
        queryId,
        duration,
        timestamp: Date.now(),
        queryType: 'bulk_fetch_related',
        resultCount: results.reduce((sum, r) => sum + (r.data?.length || 0), 0),
        cacheHit: false
      });

      return groupedResults;

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Bulk fetch failed after ${duration}ms:`, error);

      this.recordQueryMetrics({
        queryId,
        duration,
        timestamp: Date.now(),
        queryType: 'bulk_fetch_error',
        resultCount: 0,
        cacheHit: false
      });

      throw error;
    }
  }

  /**
   * Optimized pagination with cursor-based approach for large datasets
   */
  async paginateOptimized(
    table: string,
    options: {
      select?: string;
      filters?: Record<string, any>;
      orderBy?: { column: string; ascending?: boolean };
      cursor?: string;
      limit?: number;
    } = {}
  ): Promise<{ data: any[]; nextCursor?: string; hasMore: boolean }> {
    const startTime = Date.now();
    const queryId = `paginate_${table}_${Date.now()}`;

    try {
      const {
        select = '*',
        filters = {},
        orderBy = { column: 'created_at', ascending: false },
        cursor,
        limit = 50
      } = options;

      let query = (supabase as any)
        .from(table)
        .select(select)
        .order(orderBy.column, { ascending: orderBy.ascending })
        .limit(limit + 1); // Fetch one extra to check if there are more

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });

      // Apply cursor for pagination
      if (cursor) {
        const operator = orderBy.ascending ? 'gt' : 'lt';
        query = query[operator](orderBy.column, cursor);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      const hasMore = data.length > limit;
      const results = hasMore ? data.slice(0, limit) : data;
      const nextCursor = hasMore && results.length > 0
        ? results[results.length - 1][orderBy.column]
        : undefined;

      const duration = Date.now() - startTime;
      this.recordQueryMetrics({
        queryId,
        duration,
        timestamp: Date.now(),
        queryType: 'paginate_optimized',
        resultCount: results.length,
        cacheHit: false
      });

      return {
        data: results,
        nextCursor,
        hasMore
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error(`Optimized pagination failed after ${duration}ms:`, error);

      this.recordQueryMetrics({
        queryId,
        duration,
        timestamp: Date.now(),
        queryType: 'paginate_error',
        resultCount: 0,
        cacheHit: false
      });

      throw error;
    }
  }

  /**
   * Record query performance metrics
   */
  private recordQueryMetrics(metrics: QueryMetrics): void {
    this.queryMetrics.push(metrics);

    // Keep only last 10000 metrics to prevent memory issues
    if (this.queryMetrics.length > 10000) {
      this.queryMetrics = this.queryMetrics.slice(-10000);
    }

    // Log slow queries
    if (metrics.duration > this.performanceThresholds.slowQueryMs) {
      logger.warn(`Slow query detected: ${metrics.queryId} took ${metrics.duration}ms`);
    }
  }

  /**
   * Get performance metrics for monitoring
   */
  getPerformanceMetrics(): PerformanceMetrics {
    const recentMetrics = this.queryMetrics.filter(
      m => Date.now() - m.timestamp < 3600000 // Last hour
    );

    const totalQueries = recentMetrics.length;
    const averageQueryTime = totalQueries > 0
      ? recentMetrics.reduce((sum, m) => sum + m.duration, 0) / totalQueries
      : 0;

    const slowQueries = recentMetrics.filter(
      m => m.duration > this.performanceThresholds.slowQueryMs
    );

    const cacheHits = recentMetrics.filter(m => m.cacheHit).length;
    const cacheHitRate = totalQueries > 0 ? (cacheHits / totalQueries) * 100 : 0;

    return {
      averageQueryTime,
      slowQueries,
      cacheHitRate,
      totalQueries,
      memoryUsage: this.getMemoryUsage(),
      activeConnections: 0 // Would need to implement connection tracking
    };
  }

  /**
   * Utility function to chunk arrays for batch processing
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Get current memory usage (simplified)
   */
  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed / 1024 / 1024; // MB
    }
    return 0;
  }

  /**
   * Execute a batch operation with performance optimization
   */
  async executeBatchOperation<T>(operation: BatchOperation<T>): Promise<BatchOperationResult> {
    const startTime = Date.now();

    try {
      logger.info(`Executing batch operation: ${operation.operation} on ${operation.table}`, {
        dataCount: operation.data.length,
        batchSize: operation.batchSize || 100
      });

      const batchSize = operation.batchSize || 100;
      const batches = [];

      // Split data into batches
      for (let i = 0; i < operation.data.length; i += batchSize) {
        batches.push(operation.data.slice(i, i + batchSize));
      }

      let processedCount = 0;
      const errors: PerformanceError[] = [];

      // Process each batch
      for (const batch of batches) {
        try {
          let query = (supabase as any).from(operation.table);

          switch (operation.operation) {
            case 'insert':
              await query.insert(batch);
              break;
            case 'update':
              // For updates, assume each item has an id field
              for (const item of batch) {
                await query.update(item).eq('id', (item as any).id);
              }
              break;
            case 'delete':
              // For deletes, assume data contains ids
              const ids = batch.map((item: any) => item.id || item);
              await query.delete().in('id', ids);
              break;
          }

          processedCount += batch.length;
        } catch (batchError) {
          errors.push({
            code: 'BATCH_ERROR',
            message: batchError instanceof Error ? batchError.message : 'Batch operation failed',
            context: { operation: operation.operation, batchSize: batch.length },
            timestamp: Date.now()
          });
        }
      }

      const duration = Date.now() - startTime;

      return {
        success: errors.length === 0,
        processedCount,
        errors,
        duration
      };
    } catch (error) {
      return {
        success: false,
        processedCount: 0,
        errors: [{
          code: 'BATCH_OPERATION_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          context: { operation: operation.operation, table: operation.table },
          timestamp: Date.now()
        }],
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Clear old metrics to free memory
   */
  clearOldMetrics(): void {
    const cutoff = Date.now() - 86400000; // 24 hours ago
    this.queryMetrics = this.queryMetrics.filter(m => m.timestamp > cutoff);
    logger.info(`Cleared old metrics, ${this.queryMetrics.length} metrics remaining`);
  }
}

export const performanceOptimizationService = PerformanceOptimizationService.getInstance();
export default performanceOptimizationService;
