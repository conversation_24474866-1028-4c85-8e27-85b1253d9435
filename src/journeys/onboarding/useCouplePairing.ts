/**
 * Couple Pairing Hook
 *
 * React hook for managing couple pairing functionality including
 * creating couples, joining couples, and managing pairing state.
 *
 * <AUTHOR> Us Team
 * @version 1.0.0
 */

import { useCallback, useEffect, useState } from 'react';
import { logger } from '../../shared/utils/logger';
import { Couple, couplePairingService, PairingStatus } from '../services/couplePairingService';
import { supabase } from '../types/supabase.types';
import { useAuth } from './useAuth';

export interface CouplePairingState {
  // Current couple data
  couple: Couple | null;
  isLoading: boolean;
  error: string | null;

  // Pairing status
  pairingStatus: PairingStatus | null;

  // UI state
  isCreatingCouple: boolean;
  isJoiningCouple: boolean;
  showInviteModal: boolean;
  showJoinModal: boolean;

  // Form state
  joinCode: string;
  invitationText: string;
}

export interface CouplePairingActions {
  // Core actions
  createCouple: () => Promise<boolean>;
  joinCouple: (code: string) => Promise<boolean>;
  cancelCouple: () => Promise<boolean>;
  refreshCouple: () => Promise<void>;

  // UI actions
  openInviteModal: () => void;
  closeInviteModal: () => void;
  openJoinModal: () => void;
  closeJoinModal: () => void;

  // Form actions
  setJoinCode: (code: string) => void;
  copyInvitationText: () => Promise<boolean>;
  shareInvitation: () => Promise<void>;

  // Status checks
  checkPairingStatus: () => Promise<void>;
  canCreateCouple: () => boolean;
  canJoinCouple: () => boolean;
  isPartnerConnected: () => boolean;
}

const initialState: CouplePairingState = {
  couple: null,
  isLoading: false,
  error: null,
  pairingStatus: null,
  isCreatingCouple: false,
  isJoiningCouple: false,
  showInviteModal: false,
  showJoinModal: false,
  joinCode: '',
  invitationText: ''
};

export function useCouplePairing(): CouplePairingState & CouplePairingActions {
  const [state, setState] = useState<CouplePairingState>(initialState);
  const { user, isAuthenticated } = useAuth();

  // Load couple data on mount and auth changes
  useEffect(() => {
    if (isAuthenticated && user) {
      refreshCouple();
      checkPairingStatus();
    } else {
      setState(prev => ({ ...prev, couple: null, pairingStatus: null }));
    }
  }, [isAuthenticated, user]);

  // Update invitation text when couple changes
  useEffect(() => {
    if (state.couple) {
      const invitationText = couplePairingService.generateInvitationText(state.couple);
      setState(prev => ({ ...prev, invitationText }));
    }
  }, [state.couple]);

  /**
   * Refresh couple data from server
   */
  const refreshCouple = useCallback(async () => {
    if (!user) return;

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const couple = await couplePairingService.getUserCouple(user.id);
      setState(prev => ({
        ...prev,
        couple,
        isLoading: false
      }));
    } catch (error) {
      logger.error('Error refreshing couple:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to load couple data',
        isLoading: false
      }));
    }

	  // Realtime sync: refresh couple on changes to couples/couple_stories for this user
	  useEffect(() => {
	    if (!user?.id) return;
	    const channel = supabase
	      .channel('couple-sync')
	      .on('postgres_changes', { event: '*', schema: 'public', table: 'couples', filter: `partner1_user_id=eq.${user.id}` }, () => refreshCouple())
	      .on('postgres_changes', { event: '*', schema: 'public', table: 'couples', filter: `partner2_user_id=eq.${user.id}` }, () => refreshCouple())
	      .on('postgres_changes', { event: '*', schema: 'public', table: 'couple_stories' }, () => refreshCouple())
	      .subscribe();
	    return () => {
	      supabase.removeChannel(channel);
	    };
	  }, [user?.id, refreshCouple]);

  }, [user]);

  /**
   * Create a new couple
   */
  const createCouple = useCallback(async (): Promise<boolean> => {
    if (!user) return false;

    setState(prev => ({ ...prev, isCreatingCouple: true, error: null }));

    try {
      const result = await couplePairingService.createCouple(user.id);

      if (result.success && result.couple) {
        setState(prev => ({
          ...prev,
          couple: result.couple!,
          isCreatingCouple: false,
          showInviteModal: true
        }));
        return true;
      } else {
        setState(prev => ({
          ...prev,
          error: result.error || 'Failed to create couple',
          isCreatingCouple: false
        }));
        return false;
      }
    } catch (error) {
      logger.error('Error creating couple:', error);
      setState(prev => ({
        ...prev,
        error: 'An unexpected error occurred',
        isCreatingCouple: false
      }));
      return false;
    }
  }, [user]);

  /**
   * Join an existing couple
   */
  const joinCouple = useCallback(async (code: string): Promise<boolean> => {
    if (!user) return false;

    setState(prev => ({ ...prev, isJoiningCouple: true, error: null }));

    try {
      const result = await couplePairingService.joinCouple(user.id, code);

      if (result.success && result.couple) {
        setState(prev => ({
          ...prev,
          couple: result.couple!,
          isJoiningCouple: false,
          showJoinModal: false,
          joinCode: ''
        }));
        return true;
      } else {
        const errorMessage = result.attempts_remaining !== undefined
          ? `${result.error} (${result.attempts_remaining} attempts remaining)`
          : result.error || 'Failed to join couple';

        setState(prev => ({
          ...prev,
          error: errorMessage,
          isJoiningCouple: false
        }));

        // Update pairing status after failed attempt
        await checkPairingStatus();
        return false;
      }
    } catch (error) {
      logger.error('Error joining couple:', error);
      setState(prev => ({
        ...prev,
        error: 'An unexpected error occurred',
        isJoiningCouple: false
      }));
      return false;
    }
  }, [user]);

  /**
   * Cancel current couple invitation
   */
  const cancelCouple = useCallback(async (): Promise<boolean> => {
    if (!user || !state.couple) return false;

    try {
      const success = await couplePairingService.cancelCouple(state.couple.id, user.id);

      if (success) {
        setState(prev => ({
          ...prev,
          couple: null,
          showInviteModal: false
        }));
        return true;
      } else {
        setState(prev => ({
          ...prev,
          error: 'Failed to cancel couple invitation'
        }));
        return false;
      }
    } catch (error) {
      logger.error('Error cancelling couple:', error);
      setState(prev => ({
        ...prev,
        error: 'An unexpected error occurred'
      }));
      return false;
    }
  }, [user, state.couple]);

  /**
   * Check pairing attempt status
   */
  const checkPairingStatus = useCallback(async () => {
    if (!user) return;

    try {
      const pairingStatus = await couplePairingService.checkPairingStatus(user.id);
      setState(prev => ({ ...prev, pairingStatus }));
    } catch (error) {
      logger.error('Error checking pairing status:', error);
    }
  }, [user]);

  /**
   * UI action handlers
   */
  const openInviteModal = useCallback(() => {
    setState(prev => ({ ...prev, showInviteModal: true }));
  }, []);

  const closeInviteModal = useCallback(() => {
    setState(prev => ({ ...prev, showInviteModal: false }));
  }, []);

  const openJoinModal = useCallback(() => {
    setState(prev => ({ ...prev, showJoinModal: true, error: null }));
  }, []);

  const closeJoinModal = useCallback(() => {
    setState(prev => ({ ...prev, showJoinModal: false, joinCode: '', error: null }));
  }, []);

  const setJoinCode = useCallback((code: string) => {
    setState(prev => ({ ...prev, joinCode: code.toUpperCase() }));
  }, []);

  /**
   * Copy invitation text to clipboard
   */
  const copyInvitationText = useCallback(async (): Promise<boolean> => {
    try {
      if (typeof navigator !== 'undefined' && navigator.clipboard) {
        await navigator.clipboard.writeText(state.invitationText);
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Error copying to clipboard:', error);
      return false;
    }
  }, [state.invitationText]);

  /**
   * Share invitation using native sharing
   */
  const shareInvitation = useCallback(async () => {
    try {
      if (typeof navigator !== 'undefined' && navigator.share) {
        await navigator.share({
          title: 'Join me on Everlasting Us!',
          text: state.invitationText,
          url: state.couple?.qr_code_data?.app_url
        });
      }
    } catch (error) {
      logger.error('Error sharing invitation:', error);
    }
  }, [state.invitationText, state.couple]);

  /**
   * Status check helpers
   */
  const canCreateCouple = useCallback((): boolean => {
    return !state.couple || state.couple.status === 'expired' || state.couple.status === 'cancelled';
  }, [state.couple]);

  const canJoinCouple = useCallback((): boolean => {
    return state.pairingStatus?.can_attempt ?? false;
  }, [state.pairingStatus]);

  const isPartnerConnected = useCallback((): boolean => {
    return state.couple?.status === 'active' && !!state.couple.partner2_user_id;
  }, [state.couple]);

  return {
    // State
    ...state,

    // Actions
    createCouple,
    joinCouple,
    cancelCouple,
    refreshCouple,
    openInviteModal,
    closeInviteModal,
    openJoinModal,
    closeJoinModal,
    setJoinCode,
    copyInvitationText,
    shareInvitation,
    checkPairingStatus,
    canCreateCouple,
    canJoinCouple,
    isPartnerConnected
  };
}
