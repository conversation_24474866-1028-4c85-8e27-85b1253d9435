import { Alert } from 'react-native';
import { router } from 'expo-router';

/**
 * Prompts user to authenticate when they try to save personal data
 * @param action - The action they're trying to perform (e.g., "save photos", "create story")
 * @param onAuthenticated - Callback to execute after successful authentication
 */
export function promptForAuthentication(
  action: string,
  onAuthenticated?: () => void
) {
  Alert.alert(
    'Sign Up to Save Your Data',
    `To ${action}, please create an account to keep your data safe and synced across devices.`,
    [
      {
        text: 'Continue Without Saving',
        style: 'cancel',
        onPress: () => {
          // User chose to continue without saving
          console.log(`User chose to continue without saving for action: ${action}`);
        }
      },
      {
        text: 'Sign Up',
        style: 'default',
        onPress: () => {
          // Navigate to auth screen
          router.push('/auth');
        }
      }
    ]
  );
}

/**
 * Checks if user is authenticated and prompts if not
 * @param action - The action they're trying to perform
 * @param isAuthenticated - Whether the user is currently authenticated
 * @param onAuthenticated - Callback to execute if already authenticated
 * @returns true if authenticated, false if not
 */
export function requireAuthentication(
  action: string,
  isAuthenticated: boolean,
  onAuthenticated?: () => void
): boolean {
  if (isAuthenticated) {
    onAuthenticated?.();
    return true;
  } else {
    promptForAuthentication(action, onAuthenticated);
    return false;
  }
}
