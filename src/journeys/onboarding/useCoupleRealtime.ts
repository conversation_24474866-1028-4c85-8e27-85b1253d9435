import { useEffect } from 'react';
import { supabase } from '../types/supabase';
import { logger } from '../../shared/utils/logger';

// Subscribes to realtime changes for a given couple_id across key tables
// and invokes the provided callback when any change occurs.
export function useCoupleRealtime(coupleId: string | null | undefined, onChange: () => void) {
  useEffect(() => {
    if (!coupleId) return;

    const channel = supabase
      .channel(`couple-${coupleId}`)
      .on('postgres_changes', { event: '*', schema: 'public', table: 'timeline_events', filter: `couple_id=eq.${coupleId}` }, () => {
        logger.debug('Realtime: timeline_events changed');
        onChange?.();
      })
      .on('postgres_changes', { event: '*', schema: 'public', table: 'timeline_photos', filter: `couple_id=eq.${coupleId}` }, () => {
        logger.debug('Realtime: timeline_photos changed');
        onChange?.();
      })
      .on('postgres_changes', { event: '*', schema: 'public', table: 'couple_stories', filter: `couple_id=eq.${coupleId}` }, () => {
        logger.debug('Realtime: couple_stories changed');
        onChange?.();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [coupleId, onChange]);
}

