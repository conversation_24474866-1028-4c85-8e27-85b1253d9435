import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { validateName } from '../utils/validation';
import { logger } from '../../shared/utils/logger';
import { auth } from '../types/auth';
import { useAuth } from './useAuth';
import type { UserProfile } from '../types/auth';
import { supabase } from '../services/supabase/client';

const USER_PROFILE_STORAGE_KEY = 'user_profile';

const DEFAULT_PROFILE: UserProfile = {
  partner1: {
    name: '',
    icon: '',
  },
  partner2: {
    name: '',
    icon: '',
  },
  isComplete: false,
  createdAt: Date.now(),
  updatedAt: Date.now(),
};

export const useUserProfile = () => {
  const { user } = useAuth();
  const [profile, setProfile] = useState<UserProfile>(DEFAULT_PROFILE);
  const [isLoading, setIsLoading] = useState(true);

  // Load profile when user changes or on mount
  useEffect(() => {
    loadProfile();
  }, [user?.id]);

  // Realtime subscription to profile changes for the current user
  useEffect(() => {
    if (!user?.id) return;

    const channel = supabase
      .channel('profiles-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'profiles', filter: `id=eq.${user.id}` },
        (payload: any) => {
          try {
            const data: any = payload.new;
            if (!data) return;
            const updated: UserProfile = {
              partner1: { name: data.partner1_name || '', icon: data.partner1_icon || '' },
              partner2: { name: data.partner2_name || '', icon: data.partner2_icon || '' },
              partner1PhotoUrl: data.partner1_profile_picture || null,
              partner2PhotoUrl: data.partner2_profile_picture || null,
              relationshipStartDate: (data as any).relationship_start_date || null,
              isComplete: !!data.is_complete,
              createdAt: new Date(data.created_at).getTime(),
              updatedAt: new Date(data.updated_at).getTime(),
            };
            setProfile(updated);
            // Keep local cache in sync
            secureStorage.setItem(USER_PROFILE_STORAGE_KEY, updated).catch(() => {});
          } catch (e) {
            logger.warn('Failed to process realtime profile update', e);
          }
        }
      )
      .subscribe();

    return () => {
      try { supabase.removeChannel(channel); } catch {}
    };
  }, [user?.id]);

  const loadProfile = async () => {
    try {
      logger.info('Loading profile...');

      // Try to load from Supabase first if user is authenticated
      if (user?.id) {
        try {
          const supabaseProfile = await auth.getUserProfile(user.id);
          if (supabaseProfile) {
            setProfile(supabaseProfile);
            // Also save to local storage for offline access
            await secureStorage.setItem(USER_PROFILE_STORAGE_KEY, supabaseProfile);
            logger.info('Profile loaded from Supabase');
            return;
          }
        } catch (supabaseError) {
          logger.warn('Failed to load from Supabase, falling back to local storage:', supabaseError);
        }
      }

      // Fallback to local storage
      const storedProfile = await secureStorage.getItem<UserProfile>(USER_PROFILE_STORAGE_KEY);

      if (storedProfile) {
        // Validate the stored profile structure
        if (storedProfile.partner1 && storedProfile.partner2) {
          setProfile(storedProfile);
          logger.info('Profile loaded from local storage');
        } else {
          logger.warn('Invalid profile structure found, using default');
          setProfile(DEFAULT_PROFILE);
        }
      } else {
        logger.info('No stored profile found, using default');
        setProfile(DEFAULT_PROFILE);
      }
    } catch (error) {
      logger.error('Error loading user profile:', error);
      // Set default profile on error to prevent app crashes
      setProfile(DEFAULT_PROFILE);
    } finally {
      setIsLoading(false);
    }
  };

  const saveProfile = async (newProfile: UserProfile) => {
    try {
      // Validate profile data before saving
      if (!newProfile.partner1 || !newProfile.partner2) {
        throw new Error('Invalid profile data: missing partner information');
      }

      const profileToSave: UserProfile = {
        ...newProfile,
        isComplete: checkIfComplete(newProfile),
        updatedAt: Date.now(),
      };

      // Save to local storage first (for immediate UI update)
      await secureStorage.setItem(USER_PROFILE_STORAGE_KEY, profileToSave);
      setProfile(profileToSave);

      // Save to Supabase if user is authenticated
      if (user?.id) {
        try {
          await auth.updateUserProfile(user.id, profileToSave);
          logger.info('Profile saved to both local storage and Supabase');
        } catch (supabaseError) {
          logger.error('Failed to save to Supabase, but local save succeeded:', supabaseError);
          // Don't throw here - local save succeeded, Supabase can sync later
        }
      } else {
        logger.info('Profile saved to local storage only (user not authenticated)');
      }
    } catch (error) {
      logger.error('Error saving user profile:', error);
      throw new Error('Failed to save profile securely');
    }
  };

  // Update both partners in a single atomic save to avoid race conditions
  const updatePartners = async (
    p1: Partial<UserProfile['partner1']>,
    p2: Partial<UserProfile['partner2']>
  ) => {
    try {
      // Validate and sanitize names if provided
      const p1Processed = { ...p1 };
      const p2Processed = { ...p2 };

      if (p1Processed.name !== undefined) {
        const v = validateName(p1Processed.name);
        if (!v.isValid) throw new Error(v.error || 'Invalid partner 1 name');
        p1Processed.name = v.sanitizedValue!;
      }
      if (p2Processed.name !== undefined) {
        const v = validateName(p2Processed.name);
        if (!v.isValid) throw new Error(v.error || 'Invalid partner 2 name');
        p2Processed.name = v.sanitizedValue!;
      }

      const merged: UserProfile = {
        ...profile,
        partner1: { ...profile.partner1, ...p1Processed },
        partner2: { ...profile.partner2, ...p2Processed },
        updatedAt: Date.now(),
        isComplete: checkIfComplete({
          ...profile,
          partner1: { ...profile.partner1, ...p1Processed },
          partner2: { ...profile.partner2, ...p2Processed },
        }),
      };

      await saveProfile(merged);
    } catch (error) {
      logger.error('Error updating both partners:', error);
      throw error;
    }
  };

  const updatePartner1 = async (updates: Partial<UserProfile['partner1']>) => {
    try {
      // Create a copy of updates to avoid mutating the original object
      const processedUpdates = { ...updates };

      // Validate and sanitize name if it's provided
      if (processedUpdates.name !== undefined) {
        const nameValidation = validateName(processedUpdates.name);
        if (!nameValidation.isValid) {
          throw new Error(nameValidation.error || 'Invalid name');
        }
        processedUpdates.name = nameValidation.sanitizedValue!;
      }

      const newProfile = {
        ...profile,
        partner1: { ...profile.partner1, ...processedUpdates },
        isComplete: checkIfComplete({
          ...profile,
          partner1: { ...profile.partner1, ...processedUpdates },
        }),
      };

      await saveProfile(newProfile);
    } catch (error) {
      logger.error('Error updating partner 1:', error);
      throw error;
    }
  };

  const updatePartner2 = async (updates: Partial<UserProfile['partner2']>) => {
    try {
      // Create a copy of updates to avoid mutating the original object
      const processedUpdates = { ...updates };

      // Validate and sanitize name if it's provided
      if (processedUpdates.name !== undefined) {
        const nameValidation = validateName(processedUpdates.name);
        if (!nameValidation.isValid) {
          throw new Error(nameValidation.error || 'Invalid name');
        }
        processedUpdates.name = nameValidation.sanitizedValue!;
      }

      const newProfile = {
        ...profile,
        partner2: { ...profile.partner2, ...processedUpdates },
        isComplete: checkIfComplete({
          ...profile,
          partner2: { ...profile.partner2, ...processedUpdates },
        }),
      };

      await saveProfile(newProfile);
    } catch (error) {
      logger.error('Error updating partner 2:', error);
      throw error;
    }
  };

  const checkIfComplete = (profileToCheck: UserProfile): boolean => {
    return (
      profileToCheck.partner1.name.trim() !== '' &&
      profileToCheck.partner1.icon !== '' &&
      profileToCheck.partner2.name.trim() !== '' &&
      profileToCheck.partner2.icon !== ''
    );
  };

  const resetProfile = async () => {
    try {
      await secureStorage.removeItem(USER_PROFILE_STORAGE_KEY);
      setProfile(DEFAULT_PROFILE);
    } catch (error) {
      logger.error('Error resetting user profile:', error);
      throw new Error('Failed to reset profile securely');
    }
  };

  const getCoupleNames = (): string => {
    const p1 = profile.partner1?.name?.trim() || 'Partner 1';
    const p2 = profile.partner2?.name?.trim() || 'Partner 2';
    return `${p1} & ${p2}`;
  };

  const getPartnerNames = (): string[] => {
    const p1 = profile.partner1?.name?.trim() || 'Partner 1';
    const p2 = profile.partner2?.name?.trim() || 'Partner 2';
    return [p1, p2];
  };

  // Update profile photo URLs atomically
  const updateProfilePhotos = async (partner1PhotoUrl?: string | null, partner2PhotoUrl?: string | null) => {
    try {
      const merged: UserProfile = {
        ...profile,
        partner1PhotoUrl: partner1PhotoUrl !== undefined ? partner1PhotoUrl : profile.partner1PhotoUrl ?? null,
        partner2PhotoUrl: partner2PhotoUrl !== undefined ? partner2PhotoUrl : profile.partner2PhotoUrl ?? null,
        updatedAt: Date.now(),
        isComplete: checkIfComplete(profile),
      };
      await saveProfile(merged);
    } catch (error) {
      logger.error('Error updating profile photos:', error);
      throw error;
    }
  };

  // Update relationship start date
  const updateRelationshipStartDate = async (isoDate: string | null) => {
    try {
      const merged: UserProfile = {
        ...profile,
        relationshipStartDate: isoDate,
        updatedAt: Date.now(),
        isComplete: checkIfComplete(profile),
      };
      await saveProfile(merged);
    } catch (error) {
      logger.error('Error updating relationship start date:', error);
      throw error;
    }
  };

  return {
    profile,
    isLoading,
    updatePartner1,
    updatePartner2,
    updatePartners,
    updateProfilePhotos,
    updateRelationshipStartDate,
    resetProfile,
    getCoupleNames,
    getPartnerNames,
    isComplete: profile.isComplete,
  };
};
