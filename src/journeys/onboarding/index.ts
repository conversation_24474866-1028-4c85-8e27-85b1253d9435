/**
 * Onboarding Journey - Index
 * 
 * Centralized exports for all onboarding-related functionality:
 * - User authentication and profile setup
 * - Couple pairing and connection
 * - Initial app experience and feature introduction
 * 
 * <AUTHOR> Us Team
 */

// Hooks
export { useAuth } from './useAuth';
export { useUserProfile } from './useUserProfile';
export { useCouplePairing } from './useCouplePairing';
export { useFrameworkReady } from './useFrameworkReady';
export { useCoupleRealtime } from './useCoupleRealtime';

// Services
export { default as couplePairingService } from './couplePairingService';
export { default as userPreferencesService } from './userPreferencesService';

// Auth services
export * from './auth/';

// Components
export { default as AuthScreen } from './AuthScreen';

// Utils
export * from './onboardingStorage';
export * from './authPrompt';

// Types
export type {
  User,
  AuthState,
  CoupleProfile,
  OnboardingStep,
  PairingStatus
} from './onboarding.types';
