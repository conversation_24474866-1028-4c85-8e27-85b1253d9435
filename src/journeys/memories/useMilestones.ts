import { useState, useEffect } from 'react';
import { secureStorage } from '../utils/secureStorage';
import { logger } from '../../shared/utils/logger';
import { colors } from '../../utils/colors';

export interface Milestone {
  id: string;
  title: string;
  description: string;
  type: 'points' | 'streak' | 'completion' | 'special';
  threshold: number;
  points: number;
  bonusPoints: number;
  unlocked: boolean;
  unlockedAt?: number;
  icon: string;
  color: string[];
  celebrationMessage: string;
}

export interface MilestoneProgress {
  totalPoints: number;
  currentStreak: number;
  longestStreak: number;
  completedModules: number;
  completedActivities: number;
  totalModules: number;
  totalActivities: number;
}

const MILESTONES_KEY = 'milestones_data';

// Milestone definitions
const MILESTONE_DEFINITIONS: Omit<Milestone, 'id' | 'unlocked' | 'unlockedAt'>[] = [
  // Points-based milestones
  {
    title: 'First Steps',
    description: 'Complete your first activity and earn your first points',
    type: 'points',
    threshold: 25,
    points: 25,
    bonusPoints: 0,
    icon: '🎯',
    color: [colors.success, colors.secondary],
    celebrationMessage: 'Welcome to your relationship journey! Every step counts! 🎉'
  },
  {
    title: 'Bronze Level',
    description: 'Reach 100 points and unlock your first major milestone',
    type: 'points',
    threshold: 100,
    points: 100,
    bonusPoints: 25,
    icon: '🥉',
    color: [colors.warning, colors.accentPink],
    celebrationMessage: 'Bronze achieved! You\'re building something beautiful together! ✨'
  },
  {
    title: 'Silver Level',
    description: 'Reach 250 points and continue your growth journey',
    type: 'points',
    threshold: 250,
    points: 250,
    bonusPoints: 50,
    icon: '🥈',
    color: [colors.lavenderPurple, colors.textSecondary],
    celebrationMessage: 'Silver milestone! Your relationship is shining brighter! 🌟'
  },
  {
    title: 'Gold Level',
    description: 'Reach 500 points and unlock the golden achievement',
    type: 'points',
    threshold: 500,
    points: 500,
    bonusPoints: 100,
    icon: '🥇',
    color: [colors.warning, colors.accentPink],
    celebrationMessage: 'Gold unlocked! You\'re relationship champions! 🏆'
  },
  {
    title: 'Platinum Level',
    description: 'Reach 750 points and achieve platinum status',
    type: 'points',
    threshold: 750,
    points: 750,
    bonusPoints: 150,
    icon: '💎',
    color: [colors.deepCoral, colors.primaryDark],
    celebrationMessage: 'Platinum achieved! Your love is precious and rare! 💎'
  },
  {
    title: 'Diamond Level',
    description: 'Reach 1000 points and unlock diamond status',
    type: 'points',
    threshold: 1000,
    points: 1000,
    bonusPoints: 200,
    icon: '💎',
    color: [colors.tealBlue, colors.secondaryDark],
    celebrationMessage: 'Diamond milestone! Your relationship is unbreakable! 💎'
  },
  {
    title: 'Master Level',
    description: 'Reach 1500 points and become relationship masters',
    type: 'points',
    threshold: 1500,
    points: 1500,
    bonusPoints: 300,
    icon: '👑',
    color: [colors.goldenAmber, colors.warmSand],
    celebrationMessage: 'Master level achieved! You\'re relationship royalty! 👑'
  },
  {
    title: 'Legendary Status',
    description: 'Reach 2000 points and achieve legendary status',
    type: 'points',
    threshold: 2000,
    points: 2000,
    bonusPoints: 500,
    icon: '🌟',
    color: [colors.goldenAmber, colors.warmSand],
    celebrationMessage: 'Legendary status! Your love story will be told for generations! 🌟'
  },

  // Streak-based milestones
  {
    title: 'Week Warrior',
    description: 'Maintain a 7-day activity streak',
    type: 'streak',
    threshold: 7,
    points: 50,
    bonusPoints: 25,
    icon: '🔥',
    color: [colors.error, colors.redDark],
    celebrationMessage: 'Week Warrior! Your consistency is inspiring! 🔥'
  },
  {
    title: 'Fortnight Fighter',
    description: 'Maintain a 14-day activity streak',
    type: 'streak',
    threshold: 14,
    points: 100,
    bonusPoints: 50,
    icon: '🔥',
    color: [colors.error, colors.redDark],
    celebrationMessage: 'Fortnight Fighter! You\'re unstoppable! 🔥'
  },
  {
    title: 'Monthly Master',
    description: 'Maintain a 30-day activity streak',
    type: 'streak',
    threshold: 30,
    points: 200,
    bonusPoints: 100,
    icon: '🔥',
    color: [colors.error, colors.redDark],
    celebrationMessage: 'Monthly Master! You\'re relationship legends! 🔥'
  },

  // Completion-based milestones
  {
    title: 'First Week Complete',
    description: 'Complete your first full week of activities',
    type: 'completion',
    threshold: 1,
    points: 75,
    bonusPoints: 25,
    icon: '📅',
    color: [colors.success, colors.greenDark],
    celebrationMessage: 'First week complete! You\'re building momentum! 📅'
  },
  {
    title: 'Quarter Journey',
    description: 'Complete 3 weeks of activities',
    type: 'completion',
    threshold: 3,
    points: 150,
    bonusPoints: 50,
    icon: '📅',
    color: [colors.success, colors.greenDark],
    celebrationMessage: 'Quarter journey complete! You\'re making real progress! 📅'
  },
  {
    title: 'Halfway There',
    description: 'Complete 6 weeks of activities',
    type: 'completion',
    threshold: 6,
    points: 300,
    bonusPoints: 100,
    icon: '📅',
    color: [colors.success, colors.greenDark],
    celebrationMessage: 'Halfway there! Your relationship is flourishing! 📅'
  },
  {
    title: 'Almost There',
    description: 'Complete 9 weeks of activities',
    type: 'completion',
    threshold: 9,
    points: 450,
    bonusPoints: 150,
    icon: '📅',
    color: [colors.success, colors.greenDark],
    celebrationMessage: 'Almost there! You\'re relationship champions! 📅'
  },
  {
    title: 'Journey Complete',
    description: 'Complete all 12 weeks of activities',
    type: 'completion',
    threshold: 12,
    points: 600,
    bonusPoints: 300,
    icon: '🏆',
    color: [colors.warning, colors.orangeDark],
    celebrationMessage: 'Journey complete! You\'ve built something extraordinary! 🏆'
  },

  // Special milestones
  {
    title: 'Communication Champion',
    description: 'Master the soft start-up technique',
    type: 'special',
    threshold: 1,
    points: 100,
    bonusPoints: 50,
    icon: '💬',
    color: [colors.lightPurple, colors.lightPurpleDark],
    celebrationMessage: 'Communication Champion! You\'re relationship experts! 💬'
  },
  {
    title: 'Conflict Resolution Master',
    description: 'Complete all conflict resolution activities',
    type: 'special',
    threshold: 1,
    points: 150,
    bonusPoints: 75,
    icon: '⚖️',
    color: ['#8B5CF6', '#7C3AED'],
    celebrationMessage: 'Conflict Resolution Master! You handle challenges beautifully! ⚖️'
  },
  {
    title: 'Love Language Expert',
    description: 'Discover and practice all love languages',
    type: 'special',
    threshold: 1,
    points: 200,
    bonusPoints: 100,
    icon: '❤️',
    color: ['#EC4899', '#DB2777'],
    celebrationMessage: 'Love Language Expert! You speak the language of love! ❤️'
  }
];

export const useMilestones = () => {
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load milestones from storage on mount
  useEffect(() => {
    loadMilestones();
  }, []);

  const loadMilestones = async () => {
    try {
      const storedMilestones = await secureStorage.getItem<Milestone[]>(MILESTONES_KEY);
      if (storedMilestones) {
        setMilestones(storedMilestones);
      } else {
        // Initialize with default milestones
        const defaultMilestones = MILESTONE_DEFINITIONS.map((milestone, index) => ({
          ...milestone,
          id: `milestone-${index}`,
          unlocked: false
        }));
        setMilestones(defaultMilestones);
        await saveMilestones(defaultMilestones);
      }
    } catch (error) {
      logger.error('Error loading milestones:', error);
      // Fallback to default milestones
      const defaultMilestones = MILESTONE_DEFINITIONS.map((milestone, index) => ({
        ...milestone,
        id: `milestone-${index}`,
        unlocked: false
      }));
      setMilestones(defaultMilestones);
    } finally {
      setIsLoading(false);
    }
  };

  const saveMilestones = async (milestoneData: Milestone[]) => {
    try {
      await secureStorage.setItem(MILESTONES_KEY, milestoneData);
    } catch (error) {
      logger.error('Error saving milestones:', error);
      throw new Error('Failed to save milestones');
    }
  };

  // Check and unlock milestones based on progress
  const checkMilestones = async (progress: MilestoneProgress) => {
    try {
      const newMilestones = [...milestones];
      let newlyUnlocked: Milestone[] = [];

      for (const milestone of newMilestones) {
        if (!milestone.unlocked) {
          let shouldUnlock = false;

          switch (milestone.type) {
            case 'points':
              shouldUnlock = progress.totalPoints >= milestone.threshold;
              break;
            case 'streak':
              shouldUnlock = progress.currentStreak >= milestone.threshold;
              break;
            case 'completion':
              shouldUnlock = progress.completedModules >= milestone.threshold;
              break;
            case 'special':
              // Special milestones are handled separately
              shouldUnlock = false;
              break;
          }

          if (shouldUnlock) {
            milestone.unlocked = true;
            milestone.unlockedAt = Date.now();
            newlyUnlocked.push(milestone);
          }
        }
      }

      if (newlyUnlocked.length > 0) {
        setMilestones(newMilestones);
        await saveMilestones(newMilestones);
      }

      return newlyUnlocked;
    } catch (error) {
      logger.error('Error checking milestones:', error);
      return [];
    }
  };

  // Get unlocked milestones
  const getUnlockedMilestones = () => {
    return milestones.filter(milestone => milestone.unlocked);
  };

  // Get locked milestones
  const getLockedMilestones = () => {
    return milestones.filter(milestone => !milestone.unlocked);
  };

  // Get next milestone to unlock
  const getNextMilestone = (progress: MilestoneProgress) => {
    const lockedMilestones = getLockedMilestones();
    
    // Sort by threshold and return the next achievable one
    const sortedMilestones = lockedMilestones.sort((a, b) => a.threshold - b.threshold);
    
    return sortedMilestones[0] || null;
  };

  // Get progress towards next milestone
  const getProgressTowardsNext = (progress: MilestoneProgress) => {
    const nextMilestone = getNextMilestone(progress);
    if (!nextMilestone) return { percentage: 100, remaining: 0 };

    let currentValue = 0;
    switch (nextMilestone.type) {
      case 'points':
        currentValue = progress.totalPoints;
        break;
      case 'streak':
        currentValue = progress.currentStreak;
        break;
      case 'completion':
        currentValue = progress.completedModules;
        break;
      default:
        currentValue = 0;
    }

    const percentage = Math.min((currentValue / nextMilestone.threshold) * 100, 100);
    const remaining = Math.max(nextMilestone.threshold - currentValue, 0);

    return { percentage, remaining };
  };

  // Reset all milestones (for testing)
  const resetMilestones = async () => {
    try {
      const defaultMilestones = MILESTONE_DEFINITIONS.map((milestone, index) => ({
        ...milestone,
        id: `milestone-${index}`,
        unlocked: false
      }));
      setMilestones(defaultMilestones);
      await saveMilestones(defaultMilestones);
    } catch (error) {
      logger.error('Error resetting milestones:', error);
    }
  };

  return {
    milestones,
    isLoading,
    checkMilestones,
    getUnlockedMilestones,
    getLockedMilestones,
    getNextMilestone,
    getProgressTowardsNext,
    resetMilestones
  };
};
