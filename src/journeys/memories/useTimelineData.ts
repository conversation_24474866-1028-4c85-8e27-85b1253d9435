import { useState, useEffect, useMemo } from 'react';
import { useOriginStoryData } from './useOriginStoryData';
import { useWeekOneData } from '../../shared/hooks/hooks/useWeekOneData';
import { useWeekFiveData } from './useWeekFiveData';
import { usePointsSystemSupabase } from './usePointsSystemSupabase';
import { formatDate, getDaysDifference } from '../utils/dateUtils';

export interface TimelineEntry {
  id: string;
  title: string;
  date: string;
  timestamp: number;
  type: 'origin-story' | 'activity' | 'date-night' | 'reflection' | 'skill' | 'milestone';
  content: string;
  image?: string | null;
  points: number;
  action?: () => void;
  category: string;
}

export interface TimelineMilestone {
  title: string;
  date: string;
}

export const useTimelineData = () => {
  const { data: originStoryData } = useOriginStoryData();
  const { data: weekOneData } = useWeekOneData();
  const { data: weekFiveData } = useWeekFiveData();
  const { totalPoints, level } = usePointsSystemSupabase();

  const timelineEntries = useMemo(() => {
    const entries: TimelineEntry[] = [];

    // Add origin story entries with actual dates
    if (originStoryData.firstMeeting.trim() && originStoryData.firstMeetingDate) {
      entries.push({
        id: 'origin-first-meeting',
        title: 'How We Met',
        date: formatDate(originStoryData.firstMeetingDate),
        timestamp: originStoryData.firstMeetingDate,
        type: 'origin-story',
        content: originStoryData.firstMeeting,
        image: originStoryData.firstMeetingPhotos[0]?.uri || null,
        points: 50,
        category: 'Our Story'
      });
    }

    if (originStoryData.knewILovedYou.trim() && originStoryData.knewILovedYouDate) {
      entries.push({
        id: 'origin-knew-loved',
        title: 'When I Knew I Loved You',
        date: formatDate(originStoryData.knewILovedYouDate),
        timestamp: originStoryData.knewILovedYouDate,
        type: 'origin-story',
        content: originStoryData.knewILovedYou,
        image: originStoryData.knewILovedYouPhotos[0]?.uri || null,
        points: 50,
        category: 'Our Story'
      });
    }

    if (originStoryData.firstKiss.trim() && originStoryData.firstKissDate) {
      entries.push({
        id: 'origin-first-kiss',
        title: 'Our First Kiss',
        date: formatDate(originStoryData.firstKissDate),
        timestamp: originStoryData.firstKissDate,
        type: 'origin-story',
        content: originStoryData.firstKiss,
        image: originStoryData.firstKissPhotos[0]?.uri || null,
        points: 50,
        category: 'Our Story'
      });
    }

    if (originStoryData.insideJokes.trim() && originStoryData.insideJokesDate) {
      entries.push({
        id: 'origin-inside-jokes',
        title: 'Our Inside Jokes',
        date: formatDate(originStoryData.insideJokesDate),
        timestamp: originStoryData.insideJokesDate,
        type: 'origin-story',
        content: originStoryData.insideJokes,
        image: originStoryData.insideJokesPhotos[0]?.uri || null,
        points: 50,
        category: 'Our Story'
      });
    }

    if (originStoryData.mostRomantic.trim() && originStoryData.mostRomanticDate) {
      entries.push({
        id: 'origin-most-romantic',
        title: 'Most Romantic Moment',
        date: formatDate(originStoryData.mostRomanticDate),
        timestamp: originStoryData.mostRomanticDate,
        type: 'origin-story',
        content: originStoryData.mostRomantic,
        image: originStoryData.mostRomanticPhotos[0]?.uri || null,
        points: 50,
        category: 'Our Story'
      });
    }

    if (originStoryData.biggestChallenge.trim() && originStoryData.biggestChallengeDate) {
      entries.push({
        id: 'origin-biggest-challenge',
        title: 'Biggest Challenge Together',
        date: formatDate(originStoryData.biggestChallengeDate),
        timestamp: originStoryData.biggestChallengeDate,
        type: 'origin-story',
        content: originStoryData.biggestChallenge,
        image: originStoryData.biggestChallengePhotos[0]?.uri || null,
        points: 50,
        category: 'Our Story'
      });
    }

    if (originStoryData.bestMemories.trim() && originStoryData.bestMemoriesDate) {
      entries.push({
        id: 'origin-best-memories',
        title: 'Our Best Memories',
        date: formatDate(originStoryData.bestMemoriesDate),
        timestamp: originStoryData.bestMemoriesDate,
        type: 'origin-story',
        content: originStoryData.bestMemories,
        image: originStoryData.bestMemoriesPhotos[0]?.uri || null,
        points: 50,
        category: 'Our Story'
      });
    }

    // Add week one activities with actual dates
    if (weekOneData.completedSections[0] && weekOneData.sectionCompletionDates?.matchGame) {
      entries.push({
        id: 'week1-match-game',
        title: 'The Match Game',
        date: formatDate(weekOneData.sectionCompletionDates.matchGame),
        timestamp: weekOneData.sectionCompletionDates.matchGame,
        type: 'activity',
        content: 'We played the match game and discovered how well we know each other! It was so fun to guess each other\'s answers.',
        image: null,
        points: 30,
        category: 'Getting to Know You'
      });
    }

    if (weekOneData.completedSections[1] && weekOneData.sectionCompletionDates?.dateNight) {
      entries.push({
        id: 'week1-date-night',
        title: 'Date Night Plan',
        date: formatDate(weekOneData.sectionCompletionDates.dateNight),
        timestamp: weekOneData.sectionCompletionDates.dateNight,
        type: 'date-night',
        content: `We planned our game night date: ${weekOneData.dateNightPlan.when ? weekOneData.dateNightPlan.when + ' at ' : ''}${weekOneData.dateNightPlan.where || 'our favorite spot'}!`,
        image: null,
        points: 25,
        category: 'Date Planning'
      });
    }

    if (weekOneData.completedSections[2] && weekOneData.sectionCompletionDates?.chatPrompts) {
      entries.push({
        id: 'week1-chat-prompts',
        title: 'Deep Conversations',
        date: formatDate(weekOneData.sectionCompletionDates.chatPrompts),
        timestamp: weekOneData.sectionCompletionDates.chatPrompts,
        type: 'reflection',
        content: 'We shared our thoughts on happiness and what we wish we made more time for. Such meaningful conversations!',
        image: null,
        points: 20,
        category: 'Communication'
      });
    }

    if (weekOneData.completedSections[3] && weekOneData.sectionCompletionDates?.softStartup) {
      entries.push({
        id: 'week1-soft-startup',
        title: 'Soft Start-Up Practice',
        date: formatDate(weekOneData.sectionCompletionDates.softStartup),
        timestamp: weekOneData.sectionCompletionDates.softStartup,
        type: 'skill',
        content: 'We practiced rewriting statements using "I" statements. Great communication skill building!',
        image: null,
        points: 25,
        category: 'Relationship Skills'
      });
    }

    // Add week five activities
    if (weekFiveData.completedSections[0]) {
      entries.push({
        id: 'week5-dream-vacation',
        title: 'Dream Vacation Adjectives',
        date: 'This week',
        timestamp: Date.now() - (7 * 24 * 60 * 60 * 1000), // Approximate
        type: 'activity',
        content: 'We described our perfect vacation using 5 adjectives each. It was so fun to see our vacation vibes side by side!',
        image: null,
        points: 30,
        category: 'Dreams & Planning'
      });
    }

    // Add date nights from points system
    if (pointsData?.modules) {
      pointsData.modules.forEach((module, moduleIndex: any) => {
        const weekNumber = moduleIndex + 1;
        const dateNightActivity = module.activities?.find(a => a.title.includes('Date') || a.title.includes('date'));

        if (dateNightActivity?.completed && dateNightActivity.completedAt) {
          entries.push({
            id: `week${weekNumber}-date-night`,
            title: `Week ${weekNumber} Date Night`,
            date: formatDate(dateNightActivity.completedAt),
            timestamp: dateNightActivity.completedAt,
            type: 'date-night',
            content: `We completed our date night activity for week ${weekNumber}!`,
            image: null,
            points: dateNightActivity.points || 25,
            category: 'Date Planning'
          });
        }
      });
    }

    // Sort entries by timestamp (newest first)
    return entries.sort((a, b) => b.timestamp - a.timestamp);
  }, [originStoryData, weekOneData, weekFiveData, pointsData]);

  const getTimelineStats = () => {
    const totalEntries = timelineEntries.length;
    const totalPoints = timelineEntries.reduce((sum: any, entry: any) => sum + entry.points, 0);
    const originStoryCount = timelineEntries.filter(entry => entry.type === 'origin-story').length;
    const activityCount = timelineEntries.filter(entry => entry.type === 'activity').length;
    const dateNightCount = timelineEntries.filter(entry => entry.type === 'date-night').length;
    
    // Calculate weeks completed from points system
    const weeksCompleted = pointsData?.modules?.filter(m => m.completed).length || 0;
    
    // Generate milestones based on timeline entries
    const milestones: TimelineMilestone[] = [
      { title: 'Started Journey Together', date: 'The beginning of us' },
      ...(originStoryCount > 0 ? [{ title: 'Shared Origin Story', date: 'Our story begins' }] : []),
      ...(weeksCompleted > 0 ? [{ title: `Completed ${weeksCompleted} Week${weeksCompleted > 1 ? 's' : ''}`, date: 'Progress made!' }] : []),
      ...(totalPoints >= 100 ? [{ title: 'Earned 100+ Points', date: 'Milestone achieved!' }] : []),
      ...(totalPoints >= 500 ? [{ title: 'Earned 500+ Points', date: 'Amazing progress!' }] : []),
    ];

    return {
      totalEntries,
      totalPoints,
      originStoryCount,
      activityCount,
      dateNightCount,
      weeksCompleted,
      milestones
    };
  };

  return {
    timelineEntries,
    getTimelineStats,
    isLoading: false
  };
};
