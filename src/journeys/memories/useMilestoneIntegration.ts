import { useCallback } from 'react';
import { useRelationshipMilestones } from './useRelationshipMilestones';
import { useMemoryFavorites } from '../useContentFavorites';
import { usePointsSystemSupabase } from './usePointsSystemSupabase';
import { useUserEvents } from './useUserEvents';
import { logger } from '../../shared/utils/logger';

/**
 * Hook that integrates milestones with existing app systems
 * - Favorites system for milestone memories
 * - Points system for milestone completion rewards
 * - User events for tracking milestone activities
 * - Timeline integration for milestone display
 */
export const useMilestoneIntegration = () => {
  const milestones = useRelationshipMilestones();
  const memoryFavorites = useMemoryFavorites();
  const { addPoints, unlockAchievement } = usePointsSystemSupabase();
  const { logEvent } = useUserEvents();

  /**
   * Handle milestone completion with full system integration
   */
  const completeMilestone = useCallback(async (
    milestoneId: string,
    milestoneData: Record<string, any>,
    template: any
  ) => {
    try {
      // 1. Save milestone data
      await milestones.updateMilestone(milestoneId, milestoneData);

      // 2. Award points for milestone completion
      const pointsAwarded = calculateMilestonePoints(template);
      if (pointsAwarded > 0) {
        await addPoints(pointsAwarded, `Completed milestone: ${template.title}`);
        logger.info(`Awarded ${pointsAwarded} points for milestone completion`);
      }

      // 3. Check for achievement unlocks
      await checkMilestoneAchievements(template, milestones.completedMilestones + 1);

      // 4. Log user event
      await logEvent('milestone_completed', {
        milestone_key: template.milestone_key,
        milestone_category: template.category,
        points_awarded: pointsAwarded,
        completion_data: milestoneData,
      });

      // 5. Auto-favorite significant milestones
      if (template.is_core_milestone && milestoneData.photos?.length > 0) {
        try {
          await memoryFavorites.toggleFavorite(milestoneId, {
            milestone_title: template.title,
            milestone_category: template.category,
            auto_favorited: true,
          });
          logger.info(`Auto-favorited core milestone: ${template.title}`);
        } catch (favoriteError) {
          logger.warn('Failed to auto-favorite milestone:', favoriteError);
          // Don't fail the whole operation if favoriting fails
        }
      }

      logger.info(`Successfully completed milestone integration for: ${template.title}`);
      return true;
    } catch (error) {
      logger.error('Error in milestone completion integration:', error);
      throw error;
    }
  }, [milestones, memoryFavorites, addPoints, unlockAchievement, logEvent]);

  /**
   * Favorite/unfavorite a milestone memory
   */
  const toggleMilestoneFavorite = useCallback(async (
    milestoneId: string,
    template: any,
    isFavorited: boolean
  ) => {
    try {
      await memoryFavorites.toggleFavorite(milestoneId, {
        milestone_title: template.title,
        milestone_category: template.category,
        milestone_key: template.milestone_key,
      });

      // Log the favorite action
      await logEvent(isFavorited ? 'milestone_favorited' : 'milestone_unfavorited', {
        milestone_key: template.milestone_key,
        milestone_category: template.category,
      });

      logger.info(`${isFavorited ? 'Favorited' : 'Unfavorited'} milestone: ${template.title}`);
    } catch (error) {
      logger.error('Error toggling milestone favorite:', error);
      throw error;
    }
  }, [memoryFavorites, logEvent]);

  /**
   * Get milestone completion statistics for achievements
   */
  const getMilestoneStats = useCallback(() => {
    const stats = {
      totalMilestones: milestones.totalMilestones,
      completedMilestones: milestones.completedMilestones,
      overallProgress: milestones.overallProgress,
      categoryProgress: milestones.progress,
      milestonesByCategory: milestones.milestonesByCategory,
    };

    return stats;
  }, [milestones]);

  /**
   * Initialize milestones for a new couple with system integration
   */
  const initializeMilestonesWithIntegration = useCallback(async () => {
    try {
      // Initialize milestone templates
      await milestones.initializeMilestones();

      // Log initialization event
      await logEvent('milestones_initialized', {
        total_milestones: milestones.totalMilestones,
      });

      // Award points for setting up milestones
      await addPoints(50, 'Initialized relationship milestones');

      logger.info('Successfully initialized milestones with full system integration');
      return true;
    } catch (error) {
      logger.error('Error initializing milestones with integration:', error);
      throw error;
    }
  }, [milestones, logEvent, addPoints]);

  return {
    // Core milestone functionality
    ...milestones,
    
    // Integrated actions
    completeMilestone,
    toggleMilestoneFavorite,
    initializeMilestonesWithIntegration,
    
    // Statistics and data
    getMilestoneStats,
    
    // Favorite status for milestones
    isMilestoneFavorited: memoryFavorites.isFavorited,
    milestoneLoadingStates: memoryFavorites.loadingStates,
  };
};

/**
 * Calculate points awarded for completing a milestone
 */
function calculateMilestonePoints(template: any): number {
  const basePoints = 25;
  
  // Core milestones are worth more points
  if (template.is_core_milestone) {
    return basePoints * 2; // 50 points
  }
  
  // Category-based point multipliers
  const categoryMultipliers: Record<string, number> = {
    foundation: 1.5,     // 37.5 points
    getting_to_know: 1.2, // 30 points
    commitment: 1.8,     // 45 points
    engagement: 2.0,     // 50 points
    modern: 1.0,         // 25 points
  };
  
  const multiplier = categoryMultipliers[template.category] || 1.0;
  return Math.round(basePoints * multiplier);
}

/**
 * Check and unlock achievements based on milestone progress
 */
async function checkMilestoneAchievements(template: any, completedCount: number) {
  const achievements = [
    {
      id: 'first_milestone',
      condition: () => completedCount >= 1,
      title: 'First Steps',
      description: 'Completed your first relationship milestone',
    },
    {
      id: 'foundation_complete',
      condition: () => template.category === 'foundation' && completedCount >= 5,
      title: 'Strong Foundation',
      description: 'Completed all foundation milestones',
    },
    {
      id: 'milestone_master',
      condition: () => completedCount >= 10,
      title: 'Milestone Master',
      description: 'Completed 10 relationship milestones',
    },
    {
      id: 'relationship_historian',
      condition: () => completedCount >= 21,
      title: 'Relationship Historian',
      description: 'Completed all relationship milestones',
    },
  ];

  // This would integrate with your existing achievement system
  // For now, just log potential achievements
  achievements.forEach(achievement => {
    if (achievement.condition()) {
      logger.info(`Achievement unlocked: ${achievement.title}`);
      // await unlockAchievement(achievement.id);
    }
  });
}
