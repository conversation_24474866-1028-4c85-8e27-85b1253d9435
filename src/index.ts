/**
 * Everlasting Us - Main Source Index
 * 
 * Centralized exports organized by user journeys and shared functionality.
 * This structure reflects the ideal user-journey architecture.
 * 
 * <AUTHOR> Us Team
 */

// === USER JOURNEYS ===

// Onboarding Journey - Getting started, authentication, couple pairing
export * from './journeys/onboarding';

// Daily Journey - Daily questions, streaks, daily engagement
export * from './journeys/daily';

// Activities Journey - Date nights, games, activities, meals
export * from './journeys/activities';

// Memories Journey - Origin story, timeline, photos, milestones
export * from './journeys/memories';

// Planning Journey - Goal setting, preferences, scheduling
export * from './journeys/planning';

// Progress Journey - Points, achievements, analytics, tracking
export * from './journeys/progress';

// === SHARED FUNCTIONALITY ===

// Shared Components - Reusable UI components and design system
export * from './shared/components';

// Shared Hooks - Common React hooks and utilities
export * from './shared/hooks';

// Shared Services - Business logic and API integrations
export * from './shared/services';

// Shared Types - TypeScript definitions and interfaces
export * from './shared/types';

// Shared Utils - Utility functions and helpers
export * from './shared/utils';

// === ASSETS ===

// Centralized asset management
export * from './assets';

// === JOURNEY ALIASES ===
// Convenient aliases for common imports

// Authentication & User Management
export {
  useAuth,
  useUserProfile,
  useCouplePairing
} from './journeys/onboarding';

// Daily Engagement
export {
  useDailyQuestions,
  useStreakData
} from './journeys/daily';

// Activities & Games
export {
  useDateNightIdeasSupabase,
  useMatchGame,
  useFavorites
} from './journeys/activities';

// Memory & Timeline
export {
  useOriginStoryData,
  useTimeline,
  useMilestones
} from './journeys/memories';

// Progress & Analytics
export {
  usePointsSystemSupabase,
  useUserEvents,
  useEngagementSystem
} from './journeys/progress';
