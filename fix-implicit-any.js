#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix implicit any type errors in a file
function fixImplicitAny(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Common patterns to fix
    const patterns = [
      // Fix .map((item, index) => to .map((item: any, index: number) =>
      { from: /\.map\(\(([^,]+),\s*index\)\s*=>/g, to: '.map(($1: any, index: number) =>' },
      // Fix .forEach((item, index) => to .forEach((item: any, index: number) =>
      { from: /\.forEach\(\(([^,]+),\s*index\)\s*=>/g, to: '.forEach(($1: any, index: number) =>' },
      // Fix .filter((item, index) => to .filter((item: any, index: number) =>
      { from: /\.filter\(\(([^,]+),\s*index\)\s*=>/g, to: '.filter(($1: any, index: number) =>' },
      // Fix .reduce((prev, curr, index) => to .reduce((prev: any, curr: any, index: number) =>
      { from: /\.reduce\(\(([^,]+),\s*([^,]+),\s*index\)\s*=>/g, to: '.reduce(($1: any, $2: any, index: number) =>' },
      // Fix .reduce((prev, curr) => to .reduce((prev: any, curr: any) =>
      { from: /\.reduce\(\(([^,]+),\s*([^)]+)\)\s*=>/g, to: '.reduce(($1: any, $2: any) =>' },
      // Fix .map((item) => to .map((item: any) =>
      { from: /\.map\(\(([^)]+)\)\s*=>/g, to: '.map(($1: any) =>' },
      // Fix .forEach((item) => to .forEach((item: any) =>
      { from: /\.forEach\(\(([^)]+)\)\s*=>/g, to: '.forEach(($1: any) =>' },
      // Fix .filter((item) => to .filter((item: any) =>
      { from: /\.filter\(\(([^)]+)\)\s*=>/g, to: '.filter(($1: any) =>' },
      // Fix single parameter functions
      { from: /\(([a-zA-Z_][a-zA-Z0-9_]*)\s*\)\s*=>/g, to: '($1: any) =>' }
    ];
    
    patterns.forEach(pattern => {
      const beforeContent = content;
      content = content.replace(pattern.from, pattern.to);
      if (content !== beforeContent) {
        modified = true;
        console.log(`${filePath}: Fixed implicit any types`);
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed implicit any types in: ${filePath}`);
    }
    
    return modified;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Get files with TS7006 errors
function getFilesWithTS7006Errors() {
  const { execSync } = require('child_process');
  try {
    const result = execSync(`npx tsc --noEmit 2>&1 | grep "TS7006" | grep -o "^[^(]*" | sort | uniq`, { encoding: 'utf8' });
    return result.trim().split('\n').filter(file => file.length > 0);
  } catch (error) {
    console.error('Error finding files:', error.message);
    return [];
  }
}

// Main execution
console.log('🔍 Finding files with TS7006 errors...');
const filesWithErrors = getFilesWithTS7006Errors();

console.log(`📁 Found ${filesWithErrors.length} files with TS7006 errors`);

let totalFixed = 0;

filesWithErrors.forEach(file => {
  if (fs.existsSync(file)) {
    if (fixImplicitAny(file)) {
      totalFixed++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

console.log(`\n🎉 Fixed implicit any types in ${totalFixed} files!`);
